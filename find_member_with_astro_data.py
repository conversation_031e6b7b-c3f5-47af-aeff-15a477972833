"""
Find a member with astro data.
"""

import sys
import pymongo
from bson import ObjectId

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Get all astro data
astro_data_list = list(db["user_member_astro_profile_data"].find())
print(f"Number of astro data documents: {len(astro_data_list)}")

for astro_data in astro_data_list:
    member_id = astro_data.get('member_profile_id')
    print(f"\nAstro data ID: {astro_data['_id']}")
    print(f"Member profile ID: {member_id}")
    
    # Find the corresponding member profile
    if member_id:
        member = db["member_profile"].find_one({"_id": member_id})
        if member:
            print(f"Found member profile with ID: {member['_id']}")
            print(f"Member profile user_profile_id: {member.get('user_profile_id')}")
            print(f"Member profile member_profile_id: {member.get('member_profile_id')}")
            
            # Print D1 chart information
            d1_chart = astro_data.get('chart_data', {}).get('D1', {})
            if d1_chart:
                print("D1 chart data found")
                
                # Print houses information
                houses = d1_chart.get('houses', [])
                print("Houses information:")
                for house in houses[:3]:  # Print first 3 houses only
                    house_number = house.get('house_number')
                    house_name = house.get('house_name')
                    planets = house.get('planets', [])
                    print(f"House {house_number} ({house_name}): Planets = {', '.join(planets)}")
                
                # Print dasha information
                dasha_data = astro_data.get('chart_data', {}).get('dashas', {})
                print("Dasha information:")
                for period_type, periods in list(dasha_data.items())[:2]:  # Print first 2 dasha types only
                    print(f"{period_type}:")
                    if isinstance(periods, list):
                        for period in periods[:2]:  # Print first 2 periods only
                            print(f"  {period.get('period_name')}: {period.get('start_date')} to {period.get('end_date')}")
                    else:
                        print(f"  {periods}")
            else:
                print("No D1 chart data found")
        else:
            print(f"No member profile found with ID: {member_id}")
    else:
        print("No member_profile_id found in astro data")
