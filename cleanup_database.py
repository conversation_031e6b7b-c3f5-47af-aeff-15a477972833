#!/usr/bin/env python3
"""
Script to clean up the database by removing the sequences collection
"""

from pymongo import MongoClient
import json
from bson import json_util
from bson.objectid import ObjectId

# Connect to MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client.fortune_lens

print('=== CLEANING UP DATABASE ===')

# Check if sequences collection exists
if 'sequences' in db.list_collection_names():
    # Get current sequence values
    sequences = list(db.sequences.find())
    print("Current sequences:")
    for seq in sequences:
        print(f"  - {seq['_id']}: {seq.get('seq')}")
    
    # Drop sequences collection
    db.sequences.drop()
    print("Sequences collection dropped.")
else:
    print("Sequences collection does not exist.")

print("\nDatabase cleanup completed.")
