{"info": {"name": "Fortune Lens API", "description": "API collection for Fortune Lens application", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Authentication", "description": "Authentication endpoints for user registration and login", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/register", "host": ["{{base_url}}"], "path": ["auth", "register"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword\",\n  \"name\": \"<PERSON>\",\n  \"mobile\": \"1234567890\"\n}"}, "description": "Register a new user. If OTP verification is enabled, this will send an OTP to the user's email."}, "response": []}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/verify-otp", "host": ["{{base_url}}"], "path": ["auth", "verify-otp"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"otp\": \"123456\"\n}"}, "description": "Verify OTP sent during registration"}, "response": []}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/auth/login", "host": ["{{base_url}}"], "path": ["auth", "login"]}, "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"securepassword\"\n}"}, "description": "Login and get access token"}, "response": []}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{refresh_token}}"}], "url": {"raw": "{{base_url}}/auth/refresh", "host": ["{{base_url}}"], "path": ["auth", "refresh"]}, "description": "Refresh access token using refresh token"}, "response": []}, {"name": "Get Current User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/auth/me", "host": ["{{base_url}}"], "path": ["auth", "me"]}, "description": "Get current user information"}, "response": []}]}, {"name": "User Management", "description": "User management endpoints", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users", "host": ["{{base_url}}"], "path": ["users"]}, "description": "Get list of all users (admin only)"}, "response": []}, {"name": "Get User by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}, "description": "Get user by ID"}, "response": []}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Name\",\n  \"mobile\": \"9876543210\"\n}"}, "description": "Update user information"}, "response": []}, {"name": "Delete User", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/users/{{user_id}}", "host": ["{{base_url}}"], "path": ["users", "{{user_id}}"]}, "description": "Delete user"}, "response": []}]}, {"name": "Member Profiles", "description": "Member profile management endpoints", "item": [{"name": "Create Member Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles", "host": ["{{base_url}}"], "path": ["member-profiles"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"relation\": \"spouse\",\n  \"birth_date\": \"1990-01-01\",\n  \"birth_time\": \"10:30:00\",\n  \"birth_place\": \"Chennai\",\n  \"state\": \"Tamil Nadu\",\n  \"country\": \"India\",\n  \"latitude\": 13.0827,\n  \"longitude\": 80.2707,\n  \"gender\": \"female\"\n}"}, "description": "Create a new member profile"}, "response": []}, {"name": "Create Member Profile with Charts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/with-charts", "host": ["{{base_url}}"], "path": ["member-profiles", "with-charts"]}, "body": {"mode": "raw", "raw": "{\n  \"member_name\": \"<PERSON>\",\n  \"member_gender\": \"female\",\n  \"member_relation\": \"spouse\",\n  \"user_birthdate\": \"01-01-1990\",\n  \"user_birthtime\": \"10:30:00 AM\",\n  \"user_birthplace\": \"Chennai\",\n  \"user_state\": \"Tamil Nadu\",\n  \"user_country\": \"India\"\n}"}, "description": "Create a member profile with automatic chart generation"}, "response": []}, {"name": "Get All Member Profiles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles", "host": ["{{base_url}}"], "path": ["member-profiles"]}, "description": "Get all member profiles for the current user"}, "response": []}, {"name": "Get Member Profile by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/{{profile_id}}", "host": ["{{base_url}}"], "path": ["member-profiles", "{{profile_id}}"]}, "description": "Get member profile by ID"}, "response": []}, {"name": "Get Self Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/self", "host": ["{{base_url}}"], "path": ["member-profiles", "self"]}, "description": "Get or create user's own profile as a member profile"}, "response": []}, {"name": "Get Member Profile with Charts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/{{profile_id}}/charts", "host": ["{{base_url}}"], "path": ["member-profiles", "{{profile_id}}", "charts"]}, "description": "Get member profile with all chart data"}, "response": []}, {"name": "Update Member Profile", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/{{profile_id}}", "host": ["{{base_url}}"], "path": ["member-profiles", "{{profile_id}}"]}, "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Name\",\n  \"birth_place\": \"Updated Place\",\n  \"latitude\": 12.9716,\n  \"longitude\": 77.5946\n}"}, "description": "Update member profile"}, "response": []}, {"name": "Delete Member Profile", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/member-profiles/{{profile_id}}", "host": ["{{base_url}}"], "path": ["member-profiles", "{{profile_id}}"]}, "description": "Delete member profile"}, "response": []}]}, {"name": "Charts", "description": "Chart generation endpoints", "item": [{"name": "Get Available Chart Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/charts/types", "host": ["{{base_url}}"], "path": ["charts", "types"]}, "description": "Get available chart types"}, "response": []}, {"name": "Generate All Charts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/charts/generate", "host": ["{{base_url}}"], "path": ["charts", "generate"]}, "body": {"mode": "raw", "raw": "{\n  \"user_birthdate\": \"01-01-1990\",\n  \"user_birthtime\": \"10:30:00 AM\",\n  \"user_birthplace\": \"Chennai\",\n  \"user_state\": \"Tamil Nadu\",\n  \"user_country\": \"India\"\n}"}, "description": "Generate all 23 astrological divisional charts"}, "response": []}, {"name": "Generate Divisional Chart", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/charts/divisional", "host": ["{{base_url}}"], "path": ["charts", "divisional"]}, "body": {"mode": "raw", "raw": "{\n  \"user_birthdate\": \"01-01-1990\",\n  \"user_birthtime\": \"10:30:00 AM\",\n  \"user_birthplace\": \"Chennai\",\n  \"user_state\": \"Tamil Nadu\",\n  \"user_country\": \"India\",\n  \"chart_type\": 9\n}"}, "description": "Generate a specific divisional chart (e.g., D9 for Navamsa)"}, "response": []}]}, {"name": "Marriage Matching", "description": "Marriage compatibility analysis endpoints", "item": [{"name": "Analyze Marriage Compatibility", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-matching", "host": ["{{base_url}}"], "path": ["marriage-matching"]}, "body": {"mode": "raw", "raw": "{\n  \"bride_id\": \"{{bride_profile_id}}\",\n  \"groom_id\": \"{{groom_profile_id}}\"\n}"}, "description": "Analyze marriage compatibility between two members"}, "response": []}, {"name": "Get Marriage Compatibility", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-matching/{{bride_profile_id}}/{{groom_profile_id}}", "host": ["{{base_url}}"], "path": ["marriage-matching", "{{bride_profile_id}}", "{{groom_profile_id}}"]}, "description": "Get marriage compatibility analysis between two members"}, "response": []}, {"name": "Analyze Rasi Marriage Compatibility", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-matching/rasi", "host": ["{{base_url}}"], "path": ["marriage-matching", "rasi"]}, "body": {"mode": "raw", "raw": "{\n  \"bride_id\": \"{{bride_profile_id}}\",\n  \"groom_id\": \"{{groom_profile_id}}\",\n  \"marriage_date\": \"2023-12-01\"\n}"}, "description": "Analyze Rasi-based marriage compatibility between two members"}, "response": []}, {"name": "Get Rasi Marriage Compatibility", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-matching/rasi/{{bride_profile_id}}/{{groom_profile_id}}?marriage_date=2023-12-01", "host": ["{{base_url}}"], "path": ["marriage-matching", "rasi", "{{bride_profile_id}}", "{{groom_profile_id}}"], "query": [{"key": "marriage_date", "value": "2023-12-01"}]}, "description": "Get Rasi-based marriage compatibility analysis between two members"}, "response": []}]}, {"name": "Marriage Date Prediction", "description": "Marriage date prediction endpoints", "item": [{"name": "Predict Marriage Date", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-date-prediction", "host": ["{{base_url}}"], "path": ["marriage-date-prediction"]}, "body": {"mode": "raw", "raw": "{\n  \"member_id\": \"{{member_profile_id}}\",\n  \"start_age\": 21,\n  \"end_age\": 40,\n  \"print_output\": false\n}"}, "description": "Predict potential marriage dates for a member"}, "response": []}, {"name": "Get Marriage Date Prediction", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-date-prediction/{{member_profile_id}}?start_age=21&end_age=40&print_output=false", "host": ["{{base_url}}"], "path": ["marriage-date-prediction", "{{member_profile_id}}"], "query": [{"key": "start_age", "value": "21"}, {"key": "end_age", "value": "40"}, {"key": "print_output", "value": "false"}]}, "description": "Get marriage date prediction for a member"}, "response": []}, {"name": "Get Marriage Date Prediction by User", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/marriage-date-prediction/user/{{user_profile_id}}/{{member_profile_id}}?start_age=21&end_age=40&print_output=false", "host": ["{{base_url}}"], "path": ["marriage-date-prediction", "user", "{{user_profile_id}}", "{{member_profile_id}}"], "query": [{"key": "start_age", "value": "21"}, {"key": "end_age", "value": "40"}, {"key": "print_output", "value": "false"}]}, "description": "Get marriage date prediction for a member of a user"}, "response": []}]}, {"name": "Daily Panchanga", "description": "Tamil Panchanga (calendar) endpoints", "item": [{"name": "Get Daily Panchanga", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/daily-panchanga/daily?date=2023-01-01&lat=13.0878&lon=80.2785&tz=5.5", "host": ["{{base_url}}"], "path": ["daily-panchanga", "daily"], "query": [{"key": "date", "value": "2023-01-01"}, {"key": "lat", "value": "13.0878"}, {"key": "lon", "value": "80.2785"}, {"key": "tz", "value": "5.5"}]}, "description": "Get daily panchanga information for a specific date"}, "response": []}, {"name": "Get Monthly Panchanga", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/daily-panchanga/monthly?year=2023&month=1&lat=13.0878&lon=80.2785&tz=5.5", "host": ["{{base_url}}"], "path": ["daily-panchanga", "monthly"], "query": [{"key": "year", "value": "2023"}, {"key": "month", "value": "1"}, {"key": "lat", "value": "13.0878"}, {"key": "lon", "value": "80.2785"}, {"key": "tz", "value": "5.5"}]}, "description": "Get monthly panchanga information"}, "response": []}, {"name": "Get Festival Dates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/daily-panchanga/festivals?year=2023&lat=13.0878&lon=80.2785&tz=5.5", "host": ["{{base_url}}"], "path": ["daily-panchanga", "festivals"], "query": [{"key": "year", "value": "2023"}, {"key": "lat", "value": "13.0878"}, {"key": "lon", "value": "80.2785"}, {"key": "tz", "value": "5.5"}]}, "description": "Get festival dates for a specific year"}, "response": []}, {"name": "Get Tamil Date", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/daily-panchanga/tamil-date?date=2023-01-01&lat=13.0878&lon=80.2785&tz=5.5", "host": ["{{base_url}}"], "path": ["daily-panchanga", "tamil-date"], "query": [{"key": "date", "value": "2023-01-01"}, {"key": "lat", "value": "13.0878"}, {"key": "lon", "value": "80.2785"}, {"key": "tz", "value": "5.5"}]}, "description": "Get Tamil date information for a specific date"}, "response": []}]}, {"name": "Career Prediction", "description": "Career prediction endpoints", "item": [{"name": "Predict Medical Profession", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/career-prediction/medical-profession", "host": ["{{base_url}}"], "path": ["career-prediction", "medical-profession"]}, "body": {"mode": "raw", "raw": "{\n  \"user_profile_id\": \"{{user_profile_id}}\",\n  \"member_profile_id\": \"{{member_profile_id}}\",\n  \"print_output\": false,\n  \"use_excel_data\": false\n}"}, "description": "Predict if a person has potential for a medical profession based on astrological factors"}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Auto-extract tokens from login response", "if (pm.response.code === 200 && pm.info.requestName === \"Login\") {", "    var jsonData = pm.response.json();", "    if (jsonData.access_token) {", "        pm.environment.set(\"access_token\", jsonData.access_token);", "    }", "    if (jsonData.refresh_token) {", "        pm.environment.set(\"refresh_token\", jsonData.refresh_token);", "    }", "}"]}}], "variable": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "string"}]}