# Web Framework
Flask==3.1.0
flask-cors==5.0.1
Werkzeug==3.1.3
Jinja2==3.1.6
itsdangerous==2.2.0
blinker==1.9.0
MarkupSafe==3.0.2

# Database
pymongo==4.11.3
Flask-PyMongo==3.0.1
dnspython==2.7.0

# Data Processing
pandas==2.2.3
numpy==2.2.4
openpyxl==3.1.5
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# Forms and Validation
marshmallow==3.26.1

# Astronomy/Astrology
swisseph==0.0.0.dev1
pyswisseph==2.10.3.2

# Geolocation
geopy==2.4.1
geographiclib==2.0
timezonefinder==6.5.9

# MongoDB Integration for Stock Screener
# These packages will be needed when implementing MongoDB for the stock screener
SQLAlchemy==2.0.27
Flask-SQLAlchemy==3.1.1

# Utilities
et_xmlfile==1.0.0
six==1.17.0
click==8.1.8

# Environment Variables
python-dotenv==1.0.0

# HTTP Requests
requests==2.32.3