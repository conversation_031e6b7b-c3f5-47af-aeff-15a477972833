#!/usr/bin/env python3
"""
Script to insert test data into the users_master_data collection
"""

import os
import sys
from pymongo import MongoClient
from datetime import datetime

# Add the parent directory to the path so we can import from the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the app configuration
from fl_user_profile.app.config import Config

def insert_test_data():
    """Insert test data into the users_master_data collection"""
    try:
        # Connect to MongoDB
        client = MongoClient(Config.MONGO_URI)
        db = client[Config.MONGO_DBNAME]
        collection = db[Config.MONGO_USER_COLLECTION]
        
        # Create a test document
        test_document = {
            "user_id": "test_user_001",
            "name": "Test User",
            "email": "<EMAIL>",
            "birth_date": datetime(1990, 1, 1),
            "birth_time": "10:30:00",
            "birth_place": "New York, USA",
            "latitude": 40.7128,
            "longitude": -74.0060,
            "created_at": datetime.utcnow()
        }
        
        # Insert the document
        result = collection.insert_one(test_document)
        
        print(f"Inserted document with ID: {result.inserted_id}")
        
        # Verify the document was inserted
        count = collection.count_documents({})
        print(f"Collection now has {count} documents")
        
        return True
    
    except Exception as e:
        print(f"Error inserting test data: {str(e)}")
        return False

if __name__ == "__main__":
    insert_test_data()
