import os

class Config:
    # Flask settings
    DEBUG = True
    SECRET_KEY = os.environ.get('SECRET_KEY', '7f9e6d5f74b8c91e42ef1c9a2b29c1f9d7ae3d4fa3c2b1e84e8f0b2d4e7a5b3c')

    # MongoDB settings
    MONGO_URI = os.environ.get('MONGO_URI', 'mongodb://localhost:27017/fortune_lens')
    MONGO_DBNAME = os.environ.get('MONGO_DBNAME', 'fortune_lens')

    # Collections
    MONGO_USER_COLLECTION = 'users_master_data'

    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'DEBUG')
