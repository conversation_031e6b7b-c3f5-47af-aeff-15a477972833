from flask import Blueprint, jsonify, request, current_app

from ..requests.dataset_requests import CreateDbReq, GetCollDb, AddTableReq, GetTables
from ..services.dataset_service import test_and_create_db, get_collections_for_code, add_table, check_db_presence, \
    get_tables_for_code

dataset_api = Blueprint('dataset_api', __name__)


@dataset_api.route('/dataset/create_db', methods=['POST'])
def create_db():
    current_app.logger.debug("request json: " + str(request.get_json()))

    # 1. Get the request json
    req_json = request.get_json()
    print(req_json)
    # 2. Validate the req json using requts schema
    errors = CreateDbReq().validate(req_json)
    print(errors)
    if errors:
        return jsonify({'result': False, 'errors': errors}), 400

    # 3. Calling the required service.
    res = test_and_create_db(**req_json)
    print(res)
    if res['result']:
        return jsonify(res), 201

    return jsonify(res), 400


@dataset_api.route('/dataset/get_collections', methods=['POST'])
def get_collections_controller():
    try:
        req_json = request.get_json()
        errors = GetCollDb().validate(req_json)
        if errors:
            return jsonify({'result': False, 'errors': errors}), 400

        collections = get_collections_for_code(req_json)  # pass the whole dictionary as an argument

        return jsonify({'result': True, 'data': {'collections': collections}}), 200

    except Exception as e:
        return jsonify({'result': False, 'errors': str(e)}), 500


@dataset_api.route('/dataset/add_table', methods=['POST'])
def add_table_controller():
    try:
        req_json = request.get_json()
        errors = AddTableReq().validate(req_json)
        if errors:
            return jsonify({'result': False, 'errors': errors}), 400

        res = add_table(**req_json)
        if res['result']:
            return jsonify(res), 201

        return jsonify(res), 400

    except Exception as e:
        return jsonify({'result': False, 'errors': str(e)}), 500


@dataset_api.route('/dataset/check_db_presence', methods=['POST'])
def check_db():
    result = check_db_presence()
    return jsonify(result)


@dataset_api.route('/dataset/get_tables', methods=['POST'])
def get_tables_controller():
    try:
        req_json = request.get_json()
        errors = GetTables().validate(req_json)
        if errors:
            return jsonify({'result': False, 'errors': errors}), 400

        db_type, tables = get_tables_for_code(**req_json)

        return jsonify({'result': True, 'data': {'db_type': db_type, 'tables': tables}}), 200

    except Exception as e:
        return jsonify({'result': False, 'errors': str(e)}), 500



