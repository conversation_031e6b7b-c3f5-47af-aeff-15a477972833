from flask import Blueprint, jsonify, request
from ..requests import *
from ..services.report_service import generate_report_service

report_api = Blueprint('report_api', __name__)


@report_api.route('/report/generate_report', methods=['POST'])
def generate_report_controller():
    try:
        req_data = request.get_json()

        errors = ReportRequestValidator().validate(data=req_data)
        if errors:
            return jsonify({'result': False, 'errors': errors}), 400

        report_data = generate_report_service(req_data)

        return jsonify(report_data), 200

    except Exception as e:
        return jsonify({'result': False, 'errors': str(e)}), 500
