from flask import Blueprint, jsonify, request
from ..requests import *
from ..services.generating_report_service import *

generate_report_api = Blueprint('generate_report_api', __name__)


@generate_report_api.route('/user/generate_report', methods=['POST'])
def generate_report_controller():
    try:
        req_data = request.get_json()
        print(req_data)
        errors = GenerateReportRequestValidator().validate(data=req_data)
        print(errors)
        if errors:
            return jsonify({'result': False, 'errors': errors}), 400

        report_data = generate_report_service(req_data)

        return jsonify(report_data), 200

    except Exception as e:
        return jsonify({'result': False, 'errors': str(e)}), 500
