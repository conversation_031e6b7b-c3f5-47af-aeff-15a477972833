from geopy.geocoders import Nominatim
from timezonefinder import TimezoneFinder
from datetime import datetime
import pytz


def get_location_info(location_name):
    geolocator = Nominatim(user_agent="my_custom_user_agent")  # Set a custom user-agent
    location = geolocator.geocode(location_name)

    if location:
        latitude = round(location.latitude, 4)
        longitude = round(location.longitude, 4)

        tf = TimezoneFinder()
        timezone_str = tf.timezone_at(lng=longitude, lat=latitude)

        if timezone_str:
            timezone = pytz.timezone(timezone_str)
            current_time = datetime.now(timezone).strftime('%Y-%m-%d %H:%M:%S')
            utc_offset = timezone.utcoffset(datetime.now()).total_seconds() / 3600
        else:
            timezone_str = 'Unknown'
            current_time = 'Unknown'
            utc_offset = 'Unknown'

        return {
                    'Location_name': location_name,
                    'latitude': latitude,
                    'longitude': longitude,
                    'timezone': timezone_str,
                    'Time_zone_offset': utc_offset,
                    'current_time': current_time
                }
    else:
        return None

