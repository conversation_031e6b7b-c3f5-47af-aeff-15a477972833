from flask import current_app
from ..extensions import mongo
from pymongo import MongoClient
from bson import json_util
import json


def get_collection(collection_name):
    """Get a MongoDB collection by name"""
    return mongo.db[collection_name]


def insert_document(collection_name, document):
    """Insert a document into a collection"""
    collection = get_collection(collection_name)
    result = collection.insert_one(document)
    return result.inserted_id


def insert_many_documents(collection_name, documents):
    """Insert multiple documents into a collection"""
    collection = get_collection(collection_name)
    result = collection.insert_many(documents)
    return result.inserted_ids


def find_document(collection_name, query=None, projection=None):
    """Find a single document in a collection"""
    collection = get_collection(collection_name)
    return collection.find_one(query, projection)


def find_documents(collection_name, query=None, projection=None, sort=None, limit=0, skip=0):
    """Find documents in a collection"""
    collection = get_collection(collection_name)
    cursor = collection.find(query, projection)

    if sort:
        cursor = cursor.sort(sort)
    if skip:
        cursor = cursor.skip(skip)
    if limit:
        cursor = cursor.limit(limit)

    return list(cursor)


def update_document(collection_name, query, update, upsert=False):
    """Update a document in a collection"""
    collection = get_collection(collection_name)
    result = collection.update_one(query, {'$set': update}, upsert=upsert)
    return result.modified_count


def delete_document(collection_name, query):
    """Delete a document from a collection"""
    collection = get_collection(collection_name)
    result = collection.delete_one(query)
    return result.deleted_count



def get_collection_fields(collection_name):
    """
    Get all field names from a MongoDB collection

    Args:
        collection_name (str): Name of the MongoDB collection

    Returns:
        list: List of field names
    """
    try:
        collection = get_collection(collection_name)

        # Get one document to extract fields
        document = collection.find_one()

        if document:
            # Return all field names
            return list(document.keys())
        return []

    except Exception as e:
        current_app.logger.error(f"Error getting collection fields: {str(e)}")
        raise e


def query_mongodb(collection_name, query, projection=None, sort=None):
    """
    Execute a query against MongoDB and return results as JSON

    Args:
        collection_name (str): Name of the MongoDB collection
        query (dict): MongoDB query
        projection (dict): Fields to include/exclude
        sort (list): List of (field, direction) tuples for sorting

    Returns:
        str: JSON string of results
    """
    try:
        collection = get_collection(collection_name)
        cursor = collection.find(query, projection)

        if sort:
            cursor = cursor.sort(sort)

        # Convert cursor to list and then to JSON
        results = list(cursor)
        return json_util.dumps(results)

    except Exception as e:
        current_app.logger.error(f"Error querying MongoDB: {str(e)}")
        raise e
