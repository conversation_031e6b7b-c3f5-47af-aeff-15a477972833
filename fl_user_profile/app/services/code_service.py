from ..extensions import mongo
from flask import current_app


def create_code(objname: str, sequence_for: str):
    db = mongo.db.code_sequences
    filter_query = {"sequence_for": sequence_for}
    code_seq_res = db.find_one(filter_query)
    seqnum = code_seq_res["sequence_no"]

    objname = "".join(filter(str.isalpha, objname))
    objname = objname[:3].upper()

    code = code_seq_res["prefix"] + objname + f"{seqnum:03d}"

    update_query = {"sequence_for": sequence_for}
    update_result = db.update_one(update_query, {"$set": {"sequence_no": seqnum + 1}})

    return code
