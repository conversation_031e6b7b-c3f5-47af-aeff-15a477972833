from flask import current_app
from app import mongo
from pymongo import MongoClient
from bson import json_util
import json

from app.services.code_service import create_code


def generate_report_service(req_data):
    try:
        current_app.logger.debug("generate_report_service-starting: " + str(req_data))

        saved_query = mongo.db.table_queries.find_one({"code": req_data["query_code"]})
        current_app.logger.debug("generate_report_service-saved_query: " + str(saved_query))
        if not saved_query:
            raise Exception("No saved query found with the provided code.")

        table_details = mongo.db.exp_db_tables.find_one({"code": saved_query["table_code"]})
        current_app.logger.debug("generate_report_service-table_details: " + str(table_details))
        if not table_details:
            raise Exception("No table details found with the provided table_code.")

        client = MongoClient(mongo.db.exp_db_connections.find_one({"code": saved_query["db_code"]})["url"])
        user_db = client[mongo.db.exp_db_connections.find_one({"code": saved_query["db_code"]})["db_name"]]

        table_name = table_details["table_name"]
        current_app.logger.debug("generate_report_service-table_name: " + str(table_name))

        if "raw_query" in saved_query:
            current_app.logger.debug("generate_report_service-executing_raw_query")
            aggregate_query = saved_query["raw_query"]["query"]["aggregate"]

            aggregate_query.append({"$project": {"_id": 0}})
            report_data = list(user_db[table_name].aggregate(aggregate_query))
        elif "builder_query" in saved_query:
            current_app.logger.debug("generate_report_service-executing_builder_query")
            filter_query = saved_query["builder_query"]["query"].get("filter", {})
            sort_query = saved_query["builder_query"]["query"].get("sort", [])
            fields_query = saved_query["builder_query"]["query"].get("fields", [])

            projection = {field: 1 for field in fields_query}
            projection["_id"] = 0

            cursor = user_db[table_name].find(filter_query, projection).sort(sort_query if sort_query else [])
            report_data = list(cursor)
        else:
            raise Exception("Neither raw query nor builder query found in the saved query.")

        current_app.logger.debug("generate_report_service-report_data: " + str(report_data))

        report_code = create_code(req_data["report_name"], "exp_db_report_config")

        inserted_data = {
            "code": report_code,
            "query_code": req_data["query_code"],
            "report_name": req_data["report_name"],
            "report_type": req_data["report_type"],
            "users_list": req_data["users_list"],
            "sched_freq": req_data.get("sched_freq"),
            "sched_time": req_data.get("sched_time"),
            "sched_day": req_data.get("sched_day"),
            "sched_date": req_data.get("sched_date")
        }

        collection = mongo.db.exp_db_report_config
        collection.insert_one(inserted_data)

        if "_id" in inserted_data:
            del inserted_data["_id"]

        client.close()

        json_data = json_util.dumps({
            "result": True,
            "report_data": report_data,
            "inserted_data": inserted_data
        })

        return json.loads(json_data)

    except Exception as e:
        current_app.logger.error("generate_report_service-error: " + str(e))
        return {
            "result": False,
            "errors": str(e)
        }