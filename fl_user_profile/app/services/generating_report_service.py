from .horoscope.dhasa.graha import vimsottari
from .location import *
from collections import namedtuple as struct
from datetime import datetime
import swisseph as swe

from .panchanga import drik
from fl_user_profile.app.services import const, utils

# Struct definitions
Date = struct('Date', ['year', 'month', 'day'])
Place = struct('Place', ['Place', 'latitude', 'longitude', 'timezone'])

# Planet and Raasi lists
planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']
raasi_list = ['Mesham', 'Rishabam', 'Midunam', 'Kadagam', 'Simmam', 'Kanni', 'Thulam', 'Virichigam', 'Dhanusu',
              'MAGARAM', 'Kumbam', 'Meenam']


#
# NAKSHATRA_LIST = [
#     <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ka<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>,
#     <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Visaakam, An<PERSON><PERSON>, Kaetta<PERSON>, Moolam, Pooraadam, Uthiraadam, Thiruvonam,
#     A<PERSON><PERSON><PERSON>, Sadhayam, Poorattathi, Uthirattathi, Revathi]


def get_astrology_data(astro_data):
    # Determine the Lagnam (1st house)
    lagnam = next((item[1][0] for item in astro_data if item[0] == 'lagnam'), None)

    # Create the sequence of Rasis starting from the Lagnam
    if lagnam:
        start_index = raasi_list.index(lagnam)
        adjusted_raasi_list = raasi_list[start_index:] + raasi_list[:start_index]

        # Initialize a dictionary to store planets and degrees for each Rasi
        raasi_planet_dict = {raasi: {'planets': [], 'degrees': []} for raasi in adjusted_raasi_list}

        # Populate the dictionary with planets and degrees for each Rasi
        for item in astro_data:
            planet = item[0]
            raasi, degree = item[1]
            if raasi in raasi_planet_dict:
                raasi_planet_dict[raasi]['planets'].append(planet)
                raasi_planet_dict[raasi]['degrees'].append(degree)

        # Create a list of dictionaries to represent houses, planets, and degrees
        house_data = []
        for i, raasi in enumerate(adjusted_raasi_list, start=1):
            house_dict = {
                f'house_name_{i}': raasi,
                f'house_{i}_planets': raasi_planet_dict[raasi]['planets'],
                f'house_{i}_planets_degree': raasi_planet_dict[raasi]['degrees']
            }
            house_data.append(house_dict)

        return house_data
    else:
        return "Lagnam not found in the data."


def julian_day_number(date_of_birth_as_tuple, time_of_birth_as_tuple):
    """
        return julian day number for give Date of birth and time of birth as tuples
        @param date_of_birth_as_tuple: Date of birth as tuple. e.g. (2000,1,1)
            Note: For BC Dates use negative year e.g. (-3114,1,1) means 1-Jan of 3114 BC
            Note: There is no 0 BC or 0 AD so avoid Zero year
        @param time_of_birth_as_tuple: time of birth as tuple e.g. (18,0,30)
        @return julian day number
    """
    tob_in_hours = time_of_birth_as_tuple[0] + time_of_birth_as_tuple[1] / 60.0 + time_of_birth_as_tuple[2] / 3600.0
    jd = swe.julday(date_of_birth_as_tuple[0], date_of_birth_as_tuple[1], date_of_birth_as_tuple[2], tob_in_hours)
    return jd


def rasi_chart(jd_at_dob, place_as_tuple, ayanamsa_mode=const._DEFAULT_AYANAMSA_MODE, years=1, months=1, sixty_hours=1
               , calculation_type='drik', pravesha_type=0):
    """
        Get Rasi chart - D1 Chart
        @param jd_at_dob:Julian day number at the date/time of birth
            Note: It can be obtained from utils.julian_day_number(...)
        @param place_as_tuple - panjanga.place format
                example drik.place('Chennai,IN',13.0,78.0,+5.5)
        @param ayanamsa_mode Default:'Lahiri' - See const.available_ayanamsa_modes for more options
        @param years: Yearly chart. number of years from date of birth
        @param months: Monthly chart. number of months from date of birth
        @param sixty_hours: 60-hour chart. number of 60 hours from date of birth
        @return: planet_positions list in the format [[planet,(raasi,planet_longitude)],...]]
                First element is that of Lagnam
            Example: [ ['L',(0,13.4)],[0,(11,12.7)],...]] Lagnam in Aries 13.4 degrees, Sun in Taurus 12.7 degrees
    """
    jd_years = drik.next_solar_date(jd_at_dob, place_as_tuple, years, months, sixty_hours)
    if pravesha_type == 2:
        from .panchanga import vratha
        bt_year, bt_month, bt_day, bt_hours = utils.jd_to_gregorian(jd_at_dob)
        #print('bt_year,bt_month,bt_day,bt_hours',bt_year,bt_month,bt_day,bt_hours)
        birth_date = drik.Date(bt_year, bt_month, bt_day);
        birth_time = tuple(utils.to_dms(bt_hours, as_string=False))
        year_number = bt_year + years - 1
        tp = vratha.tithi_pravesha(birth_date, birth_time, place_as_tuple, year_number)
        #print('tithi pravesha',tp)
        tp_date = tp[0][0]
        tp_time = tp[0][1]
        birth_time = tuple(utils.to_dms(tp_time, as_string=False))
        tp_date_new = drik.Date(tp_date[0], tp_date[1], tp_date[2])
        jd_years = utils.julian_day_number(tp_date_new, birth_time)
    if calculation_type.lower() == 'ss':
        from panchanga import surya_sidhantha
        return surya_sidhantha.planet_positions(jd_years, place_as_tuple)
    ascendant_index = const._ascendant_symbol
    drik.set_ayanamsa_mode(ayanamsa_mode)
    " Get Ascendant information"
    ascendant_constellation, ascendant_longitude, _, _ = drik.ascendant(jd_years, place_as_tuple)
    """ FIXED in V2.3.1 - asc long re-calculated to get full longitude value """
    #ascendant_longitude += ascendant_longitude + ascendant_constellation*30
    #ascendant_divisional_chart_constellation,ascendant_divisional_chart_longitude = drik.dasavarga_from_long(ascendant_longitude,divisional_chart_factor=1)
    #print('ascendant dhasa varga',ascendant_divisional_chart_constellation,ascendant_divisional_chart_longitude)
    " Get planet information "
    " planet_positions lost: [planet_id, planet_constellation, planet_longitude] "
    planet_positions = drik.dhasavarga(jd_years, place_as_tuple, divisional_chart_factor=1)
    # print('planet_positions\n',planet_positions)
    planet_positions = [[ascendant_index, (ascendant_constellation, ascendant_longitude)]] + planet_positions
    return planet_positions


def navamsa_chart(planet_positions_in_rasi):
    """
        Navamsa Chart - D9 Chart
        @param planet_positions_in_rasi: Rasi chart planet_positions list in the format [[planet,(raasi,planet_longitude)],...]]. First element is that of Lagnam
            Example: [ ['L',(0,13.4)],[0,(11,12.7)],...]] Lagnam in Aries 13.4 degrees, Sun in Taurus 12.7 degrees
        @return: planet_positions list in the format [[planet,(raasi,planet_longitude)],...]]
                First element is that of Lagnam
            Example: [ ['L',(0,13.4)],[0,(11,12.7)],...]] Lagnam in Aries 13.4 degrees, Sun in Taurus 12.7 degrees
    """
    dvf = 9
    f1 = 30.0 / 9
    navamsa_dict = {0: const.fire_signs, 3: const.water_signs, 6: const.air_signs, 9: const.earth_signs}
    dp = []
    for planet, [sign, long] in planet_positions_in_rasi:
        d_long = (long * dvf) % 30
        l = int(long // f1)
        r = [(l + key) % 12 for key, sign_list in navamsa_dict.items() if sign in sign_list][0]
        dp.append([planet, [r, d_long]])
    return dp


def get_house_planet_list_from_planet_positions(planet_positions):
    """
        to convert from the format [planet,(house,planet_longitude,...]
        into a dict of {house_1:planet_1/planet_2,house_2:Lagnam/planet_2,....}
        @param planet_positions: Format: {planet_index:(raasi_index,planet_longitude_in_the_raasi),...
        @return: house_to_planet list - in the format ['0','1/2',...] Aries has Sun, Tarus has Moon/Mars etc
    """
    h_to_p = ['' for h in range(12)]
    for sublist in planet_positions:
        p = sublist[0]
        h = sublist[1][0]
        h_to_p[h] += str(p) + ','
    h_to_p = [x[:-1] for x in h_to_p]
    return h_to_p


def get_planet_house_dictionary_from_planet_positions(planet_positions):
    """
        Get Planet_to_House Dictionary {p:h}  from Planet_Positions {p:(h,long)}
        @param planet_positions: Format: {planet_index:(raasi_index,planet_longitude_in_the_raasi),...
        @return: planet_to_house_dictionary in the format {planet_index:raasi_index,...}
    """
    p_to_h = {p: h for p, (h, _) in planet_positions}
    return p_to_h


def map_planets_to_raasis(planet_to_raasi, planet_list, raasi_list):
    mapped_positions = {}
    for planet, raasi in planet_to_raasi.items():
        if planet == 'L':
            planet_name = 'lagnam'
        else:
            try:
                planet_name = planet_list[planet]
            except IndexError:
                print(f"IndexError: {planet} is out of range in planet_list")
                continue
        raasi_name = raasi_list[raasi]
        mapped_positions[planet_name] = raasi_name
    return mapped_positions


def format_planet_positions(planet_positions, planet_list, raasi_list):
    formatted_positions = []
    for item in planet_positions:
        planet = item[0]
        raasi, longitude = item[1]
        if planet == 'L':
            planet_name = 'lagnam'
        else:
            if planet < len(planet_list):
                planet_name = planet_list[planet]
            else:
                print(f"IndexError: {planet} is out of range in planet_list")
                continue
        raasi_name = raasi_list[raasi]
        formatted_positions.append([planet_name, (raasi_name, round(longitude, 2))])
    return formatted_positions


def format_planet_positions_360(planet_positions, planet_list, raasi_list):
    formatted_positions = []
    for item in planet_positions:
        planet = item[0]
        raasi, longitude = item[1]
        if planet == 'L':
            planet_name = 'lagnam'
        else:
            if planet < len(planet_list):
                planet_name = planet_list[planet]
            else:
                print(f"IndexError: {planet} is out of range in planet_list")
                continue

        raasi_name = raasi_list[raasi]
        raasi_longitude = raasi * 30 + longitude  # Calculate the exact position in 360 degrees
        formatted_positions.append([planet_name, (raasi_name, round(raasi_longitude, 2))])
    return formatted_positions


def find_nakshatra_and_pada(degree):
    nakshatras = [
        "ASHWINI", "BARANI", "KARTHIKAI", "ROHINI", "MIRIGASIRISHAM", "THIRUVADIRAI",
        "PUNARPOOSAM", "POOSAM", "AYILYAM", "MAGAM", "POORAM", "UTHIRAM", "HASTHAM",
        "CHITHIRAI", "SWATHI", "VISAGAM", "ANUSHAM", "KETTAI", "MOOLAM", "POORADAM",
        "UTHIRADAM", "THIRUVONAM", "AVITTAM", "SADAYAM", "POORATADHI", "UTHIRATTADHI", "REVATHI"
    ]
    span_per_nakshatra = 360 / 27
    span_per_pada = span_per_nakshatra / 4

    nakshatra_index = int(degree // span_per_nakshatra)
    pada_index = int((degree % span_per_nakshatra) // span_per_pada) + 1

    return nakshatras[nakshatra_index], pada_index


def process_data(data):
    for key in list(data.keys()):
        if key.endswith("_planets_degree"):
            house = key.replace("_planets_degree", "")
            planet_key = house + "_planets"
            planets = data[planet_key]
            degrees = data[key]
            for i, degree in enumerate(degrees):
                nakshatra, pada = find_nakshatra_and_pada(degree)
                planet = planets[i]
                star_key = f"{planet}_star"
                pada_key = f"{planet}_pada"
                data[star_key] = nakshatra
                data[pada_key] = pada
    return data


def compute_periods(data):
    """
    Compute start and end times for periods based on the given data.

    Args:
        data (list of tuples): Each tuple contains a name and a start time.

    Returns:
        list of str: A list of formatted strings in the desired output format.
    """
    periods = []
    for i in range(len(data) - 1):
        name, start_time = data[i]
        _, end_time = data[i + 1]
        periods.append(f"({name}, {start_time}, {end_time})")

    # Handle the last entry with no defined end time
    name, start_time = data[-1]
    periods.append(f"({name}, {start_time}, None)")  # Replace None with a custom end message if needed

    return periods


def bhava_chart_houses(jd_at_dob, place_as_tuple, ayanamsa_mode=const._DEFAULT_AYANAMSA_MODE, years=1, months=1,
                       sixty_hours=1, calculation_type='drik', bhava_starts_with_ascendant=False):
    """
        Get Bhava chart from Rasi / D1 Chart
        @param jd_at_dob: Julian day number at the date/time of birth
            Note: It can be obtained from utils.julian_day_number(...)
        @param place_as_tuple: panjanga.place format
            example: drik.place('Chennai,IN', 13.0, 78.0, +5.5)
        @param ayanamsa_mode: Default: 'Lahiri' - See const.available_ayanamsa_modes for more options
        @param years: Yearly chart. number of years from date of birth
        @param months: Monthly chart. number of months from date of birth
        @param sixty_hours: 60-hour chart. number of 60 hours from date of birth
        @return: planet_positions list in the format [['L', (house, longitude)], ...]
            Example: [['L', (5, 29.29)], [0, (1, 11.25)], ...] Lagnam in Leo 29.29 degrees, Sun in Taurus 11.25 degrees
    """
    # Get planetary positions from the Rasi chart
    planet_positions = rasi_chart(jd_at_dob, place_as_tuple, ayanamsa_mode, years, months, sixty_hours,
                                  calculation_type=calculation_type)
    print('Rasi planet positions:', planet_positions)

    # Extract Ascendant house and its longitude
    asc_house = planet_positions[0][1][0]
    asc_long = planet_positions[0][1][1]

    # Calculate ascendant range
    asc_start = asc_long if bhava_starts_with_ascendant else asc_long - 15.0
    asc_end = asc_long + 30.0 if bhava_starts_with_ascendant else asc_long + 15.0

    # Initialize Bhava positions
    bhava_positions = []

    # Determine house placements
    for p, (h, p_long) in planet_positions:
        if bhava_starts_with_ascendant:
            if p_long < asc_start:
                h = (h - 1) % 12
            elif p_long > asc_end:
                h = (h + 1) % 12
        else:
            if p_long < asc_start:
                h = (h - 1) % 12
            elif p_long > asc_end:
                h = (h + 1) % 12
        bhava_positions.append([p, (h, p_long)])

    return bhava_positions


from openpyxl.utils.exceptions import InvalidFileException
from openpyxl import load_workbook
import pandas as pd


# Function to check if a sheet exists
def sheet_exists(file_path, sheet_name):
    try:
        workbook = load_workbook(file_path)
        return sheet_name in workbook.sheetnames
    except (FileNotFoundError, InvalidFileException):
        return False


# Function to check if a user_id exists in a specific sheet
def user_id_exists(file_path, sheet_name, user_id):
    try:
        if not sheet_exists(file_path, sheet_name):
            return False
        # Load the sheet into a DataFrame
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        return user_id in df['user_id'].values
    except (FileNotFoundError, ValueError):
        return False


from datetime import datetime


def generate_report_service(req_data):
    # try:
    dob_timestamp = req_data['user_birthdate']
    dob_datetime = datetime.strptime(dob_timestamp, "%d-%m-%Y").date()
    dob = (dob_datetime.year, dob_datetime.month, dob_datetime.day)
    tob_time = req_data['user_birthtime']
    tob_datetime = datetime.strptime(tob_time, "%I:%M:%S %p")
    tob = (tob_datetime.hour, tob_datetime.minute, tob_datetime.second)
    location = req_data['user_birthplace'] + "," + req_data['user_state'] + "," + req_data['user_country']
    location_info = get_location_info(location)
    place = Place(location_info['Location_name'], location_info['latitude'], location_info['longitude'],
                  location_info['Time_zone_offset'])
    jd = julian_day_number(dob, tob)
    pp = rasi_chart(jd, place)
    navamsa_ch = navamsa_chart(pp)   # Process astrology data
    formatted_position = format_planet_positions_360(pp, planet_list, raasi_list)
    result = get_astrology_data(formatted_position)
    navamsa_ch_formatted_position = format_planet_positions_360(navamsa_ch, planet_list, raasi_list)
    navamsa_ch_result = get_astrology_data(navamsa_ch_formatted_position)
    # Get dasha details
    vimsottari_balance = vimsottari.get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1,
                                                                 divisional_chart_factor=1)
    maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha = vimsottari_balance

    # Prepare user result
    user_result = {}
    print()
    navamsa_chart_user_result = {}
    print(result)
    for house_number, house_data in enumerate(result, 1):
        user_result[f'house_name_{house_number}'] = house_data[f'house_name_{house_number}']
        user_result[f'house_{house_number}_planets'] = house_data[f'house_{house_number}_planets']
        user_result[f'house_{house_number}_planets_degree'] = house_data[f'house_{house_number}_planets_degree']
    user_result['maha_dasha'] = maha_dasha
    user_result['bhukti_dasha'] = bhukti_dasha
    # user_result['antara_dasha'] = antara_dasha
    # user_result['sukshma_dasha'] = sukshma_dasha
    # user_result['prana_dasha'] = prana_dasha

    for house_number, house_data in enumerate(navamsa_ch_result, 1):
        navamsa_chart_user_result[f'house_name_{house_number}'] = house_data[f'house_name_{house_number}']
        navamsa_chart_user_result[f'house_{house_number}_planets'] = house_data[f'house_{house_number}_planets']
        navamsa_chart_user_result[f'house_{house_number}_planets_degree'] = house_data[
            f'house_{house_number}_planets_degree']

    user_result = process_data(user_result)
    navamsa_chart_user_result = process_data(navamsa_chart_user_result)
    from pymongo import MongoClient
    from pymongo.collection import Collection
    cli = MongoClient("mongodb://localhost:27017/")
    srvinfo = cli.server_info()
    db = cli["test_database21"]
    collection = db["test_collection"]

    # Insert a sample document
    collection.insert_one(user_result)
    print("Document inserted!")
    return {'result': True, 'message': 'DB Created Successfully'}
    # except (FileNotFoundError, ValueError):
    #     return False

# Example usage
# if __name__ == "__main__":
#     # Filepath for the Excel file
#     filepath = 'C:/Users/<USER>/PycharmProjects/FortuneLens/user_data.xlsx'
#
#     # Read the "Astro Results" and "Navamsa Chart" sheets
#     try:
#         astro_results_df = pd.read_excel(filepath, sheet_name="Astro Results")
#         navamsa_chart_df = pd.read_excel(filepath, sheet_name="Navamsa Chart")
#         astro_user_ids = set(astro_results_df['user_id'])
#         navamsa_user_ids = set(navamsa_chart_df['user_id'])
#     except ValueError:
#         print("One or both sheets ('Astro Results' or 'Navamsa Chart') do not exist. Creating them.")
#         astro_user_ids = set()
#         navamsa_user_ids = set()
#
#     # Combine user_ids from both sheets
#     processed_user_ids = astro_user_ids.intersection(navamsa_user_ids)
#     # print(processed_user_ids)
#     # Read the "User Astro Data Master" sheet for reference
#     reference_df = pd.read_excel(filepath, sheet_name="User Astro Data Master")
#     # print(reference_df)
#
#     # Filter using lambda function
#     users_to_process = reference_df[~reference_df['user_id'].isin(processed_user_ids)]
#
#     # print(users_to_process)
#
#     # print(users_to_process['user_birthtime'])
#     if users_to_process.empty:
#         print("All users are already processed. No new processing is needed.")
#     else:
#         print(f"Processing {len(users_to_process)} new users...")
#
#     results = []
#     navamsa_chart_result = []
#
#     # Loop through each user
#     for index, row in users_to_process.iterrows():
#         # print(index, row)
#         user_id = row['user_id']
#         dob_timestamp = row['user_birthdate']
#         tob_time = row['user_birthtime']
#         location = row['user_birthplace'] + "," + row['user_state'] + "," + row['user_country']
#
#         dob = Date(dob_timestamp.year, dob_timestamp.month, dob_timestamp.day)
#         tob = (tob_time.hour, tob_time.minute, tob_time.second)
#
#         location_info = get_location_info(location)
#         # print(location, location_info)
#         place = Place(location_info['Location_name'], location_info['latitude'], location_info['longitude'],
#                       location_info['Time_zone_offset'])
#
#         jd = julian_day_number(dob, tob)
#         pp = rasi_chart(jd, place)
#         navamsa_ch = navamsa_chart(pp)
#
#         # Process astrology data
#         formatted_position = format_planet_positions_360(pp, planet_list, raasi_list)
#         result = get_astrology_data(formatted_position)
#         navamsa_ch_formatted_position = format_planet_positions_360(navamsa_ch, planet_list, raasi_list)
#         navamsa_ch_result = get_astrology_data(navamsa_ch_formatted_position)
#
#         # Get dasha details
#         from horoscope.dhasa.graha import vimsottari
#
#         vimsottari_balance = vimsottari.get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1,
#                                                                      divisional_chart_factor=1)
#         maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha = vimsottari_balance
#
#         # Prepare user result
#         user_result = {'user_id': user_id}
#         print(f"Results for user {index + 1}:")
#         navamsa_chart_user_result = {'user_id': user_id}
#
#         for house_number, house_data in enumerate(result, 1):
#             user_result[f'house_name_{house_number}'] = house_data[f'house_name_{house_number}']
#             user_result[f'house_{house_number}_planets'] = house_data[f'house_{house_number}_planets']
#             user_result[f'house_{house_number}_planets_degree'] = house_data[f'house_{house_number}_planets_degree']
#             user_result['maha_dasha'] = maha_dasha
#             user_result['bhukti_dasha'] = bhukti_dasha
#             user_result['antara_dasha'] = antara_dasha
#             user_result['sukshma_dasha'] = sukshma_dasha
#             user_result['prana_dasha'] = prana_dasha
#
#         for house_number, house_data in enumerate(navamsa_ch_result, 1):
#             navamsa_chart_user_result[f'house_name_{house_number}'] = house_data[f'house_name_{house_number}']
#             navamsa_chart_user_result[f'house_{house_number}_planets'] = house_data[f'house_{house_number}_planets']
#             navamsa_chart_user_result[f'house_{house_number}_planets_degree'] = house_data[
#                 f'house_{house_number}_planets_degree']
#
#         user_result = process_data(user_result)
#         navamsa_chart_user_result = process_data(navamsa_chart_user_result)
#         results.append(user_result)
#         navamsa_chart_result.append(navamsa_chart_user_result)
#
#     # Convert results and navamsa_chart_result to DataFrame before saving to Excel
#     results_df = pd.DataFrame(results)
#     cols = results_df.columns.tolist()
#     reordered_cols = (
#             ['user_id'] +
#             [f'house_name_{i}' for i in range(1, 13)] +
#             [f'house_{i}_planets' for i in range(1, 13)] +
#             [f'house_{i}_planets_degree' for i in range(1, 13)] +
#             [f"{planet}" for planet in user_result if planet.endswith('_star')] +
#             [f"{planet}" for planet in user_result if planet.endswith('_pada')] +
#             ['maha_dasha'] +
#             ['bhukti_dasha'] +
#             ['antara_dasha'] +
#             ['sukshma_dasha'] +
#             ['prana_dasha']
#     )
#     print(reordered_cols)
#     navamsa_chart_result_df = pd.DataFrame(navamsa_chart_result)
#     navamsa_chart_cols = results_df.columns.tolist()
#     navamsa_chart_reordered_cols = (
#             ['user_id'] +
#             [f'house_name_{i}' for i in range(1, 13)] +
#             [f'house_{i}_planets' for i in range(1, 13)] +
#             [f'house_{i}_planets_degree' for i in range(1, 13)] +
#             [f"{planet}" for planet in user_result if planet.endswith('_star')] +
#             [f"{planet}" for planet in user_result if planet.endswith('_pada')])
#
#
#     def clean_list(lst):
#         return ', '.join(map(str, lst)) if lst else ''
#
#
#     for col in reordered_cols:
#         if 'planets' in col or 'degrees' in col:
#             results_df[col] = results_df[col].apply(clean_list)
#
#     for col in navamsa_chart_reordered_cols:
#         if 'planets' in col or 'degrees' in col:
#             navamsa_chart_result_df[col] = navamsa_chart_result_df[col].apply(clean_list)
#
#     print(reordered_cols, results_df)
#     results_df = results_df[reordered_cols]
#     navamsa_chart_result_df = navamsa_chart_result_df[navamsa_chart_reordered_cols]
#     # print(results_df['sukshma_dasha'])
#     # results_df['sukshma_dasha'] = results_df['column1'].astype(str) + ' ' + results_df['column2'].astype(str)
#     # Write results to Excel if new data exists
#     with pd.ExcelWriter(filepath, mode='a', engine='openpyxl', if_sheet_exists='overlay') as writer:
#         # Write Astro Results sheet
#         if not sheet_exists(filepath, "Astro Results"):
#             results_df.to_excel(writer, sheet_name="Astro Results", index=False)
#         else:
#             existing_astro_df = pd.read_excel(filepath, sheet_name="Astro Results")
#             combined_astro_df = pd.concat([existing_astro_df, results_df])
#             combined_astro_df.to_excel(writer, sheet_name="Astro Results", index=False)
#
#         # Write Navamsa Chart sheet
#         if not sheet_exists(filepath, "Navamsa Chart"):
#             navamsa_chart_result_df.to_excel(writer, sheet_name="Navamsa Chart", index=False)
#         else:
#             existing_navamsa_df = pd.read_excel(filepath, sheet_name="Navamsa Chart")
#             combined_navamsa_df = pd.concat([existing_navamsa_df, navamsa_chart_result_df])
#             combined_navamsa_df.to_excel(writer, sheet_name="Navamsa Chart", index=False)
#     print("Astrology results have been written to the 'Astro Results' Rasi chart sheet in the Excel file.")
#
#
