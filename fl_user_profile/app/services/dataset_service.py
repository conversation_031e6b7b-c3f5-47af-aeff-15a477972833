from typing import List

from flask import current_app
from pymongo import MongoClient
from pymongo.collection import Collection

from .code_service import create_code
from ..extensions import mongo


def test_and_create_db(**req_data):
    try:
        current_app.logger.debug("dataset_service-create_db: " + str(req_data))
        print(req_data)
        cli = MongoClient(req_data["url"])
        srvinfo = cli.server_info()
        db = cli["test_database21"]
        collection = db["test_collection"]

        # Insert a sample document
        collection.insert_one({"name": "<PERSON>", "age": 25})
        print("Document inserted!")
        current_app.logger.error("dataset_service-test_and_create_db: server info: " + str(srvinfo))
        current_app.logger.debug("Data Created Successfully..")
        return {'result': True, 'message': 'DB Created Successfully'}

    except Exception as e:
        current_app.logger.error("DB Exception: " + str(e))
        return {'result': False, 'errors': 'Unable to connect to Database'}

    # db = client["test_database"]
    # collection = db["test_collection"]
    #
    # # Insert a sample document
    # collection.insert_one({"name": "<PERSON>", "age": 25})
    # print("Document inserted!")
    #
    # collection = mongo.db.exp_db_connections
    # print(collection)
    # conn_code = create_code(req_data["db_name"], "exp_db_connections")
    # req_data["code"] = conn_code
    #
    # collection.insert_one(req_data)
    # current_app.logger.debug("Data Created Successfully..")
    # return {'result': True, 'message': 'DB Created Successfully'}
    # except Exception as e:
    #     current_app.logger.error("Exception: " + str(e))
    #     return {'result': False, 'errors': 'Failed to create connection.'}


def check_db_presence():
    try:
        collection = mongo.db.exp_db_connections
        existing_doc = collection.find_one()

        current_app.logger.debug("dataset_service-check_db_presence: result: " + str(existing_doc))

        if existing_doc:
            return {'result': True, 'message': 'DB present', 'db_code': existing_doc['code']}
        else:
            return {'result': False, 'message': 'DB not present'}
    except Exception as e:
        current_app.logger.error("Exception: " + str(e))
        return {'result': False, 'errors': 'An unexpected error occurred'}


def get_url_from_code(code: str, code_collection: Collection):
    try:
        code_entry = code_collection.find_one({"code": code})
        if code_entry is None or "url" not in code_entry:
            raise Exception("URL not found for the provided code: {code}")

        return code_entry["url"]

    except Exception as e:
        raise Exception("Unable to retrieve URL from code: {e}")


def get_collections_for_code(req_data: dict) -> List[str]:
    code = req_data['code']
    code_collection = mongo.db.exp_db_connections

    try:
        url = get_url_from_code(code, code_collection)  # directly pass the code string
        db_name = MongoClient(url).get_database().name

        with MongoClient(url) as client:
            db = client[db_name]
            all_collection_names = db.list_collection_names()

        registered_tables = mongo.db.exp_db_tables.find(
            {"exp_db_connections_code": code},
            {"table_name": 1}
        )
        reg_table_names = [table["table_name"] for table in registered_tables]

        filtered_coll_names = [
            collection_name for collection_name in all_collection_names
            if collection_name not in reg_table_names
        ]

        return filtered_coll_names

    except Exception as e:
        raise Exception("Unable to retrieve collections: {e}")


def add_table(code: str, table_name: str, display_name: str):
    try:
        url = get_url_from_code(code, mongo.db.exp_db_connections)
        table_code = create_code(url, "exp_db_tables")

        table_entry = {
            "exp_db_connections_code": code,
            "table_name": table_name,
            "display_name": display_name,
            "code": table_code
        }

        collection = mongo.db.exp_db_tables
        collection.insert_one(table_entry)

        return {'result': True, 'message': 'Table Added Successfully'}

    except Exception as e:
        current_app.logger.error("Exception: " + str(e))
        return {'result': False, 'errors': 'Failed to add table.'}


def get_tables_for_code(**req_data):
    try:
        code = req_data["code"]

        connection_doc = mongo.db.exp_db_connections.find_one({"code": code})
        db_type = connection_doc.get('db_type', '')

        tables_cursor = mongo.db.exp_db_tables.find({"exp_db_connections_code": code}, {"table_name" : 1,"display_name": 1, "code": 1})

        tables = [{"display_name": table["display_name"], "table_code": table["code"], "table_name": table["table_name"]} for table in tables_cursor]

        return db_type, tables
    except Exception as e:
        current_app.logger.error("Exception: " + str(e))
        return None, []
