from flask import Flask
from .config import Config
from .extensions import mongo, cors
import logging


def create_app():
    app = Flask(__name__)

    # Load configuration from Config class
    app.config.from_object(Config)

    # Configure logger
    configure_logger(app)

    # Initialize extensions
    mongo.init_app(app)
    cors.init_app(app, origins=['*'])

    # Register blueprints for routes
    from .controllers.test_controller import test_api
    from .controllers.dataset_controller import dataset_api
    from .controllers.report_controller import report_api
    from .controllers.generating_reports_controller import generate_report_api
    app.register_blueprint(test_api)
    app.register_blueprint(dataset_api)
    app.register_blueprint(report_api)
    app.register_blueprint(generate_report_api)
    return app


def configure_logger(app):
    log_level = app.config.get('LOG_LEVEL', logging.DEBUG)
    logging.basicConfig(level=log_level, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
