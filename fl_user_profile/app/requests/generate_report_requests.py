from marshmallow import Schema, fields, validate


class GenerateReportRequestValidator(Schema):
    user_name = fields.Str(required=True)
    user_birthdate = fields.Str(required=True)
    user_birthtime = fields.Str(required=True)
    user_birthplace = fields.Str(required=True)
    user_state = fields.Str(required=True)
    user_country = fields.Str(required=True)
    user_gender = fields.Str(required=True, validate=validate.OneOf(["Male", "Female", "Others"]))
