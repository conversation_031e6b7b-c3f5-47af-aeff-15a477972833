from marshmallow import Schema, fields, validate


class ReportRequestValidator(Schema):
    query_code = fields.Str(required=True)
    report_name = fields.Str(required=True)
    report_type = fields.Str(required=True, validate=validate.OneOf(["front_end", "scheduled"]))
    users_list = fields.List(fields.Str(), missing=[])
    sched_freq = fields.Str(allow_none=True)
    sched_time = fields.Str(allow_none=True)
    sched_day = fields.List(fields.Str(), allow_none=True)
    sched_date = fields.Str(allow_none=True)
