from marshmallow import Schema, fields, validate


class CreateDbReq(Schema):
    url = fields.Str(required=True)
    db_name = fields.Str(required=True)
    display_name = fields.Str(required=True)
    db_type = fields.Str(required=True, validate=validate.OneOf(["mongodb", "postgresql"]))


class GetCollDb(Schema):
    code = fields.Str(required=True)


class AddTableReq(Schema):
    code = fields.Str(required=True)
    table_name = fields.Str(required=True)
    display_name = fields.Str(required=True)


class GetTables(Schema):
    code = fields.Str(required=True)


