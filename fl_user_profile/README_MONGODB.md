# MongoDB Configuration for Fortune Lens

This document provides instructions on how to use MongoDB with the Fortune Lens application.

## Setup

1. Make sure MongoDB is installed and running on your system.
2. The application is configured to connect to MongoDB at `mongodb://localhost:27017/fortune_lens`.
3. You can modify the connection settings in `app/config.py`.


## Using MongoDB in the Application

The application uses MongoDB for:

1. **User Profiles**: Storing user information

### MongoDB Utility Functions

The application provides utility functions for working with MongoDB:

- `db_utils.py`: General MongoDB utility functions



## Testing

To test the MongoDB configuration:

```bash
python3 test_mongodb.py
```

This will:
1. Connect to MongoDB
2. Print server information
3. List available collections
4. Print sample documents if available

## Troubleshooting

If you encounter issues with MongoDB:

1. Make sure MongoDB is running: `ps aux | grep mongod`
2. Check the MongoDB connection string in `app/config.py`
3. Verify that the database and collections exist: `python3 test_mongodb.py`
4. Check the application logs for MongoDB-related errors
