"""
Test script for marriage date prediction functionality.
"""

import sys
import os
import json
from datetime import datetime
from bson import ObjectId

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Import Flask app and initialize it
from fortune_lens.app import create_app
app = create_app()

# Use the app context
with app.app_context():
    # Import the function to test
    from fortune_lens.app.services.marriage_matching.date_prediction import predict_marriage_dates
    
    # Get member profile ID for user_profile_id=1 and member_profile_id=1
    from fortune_lens.app.extensions import mongo
    
    # Find the member profile
    member_profile = mongo.db.member_profile.find_one({"user_profile_id": 1, "member_profile_id": 1})
    
    if not member_profile:
        print("Member profile not found for user_profile_id=1 and member_profile_id=1")
        sys.exit(1)
    
    member_id = str(member_profile["_id"])
    print(f"Found member profile with ID: {member_id}")
    
    # Run prediction with print_output=True
    result = predict_marriage_dates(member_id, marriage_date='2023-05-15', print_output=True)
    
    # Print the result
    print("\nFull JSON Response:")
    print(json.dumps(result, indent=2, default=str))
