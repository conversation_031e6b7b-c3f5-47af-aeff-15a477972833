#!/usr/bin/env python3
"""
Script to fix member_profile_id for user_profile_id 19
"""

from pymongo import MongoClient
from bson import ObjectId

# Connect to MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client.fortune_lens

# Find the second member for user_profile_id 19
member = db.member_profile.find_one({'user_profile_id': 19, 'relation': 'member_2'})
if member:
    print(f"Found member: {member.get('name')}")
    print(f"Current member_profile_id: {member.get('member_profile_id')}")
    
    # Update the member_profile_id to 2
    result = db.member_profile.update_one(
        {'_id': member['_id']},
        {'$set': {'member_profile_id': 2}}
    )
    
    print(f"Updated member_profile_id: {result.modified_count} document(s) modified")
    
    # Also update the corresponding astro profile
    astro_result = db.user_astro_profile_data.update_one(
        {'user_profile_id': 19, 'member_profile_id': member.get('member_profile_id')},
        {'$set': {'member_profile_id': 2}}
    )
    
    print(f"Updated astro profile member_profile_id: {astro_result.modified_count} document(s) modified")
    
    # Verify the update
    updated_member = db.member_profile.find_one({'_id': member['_id']})
    print(f"\nVerified member profile: member_profile_id={updated_member.get('member_profile_id')}")
    
    updated_astro = db.user_astro_profile_data.find_one({'user_profile_id': 19, 'member_profile_id': 2})
    if updated_astro:
        print(f"Verified astro profile: member_profile_id={updated_astro.get('member_profile_id')}")
    else:
        print("Astro profile not found after update")
else:
    print("Second member not found for user_profile_id 19")
