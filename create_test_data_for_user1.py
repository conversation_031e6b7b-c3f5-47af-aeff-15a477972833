import pymongo
from bson import ObjectId
from datetime import datetime

# Connect to MongoDB
client = pymongo.MongoClient('mongodb://localhost:27017/')
db = client['fortune_lens']

# Get existing members with astrological data
female_member = db.member_profile.find_one({'_id': ObjectId('681cf6ea371b2bdd48e94ce4')})
male_member = db.member_profile.find_one({'_id': ObjectId('681cf3574a069da2c07fe737')})

# Get user profile with ID 1
user_profile = db.user_profile.find_one({'user_profile_id': 1})

if not user_profile:
    print("User profile with ID 1 not found")
    exit(1)

# Create copies of the members for user_profile_id 1
female_member_copy = {
    '_id': ObjectId(),
    'member_profile_id': 4,  # New member_profile_id
    'user_profile_id': 1,
    'unique_key': female_member.get('unique_key', ''),
    'name': female_member.get('name', 'Female Member'),
    'relation': 'spouse',
    'birth_date': female_member.get('birth_date', '1990-05-15'),
    'birth_time': female_member.get('birth_time', '14:30:00'),
    'birth_place': female_member.get('birth_place', 'Chennai, India'),
    'state': female_member.get('state', 'Tamil Nadu'),
    'country': female_member.get('country', 'India'),
    'latitude': female_member.get('latitude', 13.0827),
    'longitude': female_member.get('longitude', 80.2707),
    'gender': 'Female',
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow()
}

male_member_copy = {
    '_id': ObjectId(),
    'member_profile_id': 5,  # New member_profile_id
    'user_profile_id': 1,
    'unique_key': male_member.get('unique_key', ''),
    'name': male_member.get('name', 'Male Member'),
    'relation': 'self',
    'birth_date': male_member.get('birth_date', '1988-10-20'),
    'birth_time': male_member.get('birth_time', '10:15:00'),
    'birth_place': male_member.get('birth_place', 'Chennai, India'),
    'state': male_member.get('state', 'Tamil Nadu'),
    'country': male_member.get('country', 'India'),
    'latitude': male_member.get('latitude', 13.0827),
    'longitude': male_member.get('longitude', 80.2707),
    'gender': 'Male',
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow()
}

# Get existing astrological data
female_astro_data = db.user_member_astro_profile_data.find_one({'member_profile_id': ObjectId('681cf6ea371b2bdd48e94ce4')})
male_astro_data = db.user_member_astro_profile_data.find_one({'member_profile_id': ObjectId('681cf3574a069da2c07fe737')})

# Create copies of the astrological data for the new members
female_astro_data_copy = {
    '_id': ObjectId(),
    'member_profile_id': female_member_copy['_id'],
    'user_profile_id': user_profile['_id'],
    'name': female_member_copy['name'],
    'gender': female_member_copy['gender'],
    'birth_date': female_member_copy['birth_date'],
    'birth_time': female_member_copy['birth_time'],
    'birth_place': female_member_copy['birth_place'],
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow(),
    'chart_data': female_astro_data.get('chart_data', {})
}

male_astro_data_copy = {
    '_id': ObjectId(),
    'member_profile_id': male_member_copy['_id'],
    'user_profile_id': user_profile['_id'],
    'name': male_member_copy['name'],
    'gender': male_member_copy['gender'],
    'birth_date': male_member_copy['birth_date'],
    'birth_time': male_member_copy['birth_time'],
    'birth_place': male_member_copy['birth_place'],
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow(),
    'chart_data': male_astro_data.get('chart_data', {})
}

# Insert data into database
db.member_profile.insert_one(female_member_copy)
db.member_profile.insert_one(male_member_copy)
db.user_member_astro_profile_data.insert_one(female_astro_data_copy)
db.user_member_astro_profile_data.insert_one(male_astro_data_copy)

print(f"Created female member with ID: {female_member_copy['_id']} and member_profile_id: {female_member_copy['member_profile_id']}")
print(f"Created male member with ID: {male_member_copy['_id']} and member_profile_id: {male_member_copy['member_profile_id']}")
print(f"Created female astro data with ID: {female_astro_data_copy['_id']}")
print(f"Created male astro data with ID: {male_astro_data_copy['_id']}")
