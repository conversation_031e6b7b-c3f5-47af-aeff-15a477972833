#!/usr/bin/env python3
"""
<PERSON><PERSON>t to clean up temporary import scripts and keep only the final one
"""

import os
import shutil

def cleanup_scripts():
    """Clean up temporary import scripts and keep only the final one"""
    print("Cleaning up temporary import scripts...")
    
    # Path to the project folder
    project_path = '/Users/<USER>/PycharmProjects/fortune_lens'
    
    # Files to keep
    files_to_keep = [
        'import_with_updated_fields.py',  # Final import script
        'clear_database.py',              # Useful utility script
        'check_database.py',              # Useful utility script
        'user_data_1.xlsx'                # Excel data file
    ]
    
    # Files to delete (all import_*.py files except the ones to keep)
    files_to_delete = []
    for filename in os.listdir(project_path):
        if filename.startswith('import_') and filename.endswith('.py') and filename not in files_to_keep:
            files_to_delete.append(filename)
        elif filename.startswith('analyze_') and filename.endswith('.py'):
            files_to_delete.append(filename)
        elif filename.startswith('check_') and filename.endswith('.py') and filename not in files_to_keep:
            files_to_delete.append(filename)
        elif filename.startswith('verify_') and filename.endswith('.py'):
            files_to_delete.append(filename)
        elif filename.startswith('examine_') and filename.endswith('.py'):
            files_to_delete.append(filename)
        elif filename.startswith('count_') and filename.endswith('.py'):
            files_to_delete.append(filename)
        elif filename.startswith('fix_') and filename.endswith('.py'):
            files_to_delete.append(filename)
        elif filename.startswith('user_') and filename.endswith('_credentials.txt'):
            files_to_delete.append(filename)
    
    # Delete files
    for filename in files_to_delete:
        file_path = os.path.join(project_path, filename)
        try:
            os.remove(file_path)
            print(f"Deleted: {filename}")
        except Exception as e:
            print(f"Error deleting {filename}: {str(e)}")
    
    # Rename the final import script to a more appropriate name
    old_path = os.path.join(project_path, 'import_with_updated_fields.py')
    new_path = os.path.join(project_path, 'import_excel_to_mongodb.py')
    try:
        shutil.copy2(old_path, new_path)
        print(f"Renamed: import_with_updated_fields.py -> import_excel_to_mongodb.py")
    except Exception as e:
        print(f"Error renaming file: {str(e)}")
    
    print("Cleanup completed.")

if __name__ == "__main__":
    cleanup_scripts()
