"""
Unit tests for the rule engine service.
"""

import unittest
from unittest.mock import patch, MagicMock
from flask import Flask
from flask_pymongo import PyMongo

from app.services.rule_engine import (
    parse_condition,
    parse_complex_query,
    evaluate_condition,
    evaluate_parsed_query,
    get_planet_house_mapping
)


class TestRuleEngine(unittest.TestCase):
    """Test cases for the rule engine service."""

    def test_parse_condition(self):
        """Test parsing a single condition."""
        # Test valid conditions
        self.assertEqual(parse_condition("Moon IN House1"), ("MOON", "IN", 1, "BASIC"))
        self.assertEqual(parse_condition("Sun IN House5"), ("SUN", "IN", 5, "BASIC"))
        self.assertEqual(parse_condition("Mars NOT IN House8"), ("MARS", "NOT IN", 8, "BASIC"))

        # Test case insensitivity
        self.assertEqual(parse_condition("moon in house1"), ("MOON", "IN", 1, "BASIC"))

        # Test advanced conditions
        self.assertEqual(parse_condition("Ketu IN House6 WITH Ruling_Planet"), ("KETU", "IN", 6, "WITH_RULING_PLANET"))
        self.assertEqual(parse_condition("Ketu IS RELATED TO House6_Ruling_Planet"), ("KETU", "IS_RELATED_TO", 6, "RELATED_TO_RULING_PLANET"))
        self.assertEqual(parse_condition("Ketu IS ASPECTING_BIRTH House6_Ruling_Planet"), ("KETU", "IS_ASPECTING_BIRTH", 6, "ASPECTING_BIRTH_RULING_PLANET"))

        # Test invalid conditions
        self.assertEqual(parse_condition("Moon House1"), (None, None, None, None))
        self.assertEqual(parse_condition("Moon IN"), (None, None, None, None))
        self.assertEqual(parse_condition("IN House1"), (None, None, None, None))

    def test_parse_complex_query(self):
        """Test parsing complex queries with logical operators."""
        # Test single condition
        query = "Moon IN House1"
        expected = [("SINGLE", ("MOON", "IN", 1, "BASIC"))]
        self.assertEqual(parse_complex_query(query), expected)

        # Test OR conditions
        query = "Moon IN House1 OR Sun IN House5"
        expected = [
            ("SINGLE", ("MOON", "IN", 1, "BASIC")),
            ("SINGLE", ("SUN", "IN", 5, "BASIC"))
        ]
        self.assertEqual(parse_complex_query(query), expected)

        # Test AND conditions
        query = "Moon IN House1 AND Sun IN House5"
        expected = [
            ("AND", [("MOON", "IN", 1, "BASIC"), ("SUN", "IN", 5, "BASIC")])
        ]
        self.assertEqual(parse_complex_query(query), expected)

        # Test mixed conditions
        query = "Moon IN House1 AND Sun IN House5 OR Mars IN House10"
        expected = [
            ("AND", [("MOON", "IN", 1, "BASIC"), ("SUN", "IN", 5, "BASIC")]),
            ("SINGLE", ("MARS", "IN", 10, "BASIC"))
        ]
        self.assertEqual(parse_complex_query(query), expected)

    def test_evaluate_condition(self):
        """Test evaluating a single condition."""
        # Setup planet-house mapping
        planet_house_mapping = {
            "SUN": 5,
            "MOON": 1,
            "MARS": 10,
            "JUPITER": 9
        }
        
        # Test IN operator
        self.assertTrue(evaluate_condition("MOON", "IN", 1, "BASIC", planet_house_mapping))
        self.assertFalse(evaluate_condition("MOON", "IN", 2, "BASIC", planet_house_mapping))

        # Test NOT IN operator
        self.assertTrue(evaluate_condition("MOON", "NOT IN", 2, "BASIC", planet_house_mapping))
        self.assertFalse(evaluate_condition("MOON", "NOT IN", 1, "BASIC", planet_house_mapping))

        # Test planet not in mapping
        self.assertFalse(evaluate_condition("VENUS", "IN", 7, "BASIC", planet_house_mapping))

    def test_evaluate_parsed_query(self):
        """Test evaluating a parsed query."""
        # Setup planet-house mapping
        planet_house_mapping = {
            "SUN": 5,
            "MOON": 1,
            "MARS": 10,
            "JUPITER": 9
        }
        
        # Test single condition
        parsed_query = [("SINGLE", ("MOON", "IN", 1, "BASIC"))]
        self.assertTrue(evaluate_parsed_query(parsed_query, planet_house_mapping))

        # Test OR conditions (one true)
        parsed_query = [
            ("SINGLE", ("MOON", "IN", 2, "BASIC")),  # False
            ("SINGLE", ("MARS", "IN", 10, "BASIC"))  # True
        ]
        self.assertTrue(evaluate_parsed_query(parsed_query, planet_house_mapping))

        # Test OR conditions (all false)
        parsed_query = [
            ("SINGLE", ("MOON", "IN", 2, "BASIC")),  # False
            ("SINGLE", ("MARS", "IN", 11, "BASIC"))  # False
        ]
        self.assertFalse(evaluate_parsed_query(parsed_query, planet_house_mapping))

        # Test AND conditions (all true)
        parsed_query = [
            ("AND", [("MOON", "IN", 1, "BASIC"), ("SUN", "IN", 5, "BASIC")])
        ]
        self.assertTrue(evaluate_parsed_query(parsed_query, planet_house_mapping))

        # Test AND conditions (one false)
        parsed_query = [
            ("AND", [("MOON", "IN", 1, "BASIC"), ("SUN", "IN", 6, "BASIC")])
        ]
        self.assertFalse(evaluate_parsed_query(parsed_query, planet_house_mapping))

        # Test mixed conditions
        parsed_query = [
            ("AND", [("MOON", "IN", 1, "BASIC"), ("SUN", "IN", 6, "BASIC")]),  # False
            ("SINGLE", ("MARS", "IN", 10, "BASIC"))  # True
        ]
        self.assertTrue(evaluate_parsed_query(parsed_query, planet_house_mapping))

    def test_get_planet_house_mapping(self):
        """Test extracting planet-house mapping from chart data."""
        # Setup mock chart data with correct structure
        chart_data = {
            "chart_data": {
                "D1": {
                    "houses": [
                        {
                            "house_number": 1,
                            "planets": ["moon", "rahu"]
                        },
                        {
                            "house_number": 5,
                            "planets": ["sun"]
                        },
                        {
                            "house_number": 10,
                            "planets": ["mars"]
                        }
                    ]
                }
            }
        }

        expected = {
            "MOON": 1,
            "RAHU": 1,
            "SUN": 5,
            "MARS": 10
        }

        self.assertEqual(get_planet_house_mapping(chart_data), expected)

        # Test with empty chart data
        self.assertEqual(get_planet_house_mapping({}), {})

        # Test with missing chart type
        self.assertEqual(get_planet_house_mapping(chart_data, "D9"), {})

        # Test with planets_precise structure
        chart_data_precise = {
            "chart_data": {
                "D1": {
                    "lagna": {"sign": "MESHAM"},
                    "planets_precise": {
                        "sun": {"sign": "SIMMAM"},  # Leo = 5th house from Aries
                        "moon": {"sign": "MESHAM"}  # Aries = 1st house from Aries
                    }
                }
            }
        }

        expected_precise = {
            "SUN": 5,
            "MOON": 1
        }

        result = get_planet_house_mapping(chart_data_precise)
        self.assertEqual(result, expected_precise)


if __name__ == "__main__":
    unittest.main()
