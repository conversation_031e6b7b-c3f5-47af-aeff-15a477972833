# 📖 **COMPLETE QUERY FORMAT REFERENCE GUIDE**

## 🎯 **RULE ENGINE - ALL SUPPORTED QUERY PATTERNS**

This document provides a comprehensive reference for all supported query patterns in the Fortune Lens Rule Engine. Each pattern includes format specifications, examples, and expected responses.

---

## 🔗 **API ENDPOINT**

**Unified Endpoint**: `POST {{base_url}}/api/rule-engine/`

**Request Body**:
```json
{
  "user_profile_id": "1",
  "member_profile_id": "1", 
  "query": "YOUR_QUERY_HERE",
  "chart_type": "D1"
}
```

---

## 📋 **ALL SUPPORTED QUERY PATTERNS**

### **1. 🏠 HOUSE TO HOUSE PLANET RELATIONSHIPS**

#### **Format**: `"#th_House_Planet IS RELATED TO #th_House_Planet"`

#### **Purpose**: Check relationships between planets in different houses

#### **Examples**:
```bash
# Basic house to house
"1st_House_Planet IS RELATED TO 7th_House_Planet"
"10th_House_Planet IS RELATED TO 11th_House_Planet"
"6th_House_Planet IS RELATED TO 12th_House_Planet"

# With ordinal numbers
"1st_House_Planet IS RELATED TO 2nd_House_Planet"
"3rd_House_Planet IS RELATED TO 9th_House_Planet"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "house_to_house_planet",
  "overall_result": true,
  "bidirectional_analysis": {
    "forward_analysis": {
      "relationships": { "together": true, "basic_position": false },
      "count": 1,
      "direction": "House 10 → House 11"
    },
    "reverse_analysis": {
      "relationships": { "together": false, "basic_position": false },
      "count": 0,
      "direction": "House 11 → House 10"
    },
    "combined_scoring": {
      "forward_points": 1,
      "reverse_points": 0,
      "total_points": 1,
      "max_possible_points": 10,
      "calculation": "Forward (1) + Reverse (0) = Total (1) out of 10 possible"
    }
  }
}
```

---

### **2. 🌟 PLANET TO HOUSE PLANET RELATIONSHIPS**

#### **Format**: `"Planet IS RELATED TO #th_House_Planet"`

#### **Purpose**: Check relationships between a specific planet and planets in a house

#### **Examples**:
```bash
# Planet to house planets
"Mars IS RELATED TO 1st_House_Planet"
"Jupiter IS RELATED TO 5th_House_Planet"
"Saturn IS RELATED TO 10th_House_Planet"
"Ketu IS RELATED TO 12th_House_Planet"
"Venus IS RELATED TO 7th_House_Planet"

# All planets supported
"Sun IS RELATED TO 1st_House_Planet"
"Moon IS RELATED TO 4th_House_Planet"
"Mercury IS RELATED TO 3rd_House_Planet"
"Rahu IS RELATED TO 6th_House_Planet"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "planet_to_house_planet",
  "overall_result": true,
  "result": {
    "bidirectional_analysis": {
      "forward_relationships": { "nakshatra": true, "aspecting": false },
      "reverse_relationships": { "nakshatra": false, "aspecting": false },
      "forward_count": 1,
      "reverse_count": 0,
      "total_count": 1
    }
  }
}
```

---

### **3. 👑 PLANET TO HOUSE RULING PLANET RELATIONSHIPS**

#### **Format**: `"Planet IS RELATED TO #th_House_Ruling_Planet"` or `"Planet IS RELATED TO #th House_Ruling_Planet"`

#### **Purpose**: Check relationships between a planet and a house ruling planet

#### **Examples**:
```bash
# With underscore format
"Ketu IS RELATED TO 6th_House_Ruling_Planet"
"Mars IS RELATED TO 1st_House_Ruling_Planet"
"Jupiter IS RELATED TO 9th_House_Ruling_Planet"
"Venus IS RELATED TO 7th_House_Ruling_Planet"

# With space format
"Saturn IS RELATED TO 10th House_Ruling_Planet"
"Mercury IS RELATED TO 3rd House_Ruling_Planet"
"Sun IS RELATED TO 1st House_Ruling_Planet"
"Moon IS RELATED TO 4th House_Ruling_Planet"

# All houses supported (1-12)
"Rahu IS RELATED TO 8th_House_Ruling_Planet"
"Ketu IS RELATED TO 12th_House_Ruling_Planet"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "planet_to_house_ruling_planet",
  "overall_result": true,
  "result": {
    "relationships": { "nakshatra": true, "basic_position": false },
    "bidirectional_analysis": {
      "forward_relationships": { "nakshatra": true },
      "reverse_relationships": { "nakshatra": false },
      "forward_count": 1,
      "reverse_count": 0,
      "total_count": 1
    },
    "house_ruling_planet": "SATURN",
    "planet_location": "House 7"
  }
}
```

---

### **4. 👑 HOUSE RULING PLANET TO HOUSE RULING PLANET RELATIONSHIPS**

#### **Format**: `"#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"`

#### **Purpose**: Check relationships between ruling planets of different houses

#### **Examples**:
```bash
# House ruling planet relationships
"6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet"
"1st_House_Ruling_Planet IS RELATED TO 7th_House_Ruling_Planet"
"9th_House_Ruling_Planet IS RELATED TO 5th_House_Ruling_Planet"
"2nd_House_Ruling_Planet IS RELATED TO 8th_House_Ruling_Planet"

# All combinations possible
"3rd_House_Ruling_Planet IS RELATED TO 11th_House_Ruling_Planet"
"4th_House_Ruling_Planet IS RELATED TO 12th_House_Ruling_Planet"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "house_ruling_planet_to_house_ruling_planet",
  "overall_result": false,
  "house1_ruling_planet": "SATURN",
  "house2_ruling_planet": "MERCURY",
  "bidirectional_analysis": {
    "forward_analysis": {
      "relationships": { "basic_position": false, "nakshatra": false },
      "count": 0,
      "direction": "SATURN (House 6 ruling) → MERCURY (House 10 ruling)"
    },
    "reverse_analysis": {
      "relationships": { "basic_position": false, "nakshatra": false },
      "count": 0,
      "direction": "MERCURY (House 10 ruling) → SATURN (House 6 ruling)"
    }
  }
}
```

---

### **5. 🏠 HOUSE RULING PLANET IN HOUSE (NEW PATTERN)**

#### **Format**: `"#th_House_Ruling_Planet in #th_House"`

#### **Purpose**: Check if a house ruling planet is located in a specific house

#### **Examples**:
```bash
# House ruling planet placement
"6th_House_Ruling_Planet in 10th_House"
"6th_House_Ruling_Planet in 12th_House"
"1st_House_Ruling_Planet in 1st_House"
"9th_House_Ruling_Planet in 5th_House"

# All house combinations
"2nd_House_Ruling_Planet in 8th_House"
"7th_House_Ruling_Planet in 7th_House"
"11th_House_Ruling_Planet in 3rd_House"
"12th_House_Ruling_Planet in 6th_House"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "house_ruling_planet_in_house",
  "overall_result": true,
  "ruling_planet": "SATURN",
  "target_house": 12,
  "placement_analysis": {
    "is_placed": true,
    "actual_house": 12,
    "explanation": "✅ PLACEMENT CONFIRMED: SATURN, the ruling planet of House 6, is currently located in House 12"
  },
  "bidirectional_analysis": {
    "combined_scoring": {
      "forward_points": 1,
      "reverse_points": 0,
      "total_points": 1,
      "max_possible_points": 2,
      "success_percentage": 50.0
    }
  }
}
```

---

### **6. 👑 HOUSE RULING PLANET CONJUNCTION (NEW PATTERN)**

#### **Format**: `"#th_House_Ruling_Planet in #th_House_Ruling_Planet"`

#### **Purpose**: Check if two house ruling planets are conjunct (in same house)

#### **Examples**:
```bash
# House ruling planet conjunctions
"6th_House_Ruling_Planet in 10th_House_Ruling_Planet"
"1st_House_Ruling_Planet in 1st_House_Ruling_Planet"
"9th_House_Ruling_Planet in 5th_House_Ruling_Planet"
"2nd_House_Ruling_Planet in 8th_House_Ruling_Planet"

# Self-conjunction (same house)
"7th_House_Ruling_Planet in 7th_House_Ruling_Planet"
"11th_House_Ruling_Planet in 11th_House_Ruling_Planet"

# Cross-house conjunctions
"3rd_House_Ruling_Planet in 11th_House_Ruling_Planet"
"4th_House_Ruling_Planet in 12th_House_Ruling_Planet"
```

#### **Response Structure**:
```json
{
  "success": true,
  "query_type": "house_ruling_planet_conjunction",
  "overall_result": true,
  "source_ruling_planet": "MERCURY",
  "target_ruling_planet": "MERCURY",
  "conjunction_analysis": {
    "is_conjunct": true,
    "source_planet_house": 8,
    "target_planet_house": 8,
    "same_planet": true,
    "explanation": "✅ CONJUNCTION CONFIRMED: MERCURY (House 1 ruling planet) and MERCURY (House 1 ruling planet) are conjunct in House 8"
  },
  "bidirectional_analysis": {
    "combined_scoring": {
      "forward_points": 1,
      "reverse_points": 1,
      "total_points": 2,
      "max_possible_points": 2,
      "success_percentage": 100.0
    }
  }
}
```

---

## 🔗 **LOGICAL OPERATORS**

### **Supported Operators**: `OR`, `AND`, `NOT`

#### **OR Queries**:
```bash
# Simple OR
"Ketu IS RELATED TO 6th_House_Ruling_Planet OR Mars IS RELATED TO 1st_House_Ruling_Planet"

# Mixed patterns with OR
"6th_House_Ruling_Planet in 12th_House OR Ketu IS RELATED TO 6th_House_Ruling_Planet"

# Multiple OR
"1st_House_Planet IS RELATED TO 7th_House_Planet OR 6th_House_Ruling_Planet in 10th_House OR Mars IS RELATED TO 1st_House_Planet"
```

#### **AND Queries**:
```bash
# Simple AND
"Ketu IS RELATED TO 6th_House_Ruling_Planet AND Mars IS RELATED TO 1st_House_Ruling_Planet"

# Mixed patterns with AND
"6th_House_Ruling_Planet in 12th_House AND 10th_House_Planet IS RELATED TO 11th_House_Planet"

# Multiple AND
"1st_House_Ruling_Planet in 1st_House_Ruling_Planet AND Ketu IS RELATED TO 6th_House_Ruling_Planet AND Mars IS RELATED TO 1st_House_Planet"
```

#### **NOT Queries**:
```bash
# Simple NOT
"NOT 6th_House_Planet IS RELATED TO 10th_House_Planet"
"NOT 6th_House_Ruling_Planet in 10th_House"

# NOT with other operators
"Ketu IS RELATED TO 6th_House_Ruling_Planet AND NOT 6th_House_Planet IS RELATED TO 10th_House_Planet"
"NOT 6th_House_Ruling_Planet in 10th_House OR 6th_House_Ruling_Planet in 12th_House"
```

#### **Complex Logical Expressions**:
```bash
# Complex combinations
"(6th_House_Ruling_Planet in 12th_House OR Ketu IS RELATED TO 6th_House_Ruling_Planet) AND 10th_House_Planet IS RELATED TO 11th_House_Planet"

# Multiple operators
"1st_House_Ruling_Planet in 1st_House_Ruling_Planet OR Mars IS RELATED TO 1st_House_Planet AND NOT 6th_House_Planet IS RELATED TO 10th_House_Planet"
```

---

## 📊 **RESPONSE STRUCTURE FOR LOGICAL QUERIES**

```json
{
  "success": true,
  "query_type": "comprehensive_relationship_with_logic",
  "overall_result": true,
  "logical_evaluation": {
    "total_sub_queries": 2,
    "sub_queries_true": 1,
    "sub_queries_false": 1,
    "operators_used": ["OR"],
    "final_logical_result": true
  },
  "true_count_analysis": {
    "overall_true_statements": 1,
    "forward_count": 1,
    "reverse_count": 0,
    "total_true_count": 1,
    "calculation": "Forward (1) + Reverse (0) = Total (1)"
  },
  "individual_results": [
    {
      "query_part": "6th_House_Ruling_Planet in 10th_House",
      "query_type": "house_ruling_planet_in_house",
      "overall_result": false
    },
    {
      "query_part": "6th_House_Ruling_Planet in 12th_House", 
      "query_type": "house_ruling_planet_in_house",
      "overall_result": true
    }
  ]
}
```

---

## 🎯 **PLANET NAMES SUPPORTED**

```bash
# All planets supported in queries
"Sun", "Moon", "Mars", "Mercury", "Jupiter", "Venus", "Saturn", "Rahu", "Ketu"

# Case insensitive
"sun", "MARS", "Jupiter", "ketu" # All work
```

---

## 🏠 **HOUSE NUMBERS SUPPORTED**

```bash
# All houses 1-12 supported
"1st_House", "2nd_House", "3rd_House", "4th_House", "5th_House", "6th_House"
"7th_House", "8th_House", "9th_House", "10th_House", "11th_House", "12th_House"

# Ordinal variations supported
"1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th", "11th", "12th"
```

---

## 📋 **RELATIONSHIP TYPES ANALYZED**

For all relationship queries, these 5 types are checked:

1. **Basic Position**: Planet in target house or ruling planet's house
2. **WITH Ruling Planet**: Planet with target's ruling planet
3. **Together**: Planets in same house (conjunction)
4. **Nakshatra**: Planet in target's nakshatra or ruling planet's nakshatra
5. **Aspecting**: Planet aspecting target or ruling planet

---

## 🔍 **BIDIRECTIONAL ANALYSIS**

All queries include:
- **Forward Analysis**: Original query direction
- **Reverse Analysis**: Opposite direction analysis  
- **Combined Scoring**: Forward + Reverse points
- **Detailed Explanations**: WHY TRUE/FALSE for each relationship type

---

## ⚠️ **ERROR HANDLING**

### **Invalid Query Formats**:
```json
{
  "success": false,
  "message": "Invalid query format. Expected: \"Planet IS RELATED TO #th_House_Planet\"",
  "error_code": "INVALID_QUERY_FORMAT"
}
```

### **Chart Data Not Found**:
```json
{
  "success": false,
  "message": "Chart data not found",
  "error_code": "CHART_DATA_NOT_FOUND"
}
```

---

## 🚀 **PRODUCTION READY FEATURES**

✅ **6 Complete Query Patterns** with bidirectional analysis
✅ **Logical Operators** (OR, AND, NOT) for all patterns
✅ **Mixed Queries** combining different pattern types
✅ **Detailed Explanations** for every TRUE/FALSE result
✅ **Comprehensive Scoring** with success rates and ratings
✅ **Single Unified Endpoint** for all query types
✅ **Consistent Response Structure** across all patterns
✅ **Complete Error Handling** with specific error codes

**The Fortune Lens Rule Engine supports comprehensive astrological relationship analysis with 6 query patterns, logical operators, and detailed bidirectional analysis!** 🎯✨

---

## 📝 **COMPLETE EXAMPLES COLLECTION**

### **🎯 WORKING EXAMPLES FOR EACH PATTERN**

#### **Pattern 1 Examples**:
```bash
# House to House Planet Relationships
"1st_House_Planet IS RELATED TO 7th_House_Planet"    # Marriage/partnership analysis
"10th_House_Planet IS RELATED TO 11th_House_Planet"  # Career and gains
"4th_House_Planet IS RELATED TO 8th_House_Planet"    # Property and transformation
"5th_House_Planet IS RELATED TO 9th_House_Planet"    # Education and wisdom
"2nd_House_Planet IS RELATED TO 12th_House_Planet"   # Wealth and expenses
```

#### **Pattern 2 Examples**:
```bash
# Planet to House Planet Relationships
"Mars IS RELATED TO 1st_House_Planet"     # Mars influence on personality
"Jupiter IS RELATED TO 5th_House_Planet"  # Jupiter's effect on children/education
"Saturn IS RELATED TO 10th_House_Planet"  # Saturn's impact on career
"Venus IS RELATED TO 7th_House_Planet"    # Venus influence on marriage
"Mercury IS RELATED TO 3rd_House_Planet"  # Communication and siblings
```

#### **Pattern 3 Examples**:
```bash
# Planet to House Ruling Planet Relationships
"Ketu IS RELATED TO 6th_House_Ruling_Planet"   # Ketu's effect on health/enemies
"Rahu IS RELATED TO 8th_House_Ruling_Planet"   # Rahu's impact on transformation
"Sun IS RELATED TO 1st_House_Ruling_Planet"    # Sun's influence on self
"Moon IS RELATED TO 4th_House_Ruling_Planet"   # Moon's effect on home/mother
"Mars IS RELATED TO 10th_House_Ruling_Planet"  # Mars influence on career
```

#### **Pattern 4 Examples**:
```bash
# House Ruling Planet to House Ruling Planet
"1st_House_Ruling_Planet IS RELATED TO 7th_House_Ruling_Planet"  # Self vs partner
"6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet" # Health vs career
"9th_House_Ruling_Planet IS RELATED TO 5th_House_Ruling_Planet"  # Wisdom vs creativity
"2nd_House_Ruling_Planet IS RELATED TO 11th_House_Ruling_Planet" # Wealth vs gains
```

#### **Pattern 5 Examples (NEW)**:
```bash
# House Ruling Planet in House Placement
"6th_House_Ruling_Planet in 12th_House"    # Health ruler in expenses house
"9th_House_Ruling_Planet in 1st_House"     # Wisdom ruler in self house
"7th_House_Ruling_Planet in 10th_House"    # Marriage ruler in career house
"5th_House_Ruling_Planet in 9th_House"     # Education ruler in wisdom house
```

#### **Pattern 6 Examples (NEW)**:
```bash
# House Ruling Planet Conjunction
"1st_House_Ruling_Planet in 7th_House_Ruling_Planet"  # Self ruler with marriage ruler
"6th_House_Ruling_Planet in 8th_House_Ruling_Planet"  # Health ruler with transformation ruler
"9th_House_Ruling_Planet in 5th_House_Ruling_Planet"  # Wisdom ruler with education ruler
"2nd_House_Ruling_Planet in 11th_House_Ruling_Planet" # Wealth ruler with gains ruler
```

### **🔗 LOGICAL OPERATOR EXAMPLES**

#### **OR Examples**:
```bash
# Simple OR
"Mars IS RELATED TO 1st_House_Planet OR Venus IS RELATED TO 7th_House_Planet"

# Mixed patterns OR
"6th_House_Ruling_Planet in 12th_House OR Ketu IS RELATED TO 6th_House_Ruling_Planet"

# Multiple OR
"1st_House_Planet IS RELATED TO 7th_House_Planet OR 6th_House_Ruling_Planet in 10th_House OR Mars IS RELATED TO 1st_House_Planet"
```

#### **AND Examples**:
```bash
# Simple AND
"Jupiter IS RELATED TO 5th_House_Planet AND 9th_House_Ruling_Planet IS RELATED TO 5th_House_Ruling_Planet"

# Mixed patterns AND
"6th_House_Ruling_Planet in 12th_House AND 10th_House_Planet IS RELATED TO 11th_House_Planet"

# Multiple AND
"Sun IS RELATED TO 1st_House_Planet AND Moon IS RELATED TO 4th_House_Planet AND Mars IS RELATED TO 10th_House_Planet"
```

#### **NOT Examples**:
```bash
# Simple NOT
"NOT 6th_House_Planet IS RELATED TO 8th_House_Planet"
"NOT 6th_House_Ruling_Planet in 10th_House"

# NOT with AND
"Mars IS RELATED TO 1st_House_Planet AND NOT 6th_House_Planet IS RELATED TO 12th_House_Planet"

# NOT with OR
"NOT 6th_House_Ruling_Planet in 10th_House OR 6th_House_Ruling_Planet in 12th_House"
```

#### **Complex Combinations**:
```bash
# All operators together
"(6th_House_Ruling_Planet in 12th_House OR Ketu IS RELATED TO 6th_House_Ruling_Planet) AND NOT 8th_House_Planet IS RELATED TO 12th_House_Planet"

# Multiple mixed patterns
"1st_House_Ruling_Planet in 7th_House_Ruling_Planet OR Mars IS RELATED TO 1st_House_Planet AND Jupiter IS RELATED TO 5th_House_Planet"
```

---

## 🎯 **QUICK REFERENCE SUMMARY**

### **All 6 Patterns**:
1. `"#th_House_Planet IS RELATED TO #th_House_Planet"`
2. `"Planet IS RELATED TO #th_House_Planet"`
3. `"Planet IS RELATED TO #th_House_Ruling_Planet"`
4. `"#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"`
5. `"#th_House_Ruling_Planet in #th_House"` 🆕
6. `"#th_House_Ruling_Planet in #th_House_Ruling_Planet"` 🆕

### **All Logical Operators**: `OR`, `AND`, `NOT`

### **All Planets**: `Sun`, `Moon`, `Mars`, `Mercury`, `Jupiter`, `Venus`, `Saturn`, `Rahu`, `Ketu`

### **All Houses**: `1st` through `12th` (all ordinal formats supported)

### **Single Endpoint**: `POST {{base_url}}/api/rule-engine/`

**Complete astrological relationship analysis with 6 patterns, logical operators, and bidirectional analysis!** 🌟
