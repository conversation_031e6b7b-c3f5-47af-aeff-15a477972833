#!/usr/bin/env python3
"""
Test Complex Ketu Query with API
Tests: "Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet"
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

# Sample MongoDB chart data structure for reference
SAMPLE_CHART_DATA = {
    "chart_data": {
        "D1": {
            "houses": [
                {"house_number": 1, "house_name": "MESHAM", "planets": ["sun", "mercury"]},
                {"house_number": 2, "house_name": "RIS<PERSON><PERSON><PERSON>", "planets": ["venus"]},
                {"house_number": 3, "house_name": "MIDUNAM", "planets": []},
                {"house_number": 4, "house_name": "<PERSON><PERSON><PERSON><PERSON>", "planets": ["moon"]},
                {"house_number": 5, "house_name": "SIMMAM", "planets": []},
                {"house_number": 6, "house_name": "KUMBAM", "planets": ["ketu"]},      # Aquarius - ruled by <PERSON>
                {"house_number": 7, "house_name": "THULAM", "planets": []},
                {"house_number": 8, "house_name": "VIRICHIGAM", "planets": []},
                {"house_number": 9, "house_name": "DHANUSU", "planets": ["jupiter"]},
                {"house_number": 10, "house_name": "MAGARAM", "planets": []},          # Capricorn - ruled by Saturn
                {"house_number": 11, "house_name": "KANNI", "planets": ["mars", "saturn"]},
                {"house_number": 12, "house_name": "MEENAM", "planets": ["rahu"]}
            ]
        }
    }
}

# Planet positions from sample data
PLANET_POSITIONS = {
    "SUN": 1, "MERCURY": 1, "VENUS": 2, "MOON": 4, "KETU": 6,
    "JUPITER": 9, "MARS": 11, "SATURN": 11, "RAHU": 12
}

def analyze_query_logic():
    """Analyze the query logic step by step"""
    print("=" * 80)
    print("QUERY ANALYSIS: Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet")
    print("=" * 80)
    
    # House name to ruling planet mapping
    house_name_ruling_planets = {
        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
    }
    
    print("Current Planet Positions:")
    for planet, house in PLANET_POSITIONS.items():
        print(f"  {planet}: House {house}")
    
    print("\nHouse Names and Ruling Planets:")
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        house_num = house["house_number"]
        house_name = house["house_name"]
        ruling_planet = house_name_ruling_planets.get(house_name)
        print(f"  House {house_num}: {house_name} (ruled by {ruling_planet})")
    
    print("\n" + "=" * 50)
    print("CONDITION 1: Ketu IN house6 WITH Ruling_Planet")
    print("=" * 50)
    
    # Condition 1: Ketu IN house6 WITH Ruling_Planet
    ketu_house = PLANET_POSITIONS.get("KETU")
    print(f"Step 1: Is Ketu in House 6? → Ketu is in House {ketu_house} → {'YES ✓' if ketu_house == 6 else 'NO ✗'}")
    
    if ketu_house == 6:
        # Find house 6 name
        house_6_name = None
        for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
            if house["house_number"] == 6:
                house_6_name = house["house_name"]
                break
        
        ruling_planet_6 = house_name_ruling_planets.get(house_6_name)
        ruling_planet_6_house = PLANET_POSITIONS.get(ruling_planet_6)
        
        print(f"Step 2: House 6 name → {house_6_name}")
        print(f"Step 3: Ruling planet of {house_6_name} → {ruling_planet_6}")
        print(f"Step 4: Is {ruling_planet_6} in House 6? → {ruling_planet_6} is in House {ruling_planet_6_house} → {'YES ✓' if ruling_planet_6_house == 6 else 'NO ✗'}")
        
        condition_1_result = ruling_planet_6_house == 6
    else:
        condition_1_result = False
    
    print(f"CONDITION 1 RESULT: {'TRUE ✓' if condition_1_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 50)
    print("CONDITION 2: Ketu IN house10 WITH Ruling_Planet")
    print("=" * 50)
    
    # Condition 2: Ketu IN house10 WITH Ruling_Planet
    print(f"Step 1: Is Ketu in House 10? → Ketu is in House {ketu_house} → {'YES ✓' if ketu_house == 10 else 'NO ✗'}")
    
    if ketu_house == 10:
        # Find house 10 name
        house_10_name = None
        for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
            if house["house_number"] == 10:
                house_10_name = house["house_name"]
                break
        
        ruling_planet_10 = house_name_ruling_planets.get(house_10_name)
        ruling_planet_10_house = PLANET_POSITIONS.get(ruling_planet_10)
        
        print(f"Step 2: House 10 name → {house_10_name}")
        print(f"Step 3: Ruling planet of {house_10_name} → {ruling_planet_10}")
        print(f"Step 4: Is {ruling_planet_10} in House 10? → {ruling_planet_10} is in House {ruling_planet_10_house} → {'YES ✓' if ruling_planet_10_house == 10 else 'NO ✗'}")
        
        condition_2_result = ruling_planet_10_house == 10
    else:
        condition_2_result = False
    
    print(f"CONDITION 2 RESULT: {'TRUE ✓' if condition_2_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 50)
    print("FINAL OR LOGIC")
    print("=" * 50)
    
    final_result = condition_1_result or condition_2_result
    print(f"Condition 1 OR Condition 2 → {condition_1_result} OR {condition_2_result} → {'TRUE ✓' if final_result else 'FALSE ✗'}")
    
    return final_result

def get_auth_token():
    """Try to get authentication token"""
    try:
        # Try to register a test user
        register_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "name": "Ketu Test User",
            "mobile": "9876543210"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code == 201:
            print("✓ Test user registered successfully")
        elif "already registered" in response.text:
            print("✓ Test user already exists")
        
        # Try to login
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print("✓ Authentication successful")
            return token
        else:
            print(f"✗ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Authentication error: {str(e)}")
        return None

def test_debug_chart(token):
    """Test debug chart to see actual data structure"""
    print("\n" + "=" * 80)
    print("TESTING DEBUG CHART ENDPOINT")
    print("=" * 80)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    data = {
        "user_profile_id": TEST_USER_ID,
        "member_profile_id": TEST_MEMBER_ID,
        "chart_type": "D1"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rule-engine/debug-chart", 
                               json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Debug chart successful")
            print(f"Chart data exists: {result.get('chart_data_exists')}")
            print(f"Available charts: {result.get('available_charts', [])}")
            print(f"Planets found: {result.get('planets_found', [])}")
            
            if result.get('houses_structure'):
                houses_info = result['houses_structure']
                print(f"Houses count: {houses_info.get('houses_count')}")
                if houses_info.get('sample_house'):
                    print(f"Sample house structure: {json.dumps(houses_info['sample_house'], indent=2)}")
            
            return True
        else:
            print(f"✗ Debug chart failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Debug chart error: {str(e)}")
        return False

def test_complex_ketu_query(token):
    """Test the complex Ketu query with API"""
    print("\n" + "=" * 80)
    print("TESTING COMPLEX KETU QUERY WITH API")
    print("=" * 80)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    # The complex query
    query = "Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet"
    
    data = {
        "user_profile_id": TEST_USER_ID,
        "member_profile_id": TEST_MEMBER_ID,
        "query": query,
        "chart_type": "D1"
    }
    
    print(f"Query: {query}")
    print(f"Request data: {json.dumps(data, indent=2)}")
    
    try:
        response = requests.post(f"{BASE_URL}/api/rule-engine/evaluate",
                               json=data, headers=headers)
        
        print(f"\nAPI Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Query evaluation successful")
            print(f"\nAPI Response:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print(f"\n🎯 FINAL RESULT: {result['result']}")
                print(f"Query: {result['query']}")
                print(f"Chart Type: {result['chart_type']}")
                
                if result.get('planet_positions'):
                    print(f"\nPlanet Positions:")
                    for planet, house in result['planet_positions'].items():
                        print(f"  {planet}: House {house}")
                
                return result['result']
            else:
                print(f"✗ Query evaluation failed: {result.get('message')}")
                return None
        else:
            print(f"✗ API Error {response.status_code}: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ API Exception: {str(e)}")
        return None

def test_individual_conditions(token):
    """Test individual conditions separately"""
    print("\n" + "=" * 80)
    print("TESTING INDIVIDUAL CONDITIONS")
    print("=" * 80)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    individual_queries = [
        "Ketu IN house6 WITH Ruling_Planet",
        "Ketu IN house10 WITH Ruling_Planet"
    ]
    
    results = []
    
    for i, query in enumerate(individual_queries, 1):
        print(f"\nCondition {i}: {query}")
        print("-" * 50)
        
        data = {
            "user_profile_id": TEST_USER_ID,
            "member_profile_id": TEST_MEMBER_ID,
            "query": query,
            "chart_type": "D1"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/evaluate",
                                   json=data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    condition_result = result['result']
                    results.append(condition_result)
                    print(f"Result: {'TRUE ✓' if condition_result else 'FALSE ✗'}")
                else:
                    print(f"Error: {result.get('message')}")
                    results.append(False)
            else:
                print(f"API Error: {response.status_code}")
                results.append(False)
                
        except Exception as e:
            print(f"Exception: {str(e)}")
            results.append(False)
    
    print(f"\n" + "=" * 50)
    print("INDIVIDUAL RESULTS SUMMARY")
    print("=" * 50)
    print(f"Condition 1 (Ketu IN house6 WITH Ruling_Planet): {'TRUE ✓' if results[0] else 'FALSE ✗'}")
    print(f"Condition 2 (Ketu IN house10 WITH Ruling_Planet): {'TRUE ✓' if results[1] else 'FALSE ✗'}")
    print(f"Expected OR Result: {'TRUE ✓' if (results[0] or results[1]) else 'FALSE ✗'}")
    
    return results

def main():
    """Main test function"""
    print("COMPLEX KETU QUERY API TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print(f"Base URL: {BASE_URL}")
    
    # Analyze query logic first
    expected_result = analyze_query_logic()
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print("\n✓ Server is running")
    except:
        print("\n✗ Server is not responding. Please start the Flask application.")
        return
    
    # Get authentication token
    print("\n" + "=" * 80)
    print("AUTHENTICATION")
    print("=" * 80)
    token = get_auth_token()
    
    if not token:
        print("⚠ Proceeding without authentication token (may cause failures)")
    
    # Test debug chart
    debug_success = test_debug_chart(token)
    
    # Test individual conditions
    individual_results = test_individual_conditions(token)
    
    # Test complex query
    api_result = test_complex_ketu_query(token)
    
    # Summary
    print("\n" + "=" * 80)
    print("FINAL SUMMARY")
    print("=" * 80)
    print(f"Expected Result (from analysis): {'TRUE ✓' if expected_result else 'FALSE ✗'}")
    if individual_results:
        expected_or = individual_results[0] or individual_results[1]
        print(f"Individual OR Result: {'TRUE ✓' if expected_or else 'FALSE ✗'}")
    if api_result is not None:
        print(f"API Result: {'TRUE ✓' if api_result else 'FALSE ✗'}")
        
        # Check consistency
        if api_result == expected_result:
            print("🎉 Results are CONSISTENT! ✓")
        else:
            print("⚠ Results are INCONSISTENT! ✗")
    else:
        print("❌ API test failed")

if __name__ == "__main__":
    main()
