#!/usr/bin/env python3
"""
Test Reverse Order Functionality
Tests that the system checks both directions automatically:
- 6th_House_Planet IS RELATED TO 10th_House_Planet
- 10th_House_Planet IS RELATED TO 6th_House_Planet
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

def test_reverse_order_functionality():
    """Test the reverse order functionality"""
    print("=" * 80)
    print("🔄 REVERSE ORDER TESTING")
    print("=" * 80)
    
    print("Your Query: '6th_House_Planet IS RELATED TO 10th_House_Planet'")
    print("\nNow the system automatically checks BOTH directions:")
    print("✅ Forward:  6th House Planet → 10th House Planet")
    print("✅ Reverse:  10th House Planet → 6th House Planet")
    
    print("\nThis ensures comprehensive relationship analysis by checking:")
    print("1. All planets in House 6 against all planets in House 10")
    print("2. All planets in House 10 against all planets in House 6")
    print("3. All 5 relationship types in both directions")
    
    # Expected results based on our chart data
    print("\n" + "=" * 60)
    print("EXPECTED RESULTS ANALYSIS")
    print("=" * 60)
    
    print("Chart Data Summary:")
    print("• House 6: Contains KETU")
    print("• House 10: Contains NO planets (empty)")
    
    print("\nFor 6th_House_Planet IS RELATED TO 10th_House_Planet:")
    print("Forward Direction (House 6 → House 10):")
    print("  • KETU (from House 6) → No planets in House 10")
    print("  • Result: No relationships to check")
    
    print("\nReverse Direction (House 10 → House 6):")
    print("  • No planets in House 10 → KETU (in House 6)")
    print("  • Result: No relationships to check")
    
    print("\nOverall Result: FALSE ❌ (No planets in House 10 for either direction)")

def show_better_examples():
    """Show better examples with planets in both houses"""
    print("\n" + "=" * 80)
    print("🔥 BETTER EXAMPLES WITH REVERSE ORDER")
    print("=" * 80)
    
    examples = [
        {
            "query": "1st_House_Planet IS RELATED TO 2nd_House_Planet",
            "house1_planets": ["SUN", "MERCURY"],
            "house2_planets": ["VENUS"],
            "forward_relationships": [
                "SUN (House 1) → VENUS (House 2)",
                "MERCURY (House 1) → VENUS (House 2)"
            ],
            "reverse_relationships": [
                "VENUS (House 2) → SUN (House 1)",
                "VENUS (House 2) → MERCURY (House 1)"
            ],
            "total_checks": "4 planet pair relationships (2 forward + 2 reverse)"
        },
        {
            "query": "9th_House_Planet IS RELATED TO 11th_House_Planet",
            "house1_planets": ["JUPITER"],
            "house2_planets": ["MARS", "SATURN"],
            "forward_relationships": [
                "JUPITER (House 9) → MARS (House 11)",
                "JUPITER (House 9) → SATURN (House 11)"
            ],
            "reverse_relationships": [
                "MARS (House 11) → JUPITER (House 9)",
                "SATURN (House 11) → JUPITER (House 9)"
            ],
            "total_checks": "4 planet pair relationships (2 forward + 2 reverse)"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 11th_House_Planet",
            "house1_planets": ["SUN", "MERCURY"],
            "house2_planets": ["MARS", "SATURN"],
            "forward_relationships": [
                "SUN (House 1) → MARS (House 11)",
                "SUN (House 1) → SATURN (House 11)",
                "MERCURY (House 1) → MARS (House 11)",
                "MERCURY (House 1) → SATURN (House 11)"
            ],
            "reverse_relationships": [
                "MARS (House 11) → SUN (House 1)",
                "MARS (House 11) → MERCURY (House 1)",
                "SATURN (House 11) → SUN (House 1)",
                "SATURN (House 11) → MERCURY (House 1)"
            ],
            "total_checks": "8 planet pair relationships (4 forward + 4 reverse)"
        }
    ]
    
    print("Examples showing comprehensive bidirectional checking:")
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['query']}")
        print(f"   House 1 Planets: {example['house1_planets']}")
        print(f"   House 2 Planets: {example['house2_planets']}")
        
        print(f"\n   Forward Direction Checks:")
        for rel in example['forward_relationships']:
            print(f"     • {rel}")
        
        print(f"\n   Reverse Direction Checks:")
        for rel in example['reverse_relationships']:
            print(f"     • {rel}")
        
        print(f"\n   Total: {example['total_checks']}")

def show_api_response_structure():
    """Show the enhanced API response structure with reverse order info"""
    print("\n" + "=" * 80)
    print("📊 ENHANCED API RESPONSE STRUCTURE")
    print("=" * 80)
    
    print("The API response now includes reverse order information:")
    
    enhanced_response = {
        "success": True,
        "query": "1st_House_Planet IS RELATED TO 2nd_House_Planet",
        "chart_type": "D1",
        "overall_result": True,
        "house1_planets": ["SUN", "MERCURY"],
        "house2_planets": ["VENUS"],
        "forward_direction": "House 1 → House 2",
        "reverse_direction": "House 2 → House 1",
        "planet_relationships": {
            "SUN_TO_VENUS": {
                "basic_position": True,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": True,
                "aspecting": False,
                "direction": "SUN (House 1) → VENUS (House 2)"
            },
            "MERCURY_TO_VENUS": {
                "basic_position": True,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": False,
                "aspecting": True,
                "direction": "MERCURY (House 1) → VENUS (House 2)"
            },
            "VENUS_TO_SUN": {
                "basic_position": False,
                "with_ruling_planet": True,
                "together": False,
                "nakshatra": False,
                "aspecting": True,
                "direction": "VENUS (House 2) → SUN (House 1)"
            },
            "VENUS_TO_MERCURY": {
                "basic_position": False,
                "with_ruling_planet": True,
                "together": False,
                "nakshatra": True,
                "aspecting": False,
                "direction": "VENUS (House 2) → MERCURY (House 1)"
            }
        },
        "summary": {
            "basic_position": True,
            "with_ruling_planet": True,
            "together": False,
            "nakshatra": True,
            "aspecting": True
        }
    }
    
    print(json.dumps(enhanced_response, indent=2))

def show_curl_tests():
    """Show curl commands for testing reverse order"""
    print("\n" + "=" * 80)
    print("🌐 CURL TEST COMMANDS")
    print("=" * 80)
    
    print("1. Test Your Original Query (with reverse order):")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "6th_House_Planet IS RELATED TO 10th_House_Planet", "chart_type": "D1"}\'')
    
    print("\n2. Test Better Example (with reverse order):")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "1st_House_Planet IS RELATED TO 2nd_House_Planet", "chart_type": "D1"}\'')
    
    print("\n3. Test Multiple Planets (with reverse order):")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "1st_House_Planet IS RELATED TO 11th_House_Planet", "chart_type": "D1"}\'')

def show_benefits():
    """Show the benefits of reverse order checking"""
    print("\n" + "=" * 80)
    print("✅ BENEFITS OF REVERSE ORDER CHECKING")
    print("=" * 80)
    
    benefits = [
        {
            "benefit": "Comprehensive Analysis",
            "description": "Checks all possible planet relationships in both directions"
        },
        {
            "benefit": "No Missing Relationships",
            "description": "Ensures no planet-to-planet relationship is overlooked"
        },
        {
            "benefit": "Bidirectional Aspects",
            "description": "Captures aspects from both planets to each other"
        },
        {
            "benefit": "Complete Nakshatra Analysis",
            "description": "Checks if either planet is in the other's nakshatra"
        },
        {
            "benefit": "Full Position Exchange",
            "description": "Verifies if planets are in each other's houses"
        },
        {
            "benefit": "Automatic Processing",
            "description": "No need to run separate queries for reverse direction"
        }
    ]
    
    print("Why reverse order checking is important:")
    
    for i, benefit in enumerate(benefits, 1):
        print(f"\n{i}. {benefit['benefit']}:")
        print(f"   {benefit['description']}")

def main():
    """Main testing function"""
    print("REVERSE ORDER FUNCTIONALITY TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing automatic reverse order checking for comprehensive analysis")
    
    # Run all demonstrations
    test_reverse_order_functionality()
    show_better_examples()
    show_api_response_structure()
    show_curl_tests()
    show_benefits()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ Reverse order checking implemented")
    print("✅ Both directions checked automatically")
    print("✅ Enhanced API response with direction info")
    print("✅ Comprehensive bidirectional analysis")
    print("✅ No missing planet relationships")
    
    print("\n🎯 YOUR QUERY ENHANCEMENT:")
    print("✅ '6th_House_Planet IS RELATED TO 10th_House_Planet'")
    print("✅ Now checks: House 6 → House 10 AND House 10 → House 6")
    print("✅ All 5 relationship types in both directions")
    print("✅ Complete comprehensive analysis")
    
    print("\n📋 NEXT STEPS:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test your query with automatic reverse order checking")
    print("4. Try better examples to see bidirectional relationships")
    print("5. Review enhanced response structure")
    print("6. Verify comprehensive analysis results")

if __name__ == "__main__":
    main()
