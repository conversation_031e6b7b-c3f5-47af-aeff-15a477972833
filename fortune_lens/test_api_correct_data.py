#!/usr/bin/env python3
"""
Test API with Correct Database Data
Verify the API endpoints are working with actual database data
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"

def get_auth_token():
    """Get authentication token"""
    print("🔐 Getting authentication token...")
    
    login_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"✅ Authentication successful")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error during authentication: {e}")
        return None

def test_your_original_query(token):
    """Test your original query with correct data"""
    print("\n" + "=" * 80)
    print("🎯 TESTING YOUR ORIGINAL QUERY")
    print("=" * 80)
    
    query_data = {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
        "chart_type": "D1"
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"Query: {query_data['query']}")
    print("Expected: FALSE (House 6 is empty)")
    
    try:
        response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship", 
                               json=query_data, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"\n✅ API Response received")
            print(f"Success: {data.get('success')}")
            print(f"Overall Result: {data.get('overall_result')}")
            print(f"House 6 Planets: {data.get('house1_planets')}")
            print(f"House 10 Planets: {data.get('house2_planets')}")
            print(f"Planet Relationships: {len(data.get('planet_relationships', {}))}")
            
            # Verify correct behavior
            if data.get('success') and not data.get('overall_result'):
                if not data.get('house1_planets'):  # House 6 empty
                    print("✅ CORRECT: House 6 is empty, result is FALSE")
                else:
                    print("❌ WRONG: House 6 should be empty")
                    
                if data.get('house2_planets'):  # House 10 has planets
                    print("✅ CORRECT: House 10 has planets")
                else:
                    print("❌ WRONG: House 10 should have planets")
            else:
                print("❌ Unexpected result")
                
            return data
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing query: {e}")
        return None

def test_same_house_together(token):
    """Test same house together with correct data"""
    print("\n" + "=" * 80)
    print("📍 TESTING SAME HOUSE TOGETHER")
    print("=" * 80)
    
    test_cases = [
        {
            "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
            "expected_planets": ["LAGNAM", "RAHU"],
            "house": "House 1"
        },
        {
            "query": "10th_House_Planet IS RELATED TO 10th_House_Planet", 
            "expected_planets": ["JUPITER", "VENUS"],
            "house": "House 10"
        }
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['query']}")
        print(f"Expected planets in {test_case['house']}: {test_case['expected_planets']}")
        
        query_data = {
            "user_profile_id": "1",
            "member_profile_id": "1",
            "query": test_case['query'],
            "chart_type": "D1"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship",
                                   json=query_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ API Response: Success={data.get('success')}, Result={data.get('overall_result')}")
                print(f"House Planets: {data.get('house1_planets')}")
                
                # Verify planets match expected
                actual_planets = data.get('house1_planets', [])
                expected_set = set(test_case['expected_planets'])
                actual_set = set(actual_planets)
                
                if expected_set == actual_set:
                    print("✅ CORRECT: Planets match expected data")
                else:
                    print(f"❌ WRONG: Expected {expected_set}, got {actual_set}")
                
                # Check together relationship
                if data.get('summary', {}).get('together'):
                    print("✅ CORRECT: Together relationship found")
                else:
                    print("❌ WRONG: Should have together relationship")
                    
                # Check same house together type
                if data.get('summary', {}).get('together_types', {}).get('same_house'):
                    print("✅ CORRECT: Same house together type detected")
                else:
                    print("❌ WRONG: Should detect same_house together type")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def test_cross_house_relationships(token):
    """Test cross-house relationships with correct data"""
    print("\n" + "=" * 80)
    print("🔗 TESTING CROSS-HOUSE RELATIONSHIPS")
    print("=" * 80)
    
    test_cases = [
        {
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "house1_planets": ["LAGNAM", "RAHU"],
            "house2_planets": ["JUPITER", "VENUS"],
            "expected_relationships": 4  # 2x2 = 4 cross-house relationships
        },
        {
            "query": "9th_House_Planet IS RELATED TO 11th_House_Planet",
            "house1_planets": ["SUN"],
            "house2_planets": ["MARS"],
            "expected_relationships": 2  # 1x1 = 2 relationships (bidirectional)
        }
    ]
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['query']}")
        print(f"Expected House 1: {test_case['house1_planets']}")
        print(f"Expected House 2: {test_case['house2_planets']}")
        print(f"Expected relationships: {test_case['expected_relationships']}")
        
        query_data = {
            "user_profile_id": "1",
            "member_profile_id": "1",
            "query": test_case['query'],
            "chart_type": "D1"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship",
                                   json=query_data, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"✅ API Response: Success={data.get('success')}, Result={data.get('overall_result')}")
                
                # Check house planets
                house1_actual = set(data.get('house1_planets', []))
                house2_actual = set(data.get('house2_planets', []))
                house1_expected = set(test_case['house1_planets'])
                house2_expected = set(test_case['house2_planets'])
                
                if house1_actual == house1_expected:
                    print(f"✅ CORRECT: House 1 planets match: {house1_actual}")
                else:
                    print(f"❌ WRONG: House 1 expected {house1_expected}, got {house1_actual}")
                    
                if house2_actual == house2_expected:
                    print(f"✅ CORRECT: House 2 planets match: {house2_actual}")
                else:
                    print(f"❌ WRONG: House 2 expected {house2_expected}, got {house2_actual}")
                
                # Check relationships
                relationships = data.get('planet_relationships', {})
                actual_count = len(relationships)
                
                print(f"Relationships found: {actual_count}")
                print(f"Relationship keys: {list(relationships.keys())}")
                
                if actual_count >= test_case['expected_relationships']:
                    print(f"✅ CORRECT: Found {actual_count} relationships (expected {test_case['expected_relationships']})")
                else:
                    print(f"❌ WRONG: Expected at least {test_case['expected_relationships']}, got {actual_count}")
                
                # Verify no same-house relationships in cross-house query
                same_house_found = False
                for rel_key in relationships.keys():
                    # Check if this is a same-house relationship
                    if any(p1 in test_case['house1_planets'] and p2 in test_case['house1_planets'] 
                           for p1 in [rel_key.split('_TO_')[0]] for p2 in [rel_key.split('_TO_')[1]]):
                        same_house_found = True
                        break
                    if any(p1 in test_case['house2_planets'] and p2 in test_case['house2_planets'] 
                           for p1 in [rel_key.split('_TO_')[0]] for p2 in [rel_key.split('_TO_')[1]]):
                        same_house_found = True
                        break
                
                if not same_house_found:
                    print("✅ CORRECT: No same-house relationships in cross-house query")
                else:
                    print("❌ WRONG: Found same-house relationships in cross-house query")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def main():
    """Main testing function"""
    print("API TESTING WITH CORRECT DATABASE DATA")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing API endpoints with actual database planet positions")
    
    # Wait for server to start
    print("\n⏳ Waiting for server to start...")
    time.sleep(3)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Cannot proceed without authentication token")
        return
    
    # Run tests
    test_your_original_query(token)
    test_same_house_together(token)
    test_cross_house_relationships(token)
    
    print("\n" + "=" * 80)
    print("📊 API TESTING SUMMARY")
    print("=" * 80)
    print("✅ API endpoints tested with correct database data")
    print("✅ Your original query verified")
    print("✅ Same house together relationships tested")
    print("✅ Cross-house relationships tested")
    print("✅ All tests use actual planet positions from database")

if __name__ == "__main__":
    main()
