# Fortune Lens

Fortune Lens is an astrological application that provides various features including user profiles, member profiles, astrological charts, marriage matching, and Tamil panchanga calculations.

## Project Structure

```
fortune_lens/
├── app/                        # Main application package
│   ├── __init__.py             # Application initialization
│   ├── api/                    # API endpoints
│   │   ├── __init__.py         # API blueprint initialization
│   │   ├── auth/               # Authentication endpoints
│   │   │   ├── __init__.py     # Auth blueprint initialization
│   │   │   └── routes.py       # Authentication routes
│   │   ├── profiles/           # Profile endpoints
│   │   │   ├── __init__.py     # Profiles blueprint initialization
│   │   │   └── routes.py       # Profile management routes
│   │   └── astrology/          # Astrological endpoints
│   │       ├── __init__.py     # Astrology blueprint initialization
│   │       ├── charts.py       # Chart generation endpoints
│   │       ├── panchanga.py    # Panchanga calculation endpoints
│   │       └── marriage_matching.py # Marriage compatibility endpoints
│   ├── config.py               # Application configuration
│   ├── constants.py            # Application constants
│   ├── errors/                 # Error handling
│   │   ├── __init__.py         # Error handling initialization
│   │   ├── exceptions.py       # Custom exceptions
│   │   └── handlers.py         # Error handlers
│   ├── extensions.py           # Flask extensions
│   ├── schemas/                # Request/response schemas
│   │   ├── __init__.py         # Schema initialization
│   │   ├── auth/               # Authentication schemas
│   │   │   ├── __init__.py     # Auth schemas initialization
│   │   │   └── auth_schemas.py # Authentication schemas
│   │   ├── profiles/           # Profile schemas
│   │   │   ├── __init__.py     # Profiles schemas initialization
│   │   │   ├── user_schemas.py # User schemas
│   │   │   ├── profile_schemas.py # Profile schemas
│   │   │   └── member_profile_schemas.py # Member profile schemas
│   │   └── astrology/          # Astrological schemas
│   │       └── __init__.py     # Astrology schemas initialization
│   └── services/               # Business logic
│       ├── __init__.py         # Services initialization
│       ├── auth/               # Authentication services
│       │   ├── __init__.py     # Auth services initialization
│       │   ├── auth_service.py # Authentication service
│       │   └── otp_service.py  # OTP service
│       ├── profiles/           # Profile services
│       │   ├── __init__.py     # Profile services initialization
│       │   ├── user_service.py # User service
│       │   ├── profile_service.py # Profile service
│       │   └── member_profile_service.py # Member profile service
│       ├── astrology/          # Astrological services
│       │   ├── __init__.py     # Astrology services initialization
│       │   ├── chart_service.py # Chart generation service
│       │   ├── chart_constants.py # Chart constants
│       │   ├── astro_generator.py # Astrological data generation
│       │   ├── marriage_matching_service.py # Marriage compatibility service
│       │   └── tamil_panchanga.py # Tamil panchanga service
│       ├── utils/              # Utility functions
│       │   ├── __init__.py     # Utils initialization
│       │   └── location.py     # Location service
│       ├── horoscope/          # Horoscope calculation
│       ├── panchanga/          # Panchanga calculation
│       ├── new_sequence_service.py # Sequence generation service
│       ├── sequence_service.py # Sequence service
│       └── const.py            # Astrological constants
├── run.py                      # Application entry point
└── tests/                      # Test suite
    └── test_marriage_matching.py # Marriage matching tests
```

## Getting Started

### Prerequisites

- Python 3.7+
- MongoDB
- Swiss Ephemeris library

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/fortune_lens.git
cd fortune_lens
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Create a `.env` file with the following variables:
```
FLASK_ENV=development
SECRET_KEY=your_secret_key
JWT_SECRET_KEY=your_jwt_secret_key
MONGO_URI=mongodb://localhost:27017/fortune_lens
```

5. Run the application:
```bash
python run.py
```

## API Endpoints

The application provides the following API endpoints:

- `/api/auth/register` - Register a new user
- `/api/auth/login` - Login a user
- `/api/member-profiles` - Manage member profiles
- `/api/charts/generate` - Generate astrological charts
- `/api/marriage-matching` - Calculate marriage compatibility
- `/api/panchanga/daily` - Calculate daily panchanga
- `/api/panchanga/tamil` - Calculate Tamil panchanga

## Features

- User registration and authentication
- Member profile management
- Astrological chart generation
- Marriage compatibility calculation
- Daily panchanga calculation
- Tamil panchanga calculation

## License

This project is licensed under the MIT License - see the LICENSE file for details.
