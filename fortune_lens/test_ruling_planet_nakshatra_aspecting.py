#!/usr/bin/env python3
"""
Test Ruling Planet Nakshatra and Aspecting Logic
Tests that nakshatra and aspecting conditions only check ruling planets of houses
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"

def test_ruling_planet_logic():
    """Test the ruling planet logic for all 5 relationship types"""
    print("=" * 80)
    print("🔗 TESTING ALL 5 RELATIONSHIP TYPES WITH RULING PLANET LOGIC")
    print("=" * 80)
    
    print("ALL 5 RELATIONSHIP TYPES NOW USE RULING PLANET LOGIC:")
    print("\n1. 📍 Basic Position: Planet from house1 in house2 (or vice versa)")
    print("2. 🔗 WITH Ruling Planet: Planet in house WITH house's ruling planet")
    print("3. 🤝 Together: Ruling planets of house numbers in same/different houses")
    print("4. ⭐ Nakshatra: Ruling planet of house1 in nakshatra ruled by ruling planet of house2")
    print("5. 👁️ Aspecting: Ruling planet of house1 aspects ruling planet of house2")
    
    # Test cases to verify ruling planet logic
    test_cases = [
        {
            "query": "10th_House_Planet IS RELATED TO 11th_House_Planet",
            "description": "Houses with same ruling planet (SATURN)",
            "house1": 10,
            "house2": 11,
            "house1_ruling": "SATURN",
            "house2_ruling": "SATURN",
            "expected_together": True,
            "analysis": "Both ruled by SATURN (in House 12) - should be together"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 8th_House_Planet",
            "description": "Houses with same ruling planet (MARS)",
            "house1": 1,
            "house2": 8,
            "house1_ruling": "MARS",
            "house2_ruling": "MARS",
            "expected_together": True,
            "analysis": "Both ruled by MARS (in House 11) - should be together"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "description": "Houses with different ruling planets",
            "house1": 1,
            "house2": 10,
            "house1_ruling": "MARS",
            "house2_ruling": "SATURN",
            "expected_together": False,
            "analysis": "MARS (House 11) ≠ SATURN (House 12) - should NOT be together"
        }
    ]
    
    print("\n" + "=" * 60)
    print("TESTING RULING PLANET LOGIC FOR ALL 5 TYPES")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['query']}")
        print(f"Description: {test_case['description']}")
        print(f"Analysis: {test_case['analysis']}")
        
        # Make API request
        data = {
            "user_profile_id": "1",
            "member_profile_id": "1",
            "query": test_case['query'],
            "chart_type": "D1"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship", 
                                   json=data, headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                result = response.json()
                summary = result.get('summary', {})
                
                print(f"\n📊 RESULTS:")
                print(f"   Overall Result: {result.get('overall_result')}")
                print(f"   1. Basic Position: {summary.get('basic_position')}")
                print(f"   2. WITH Ruling Planet: {summary.get('with_ruling_planet')}")
                print(f"   3. Together: {summary.get('together')}")
                print(f"   4. Nakshatra: {summary.get('nakshatra')}")
                print(f"   5. Aspecting: {summary.get('aspecting')}")
                
                # Check together result
                actual_together = summary.get('together', False)
                if actual_together == test_case['expected_together']:
                    print(f"✅ Together result correct: {actual_together}")
                else:
                    print(f"❌ Together result wrong: expected {test_case['expected_together']}, got {actual_together}")
                
                # Show together types
                together_types = summary.get('together_types', {})
                print(f"   Together Types: {together_types}")
                
                # Show relationship details
                relationships = result.get('planet_relationships', {})
                print(f"   Total Relationships: {len(relationships)}")
                
                # Show details for each relationship type
                details = result.get('details', {})
                for rel_type in ['basic_position', 'with_ruling_planet', 'together', 'nakshatra', 'aspecting']:
                    if rel_type in details and details[rel_type]:
                        print(f"\n   {rel_type.upper()} Details:")
                        for detail in details[rel_type][:2]:  # Show first 2 details
                            print(f"     • {detail}")
                
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

def show_ruling_planet_analysis():
    """Show detailed ruling planet analysis for nakshatra and aspecting"""
    print("\n" + "=" * 80)
    print("📊 RULING PLANET ANALYSIS FOR NAKSHATRA & ASPECTING")
    print("=" * 80)
    
    print("NAKSHATRA LOGIC (Ruling Planet Based):")
    print("✅ OLD: Check if Planet A is in nakshatra ruled by Planet B")
    print("✅ NEW: Check if Ruling Planet of House A is in nakshatra ruled by Ruling Planet of House B")
    print("\nExample:")
    print("   • Query: '1st_House_Planet IS RELATED TO 5th_House_Planet'")
    print("   • OLD: Check if LAGNAM/RAHU in nakshatra ruled by MOON")
    print("   • NEW: Check if MARS (House 1 ruler) in nakshatra ruled by SUN (House 5 ruler)")
    
    print("\nASPECTING LOGIC (Ruling Planet Based):")
    print("✅ OLD: Check if Planet A aspects house where Planet B is")
    print("✅ NEW: Check if Ruling Planet of House A aspects Ruling Planet of House B")
    print("\nExample:")
    print("   • Query: '1st_House_Planet IS RELATED TO 10th_House_Planet'")
    print("   • OLD: Check if LAGNAM/RAHU aspects House 10")
    print("   • NEW: Check if MARS (House 1 ruler in House 11) aspects SATURN (House 10 ruler in House 12)")
    
    print("\n" + "=" * 60)
    print("HOUSE RULING PLANET LOCATIONS")
    print("=" * 60)
    
    house_rulers = {
        1: {"ruling_planet": "MARS", "location": "House 11"},
        2: {"ruling_planet": "VENUS", "location": "House 10"},
        3: {"ruling_planet": "MERCURY", "location": "House 8"},
        4: {"ruling_planet": "MOON", "location": "House 5"},
        5: {"ruling_planet": "SUN", "location": "House 9"},
        6: {"ruling_planet": "MERCURY", "location": "House 8"},
        7: {"ruling_planet": "VENUS", "location": "House 10"},
        8: {"ruling_planet": "MARS", "location": "House 11"},
        9: {"ruling_planet": "JUPITER", "location": "House 10"},
        10: {"ruling_planet": "SATURN", "location": "House 12"},
        11: {"ruling_planet": "SATURN", "location": "House 12"},
        12: {"ruling_planet": "JUPITER", "location": "House 10"}
    }
    
    print("Ruling Planet Positions for Nakshatra & Aspecting Analysis:")
    for house_num, info in house_rulers.items():
        print(f"House {house_num:2d}: {info['ruling_planet']:7s} → {info['location']}")
    
    print("\n🔗 NAKSHATRA EXAMPLES:")
    print("   • If MARS (House 1 ruler) is in nakshatra ruled by SATURN (House 10 ruler)")
    print("   • Then House 1 and House 10 have nakshatra relationship")
    
    print("\n👁️ ASPECTING EXAMPLES:")
    print("   • If MARS (in House 11) aspects House 12 (where SATURN is)")
    print("   • Then House 1 and House 10 have aspecting relationship")

def main():
    """Main testing function"""
    print("RULING PLANET LOGIC FOR NAKSHATRA & ASPECTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing that nakshatra and aspecting only use ruling planet logic")
    
    # Show analysis
    show_ruling_planet_analysis()
    
    # Test API
    test_ruling_planet_logic()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ ALL 5 RELATIONSHIP TYPES NOW USE RULING PLANET LOGIC:")
    print("   1. ✅ Basic Position: Planet positions (unchanged)")
    print("   2. ✅ WITH Ruling Planet: Planet WITH house ruling planet (unchanged)")
    print("   3. ✅ Together: Ruling planets in same/different houses (updated)")
    print("   4. ✅ Nakshatra: Ruling planet nakshatras (updated)")
    print("   5. ✅ Aspecting: Ruling planet aspects (updated)")
    
    print("\n🎯 CONSISTENT RULING PLANET LOGIC:")
    print("✅ Together: Ruling planet positions")
    print("✅ Nakshatra: Ruling planet nakshatras")
    print("✅ Aspecting: Ruling planet aspects")
    print("✅ Pure astrological house ruling planet analysis")

if __name__ == "__main__":
    main()
