#!/usr/bin/env python3
"""
Test Enhanced Together Functionality
Tests the two types of "together" relationships:
1. Same House Together - Two planets in the same house
2. Different House Together - Two planets in different but astrologically connected houses
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

def test_together_types():
    """Test the enhanced together functionality"""
    print("=" * 80)
    print("🤝 ENHANCED TOGETHER FUNCTIONALITY TESTING")
    print("=" * 80)
    
    print("Enhanced 'Together' now includes TWO types:")
    print("\n1. 📍 Same House Together:")
    print("   • Two planets physically placed in the same house")
    print("   • Example: Sun and Mercury both in House 1")
    
    print("\n2. 🔗 Different House Together:")
    print("   • Two planets in different houses but astrologically connected")
    print("   • Opposite Houses: 1-7, 2-8, 3-9, 4-10, 5-11, 6-12 (6 houses apart)")
    print("   • Trine Houses: 1-5-9, 2-6-10, 3-7-11, 4-8-12 (4 or 8 houses apart)")
    print("   • Square Houses: 1-4-7-10, 2-5-8-11, 3-6-9-12 (3 or 9 houses apart)")
    
    # Expected results based on our chart data
    print("\n" + "=" * 60)
    print("EXPECTED RESULTS ANALYSIS")
    print("=" * 60)
    
    print("Chart Data Summary:")
    print("• House 1: SUN, MERCURY (Same House Together)")
    print("• House 2: VENUS")
    print("• House 6: KETU")
    print("• House 9: JUPITER")
    print("• House 11: MARS, SATURN (Same House Together)")
    
    print("\nFor 1st_House_Planet IS RELATED TO 2nd_House_Planet:")
    print("Same House Together:")
    print("  • SUN and MERCURY: Both in House 1 ✅")
    print("  • No planets together in House 2")
    
    print("\nDifferent House Together:")
    print("  • House 1 and House 2: Adjacent houses (1 apart) - No special connection")
    print("  • No opposite, trine, or square relationships")
    
    print("\nFor 1st_House_Planet IS RELATED TO 7th_House_Planet:")
    print("Different House Together:")
    print("  • House 1 and House 7: Opposite houses (6 apart) ✅")
    print("  • If planets exist in both houses, they would be 'Opposite Houses Together'")

def show_together_types_details():
    """Show detailed explanation of together types"""
    print("\n" + "=" * 80)
    print("📋 TOGETHER TYPES DETAILED EXPLANATION")
    print("=" * 80)
    
    together_types = [
        {
            "type": "Same House Together",
            "description": "Two planets physically in the same house",
            "examples": [
                "Sun and Mercury in House 1",
                "Mars and Saturn in House 11",
                "Any two planets sharing the same house"
            ],
            "astrological_significance": "Strong conjunction energy, planets influence each other directly"
        },
        {
            "type": "Opposite Houses Together",
            "description": "Planets 6 houses apart (180° opposition)",
            "examples": [
                "House 1 ↔ House 7 (Self ↔ Partnership)",
                "House 2 ↔ House 8 (Resources ↔ Transformation)",
                "House 3 ↔ House 9 (Communication ↔ Wisdom)",
                "House 4 ↔ House 10 (Home ↔ Career)",
                "House 5 ↔ House 11 (Creativity ↔ Gains)",
                "House 6 ↔ House 12 (Service ↔ Liberation)"
            ],
            "astrological_significance": "Opposition aspect, complementary energies, balance and tension"
        },
        {
            "type": "Trine Houses Together",
            "description": "Planets 4 or 8 houses apart (120° trine)",
            "examples": [
                "House 1 ↔ House 5 ↔ House 9 (Fire Trine)",
                "House 2 ↔ House 6 ↔ House 10 (Earth Trine)",
                "House 3 ↔ House 7 ↔ House 11 (Air Trine)",
                "House 4 ↔ House 8 ↔ House 12 (Water Trine)"
            ],
            "astrological_significance": "Harmonious aspect, supportive energy, natural flow"
        },
        {
            "type": "Square Houses Together",
            "description": "Planets 3 or 9 houses apart (90° square)",
            "examples": [
                "House 1 ↔ House 4 ↔ House 7 ↔ House 10 (Cardinal Square)",
                "House 2 ↔ House 5 ↔ House 8 ↔ House 11 (Fixed Square)",
                "House 3 ↔ House 6 ↔ House 9 ↔ House 12 (Mutable Square)"
            ],
            "astrological_significance": "Challenging aspect, dynamic tension, growth through conflict"
        }
    ]
    
    for i, together_type in enumerate(together_types, 1):
        print(f"\n{i}. {together_type['type']}:")
        print(f"   Description: {together_type['description']}")
        print(f"   Examples:")
        for example in together_type['examples']:
            print(f"     • {example}")
        print(f"   Significance: {together_type['astrological_significance']}")

def show_api_response_structure():
    """Show the enhanced API response structure with together types"""
    print("\n" + "=" * 80)
    print("📊 ENHANCED API RESPONSE STRUCTURE")
    print("=" * 80)
    
    print("The API response now includes detailed together type information:")
    
    enhanced_response = {
        "success": True,
        "query": "1st_House_Planet IS RELATED TO 11th_House_Planet",
        "overall_result": True,
        "house1_planets": ["SUN", "MERCURY"],
        "house2_planets": ["MARS", "SATURN"],
        "summary": {
            "basic_position": True,
            "with_ruling_planet": False,
            "together": True,
            "together_types": {
                "same_house": True,
                "opposite_houses": False,
                "trine_houses": False,
                "square_houses": False
            },
            "nakshatra": True,
            "aspecting": True
        },
        "planet_relationships": {
            "SUN_TO_MERCURY": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": True,
                "together_type": "same_house",
                "nakshatra": False,
                "aspecting": False,
                "direction": "SUN (House 1) → MERCURY (House 1)"
            },
            "MARS_TO_SATURN": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": True,
                "together_type": "same_house",
                "nakshatra": False,
                "aspecting": False,
                "direction": "MARS (House 11) → SATURN (House 11)"
            }
        }
    }
    
    print(json.dumps(enhanced_response, indent=2))

def show_test_scenarios():
    """Show different test scenarios for together types"""
    print("\n" + "=" * 80)
    print("🧪 TEST SCENARIOS FOR TOGETHER TYPES")
    print("=" * 80)
    
    scenarios = [
        {
            "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
            "expected_together_type": "same_house",
            "description": "Sun and Mercury both in House 1",
            "expected_result": True
        },
        {
            "query": "11th_House_Planet IS RELATED TO 11th_House_Planet",
            "expected_together_type": "same_house",
            "description": "Mars and Saturn both in House 11",
            "expected_result": True
        },
        {
            "query": "1st_House_Planet IS RELATED TO 7th_House_Planet",
            "expected_together_type": "opposite_houses",
            "description": "House 1 and House 7 are opposite (6 apart)",
            "expected_result": "Depends on planets in House 7"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 5th_House_Planet",
            "expected_together_type": "trine_houses",
            "description": "House 1 and House 5 are trine (4 apart)",
            "expected_result": "Depends on planets in House 5"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 4th_House_Planet",
            "expected_together_type": "square_houses",
            "description": "House 1 and House 4 are square (3 apart)",
            "expected_result": "Depends on planets in House 4"
        },
        {
            "query": "6th_House_Planet IS RELATED TO 12th_House_Planet",
            "expected_together_type": "opposite_houses",
            "description": "House 6 and House 12 are opposite (6 apart)",
            "expected_result": "Depends on planets in House 12"
        }
    ]
    
    print("Test scenarios to verify together types:")
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['query']}")
        print(f"   Expected Together Type: {scenario['expected_together_type']}")
        print(f"   Description: {scenario['description']}")
        print(f"   Expected Result: {scenario['expected_result']}")

def show_curl_tests():
    """Show curl commands for testing together types"""
    print("\n" + "=" * 80)
    print("🌐 CURL TEST COMMANDS")
    print("=" * 80)
    
    print("1. Test Same House Together:")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "1st_House_Planet IS RELATED TO 1st_House_Planet", "chart_type": "D1"}\'')
    
    print("\n2. Test Opposite Houses Together:")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "1st_House_Planet IS RELATED TO 7th_House_Planet", "chart_type": "D1"}\'')
    
    print("\n3. Test Trine Houses Together:")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print('  -d \'{"user_profile_id": "1", "member_profile_id": "1", "query": "1st_House_Planet IS RELATED TO 5th_House_Planet", "chart_type": "D1"}\'')

def main():
    """Main testing function"""
    print("ENHANCED TOGETHER FUNCTIONALITY TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing the two types of 'together' relationships")
    
    # Run all demonstrations
    test_together_types()
    show_together_types_details()
    show_api_response_structure()
    show_test_scenarios()
    show_curl_tests()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ Enhanced 'together' functionality implemented")
    print("✅ Two types of together relationships supported")
    print("✅ Same house together: Planets in same house")
    print("✅ Different house together: Opposite, trine, square relationships")
    print("✅ Detailed together type information in API response")
    print("✅ Comprehensive astrological relationship analysis")
    
    print("\n🎯 TOGETHER TYPES SUPPORTED:")
    print("✅ Same House Together: Planets in same house")
    print("✅ Opposite Houses Together: 6 houses apart (180°)")
    print("✅ Trine Houses Together: 4 or 8 houses apart (120°)")
    print("✅ Square Houses Together: 3 or 9 houses apart (90°)")
    
    print("\n📋 NEXT STEPS:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test same house together queries")
    print("4. Test different house together queries")
    print("5. Review enhanced together type information")
    print("6. Verify comprehensive astrological analysis")

if __name__ == "__main__":
    main()
