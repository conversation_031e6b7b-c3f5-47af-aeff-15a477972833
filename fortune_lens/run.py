#!/usr/bin/env python3
"""
Fortune Lens Application Entry Point

This module serves as the entry point for the Fortune Lens application.
It loads environment variables, creates the Flask application with the
appropriate configuration, and runs the server.
"""

import os
from dotenv import load_dotenv
from app import create_app

# Load environment variables from .env file
load_dotenv()

# Get configuration from environment variable
config_name = os.getenv('FLASK_ENV', 'development')

# Create Flask application
app = create_app(config_name)

if __name__ == '__main__':
    # Run the application
    port = int(os.getenv('PORT', 5003))
    host = '0.0.0.0'
    debug = config_name == 'development'

    print(f"Starting Fortune Lens application on {host}:{port} in {config_name} mode")
    app.run(host=host, port=port, debug=debug)
