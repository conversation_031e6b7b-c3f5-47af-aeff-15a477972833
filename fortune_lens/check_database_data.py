#!/usr/bin/env python3
"""
Check Database Data
Verify the actual chart data in MongoDB to see what planets are in which houses
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app import create_app
from app.services.rule_engine import get_chart_data, get_planets_in_house

def check_database_data():
    """Check the actual database data"""
    print("=" * 80)
    print("🔍 CHECKING ACTUAL DATABASE DATA")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            # Get chart data
            user_profile_id = "1"
            member_profile_id = "1"
            
            print(f"Fetching chart data for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}")
            
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            print("✅ Chart data found!")
            
            # Check what's in the chart data
            print("\n" + "=" * 60)
            print("CHART DATA STRUCTURE")
            print("=" * 60)
            
            if "chart_data" in chart_data:
                chart_info = chart_data["chart_data"]
                print(f"Available chart types: {list(chart_info.keys())}")
                
                if "D1" in chart_info:
                    d1_chart = chart_info["D1"]
                    print(f"D1 chart keys: {list(d1_chart.keys())}")
                    
                    if "houses" in d1_chart:
                        houses = d1_chart["houses"]
                        print(f"Number of houses: {len(houses)}")
                        
                        print("\n" + "=" * 60)
                        print("PLANETS IN EACH HOUSE")
                        print("=" * 60)
                        
                        for house_num in range(1, 13):
                            planets = get_planets_in_house(chart_data, house_num, "D1")
                            if planets:
                                print(f"House {house_num:2d}: {', '.join(planets)}")
                            else:
                                print(f"House {house_num:2d}: (empty)")
                        
                        print("\n" + "=" * 60)
                        print("DETAILED HOUSE STRUCTURE")
                        print("=" * 60)
                        
                        for i, house in enumerate(houses[:3]):  # Show first 3 houses as examples
                            print(f"\nHouse {i+1} structure:")
                            for key, value in house.items():
                                if key == "planets" and isinstance(value, list):
                                    print(f"  {key}: {value}")
                                elif not isinstance(value, (dict, list)):
                                    print(f"  {key}: {value}")
                                else:
                                    print(f"  {key}: {type(value).__name__}")
                    else:
                        print("❌ No 'houses' key in D1 chart")
                else:
                    print("❌ No D1 chart found")
            else:
                print("❌ No 'chart_data' key found")
            
            return True
            
        except Exception as e:
            print(f"❌ Error checking database data: {e}")
            import traceback
            traceback.print_exc()
            return False

def check_specific_houses():
    """Check specific houses mentioned in our tests"""
    print("\n" + "=" * 80)
    print("🎯 CHECKING SPECIFIC HOUSES FROM OUR TESTS")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            user_profile_id = "1"
            member_profile_id = "1"
            
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            # Check the houses we've been testing
            test_houses = [1, 2, 6, 9, 10, 11]
            
            print("Houses we've been testing in our examples:")
            
            for house_num in test_houses:
                planets = get_planets_in_house(chart_data, house_num, "D1")
                status = "✅" if planets else "❌"
                planet_list = ', '.join(planets) if planets else "(empty)"
                print(f"{status} House {house_num:2d}: {planet_list}")
            
            print("\n" + "=" * 60)
            print("ANALYSIS OF OUR ASSUMPTIONS")
            print("=" * 60)
            
            # Check our assumptions
            house1_planets = get_planets_in_house(chart_data, 1, "D1")
            house2_planets = get_planets_in_house(chart_data, 2, "D1")
            house6_planets = get_planets_in_house(chart_data, 6, "D1")
            house9_planets = get_planets_in_house(chart_data, 9, "D1")
            house10_planets = get_planets_in_house(chart_data, 10, "D1")
            house11_planets = get_planets_in_house(chart_data, 11, "D1")
            
            assumptions = [
                {
                    "assumption": "House 1 has SUN, MERCURY",
                    "actual": house1_planets,
                    "correct": set(house1_planets) == {"SUN", "MERCURY"} if house1_planets else False
                },
                {
                    "assumption": "House 2 has VENUS",
                    "actual": house2_planets,
                    "correct": set(house2_planets) == {"VENUS"} if house2_planets else False
                },
                {
                    "assumption": "House 6 has KETU",
                    "actual": house6_planets,
                    "correct": set(house6_planets) == {"KETU"} if house6_planets else False
                },
                {
                    "assumption": "House 9 has JUPITER",
                    "actual": house9_planets,
                    "correct": set(house9_planets) == {"JUPITER"} if house9_planets else False
                },
                {
                    "assumption": "House 10 is empty",
                    "actual": house10_planets,
                    "correct": len(house10_planets) == 0
                },
                {
                    "assumption": "House 11 has MARS, SATURN",
                    "actual": house11_planets,
                    "correct": set(house11_planets) == {"MARS", "SATURN"} if house11_planets else False
                }
            ]
            
            print("Checking our assumptions against actual data:")
            
            for assumption in assumptions:
                status = "✅" if assumption["correct"] else "❌"
                actual_str = ', '.join(assumption["actual"]) if assumption["actual"] else "(empty)"
                print(f"{status} {assumption['assumption']}")
                print(f"    Actual: {actual_str}")
                if not assumption["correct"]:
                    print(f"    ⚠️  ASSUMPTION IS WRONG!")
            
            return True
            
        except Exception as e:
            print(f"❌ Error checking specific houses: {e}")
            import traceback
            traceback.print_exc()
            return False

def suggest_correct_tests():
    """Suggest correct test cases based on actual data"""
    print("\n" + "=" * 80)
    print("💡 SUGGESTED CORRECT TEST CASES")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            user_profile_id = "1"
            member_profile_id = "1"
            
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            # Find houses with planets
            houses_with_planets = []
            
            for house_num in range(1, 13):
                planets = get_planets_in_house(chart_data, house_num, "D1")
                if planets:
                    houses_with_planets.append({
                        "house": house_num,
                        "planets": planets,
                        "count": len(planets)
                    })
            
            print("Houses that actually have planets:")
            for house_info in houses_with_planets:
                print(f"  House {house_info['house']}: {', '.join(house_info['planets'])} ({house_info['count']} planets)")
            
            print("\n" + "=" * 60)
            print("SUGGESTED TEST QUERIES")
            print("=" * 60)
            
            # Suggest good test cases
            suggestions = []
            
            # Find houses with multiple planets (good for same-house together tests)
            multi_planet_houses = [h for h in houses_with_planets if h["count"] > 1]
            if multi_planet_houses:
                for house_info in multi_planet_houses:
                    suggestions.append({
                        "query": f"{house_info['house']}th_House_Planet IS RELATED TO {house_info['house']}th_House_Planet",
                        "type": "Same house together test",
                        "expected": f"Should show relationships between {', '.join(house_info['planets'])}",
                        "planets": house_info['planets']
                    })
            
            # Find pairs of houses with planets (good for cross-house tests)
            if len(houses_with_planets) >= 2:
                for i in range(len(houses_with_planets)):
                    for j in range(i+1, min(i+3, len(houses_with_planets))):  # Limit to avoid too many suggestions
                        house1 = houses_with_planets[i]
                        house2 = houses_with_planets[j]
                        suggestions.append({
                            "query": f"{house1['house']}th_House_Planet IS RELATED TO {house2['house']}th_House_Planet",
                            "type": "Cross-house test",
                            "expected": f"Should show relationships between House {house1['house']} ({', '.join(house1['planets'])}) and House {house2['house']} ({', '.join(house2['planets'])})",
                            "planets": house1['planets'] + house2['planets']
                        })
            
            print("Based on actual data, here are good test queries:")
            
            for i, suggestion in enumerate(suggestions[:6], 1):  # Limit to 6 suggestions
                print(f"\n{i}. {suggestion['query']}")
                print(f"   Type: {suggestion['type']}")
                print(f"   Expected: {suggestion['expected']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error generating suggestions: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Main function"""
    print("DATABASE DATA VERIFICATION")
    print("=" * 80)
    print("Checking actual MongoDB data to verify our assumptions")
    
    # Check database data
    if check_database_data():
        check_specific_houses()
        suggest_correct_tests()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ Database data checked")
    print("✅ Assumptions verified against actual data")
    print("✅ Correct test cases suggested")
    print("\n💡 Use the suggested test queries based on actual chart data!")

if __name__ == "__main__":
    main()
