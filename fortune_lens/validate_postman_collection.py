#!/usr/bin/env python3
"""
Validate Postman Collection Structure
Ensures all test cases are properly structured and cover all requirements
"""

import json
import os
from datetime import datetime

def validate_postman_collection():
    """Validate the Postman collection structure and content"""
    
    collection_file = "Complete_Rule_Engine_Testing_Postman.json"
    
    if not os.path.exists(collection_file):
        print(f"❌ Collection file not found: {collection_file}")
        return False
    
    try:
        with open(collection_file, 'r') as f:
            collection = json.load(f)
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in collection file: {e}")
        return False
    
    print("=" * 80)
    print("POSTMAN COLLECTION VALIDATION")
    print("=" * 80)
    
    # Validate basic structure
    if "info" not in collection:
        print("❌ Missing 'info' section")
        return False
    
    if "item" not in collection:
        print("❌ Missing 'item' section")
        return False
    
    print(f"✅ Collection Name: {collection['info']['name']}")
    print(f"✅ Collection Description: {collection['info']['description']}")
    
    # Count test categories and individual tests
    categories = collection["item"]
    total_tests = 0
    
    print(f"\n📋 TEST CATEGORIES ({len(categories)} categories):")
    print("-" * 60)
    
    expected_categories = [
        "1. Authentication & Setup",
        "2. Basic Position Queries", 
        "3. Logical Operations (OR, AND, NOT)",
        "4. Advanced Ruling Planet Queries",
        "5. Nakshatra (Star) Queries",
        "6. Aspecting (Planetary Aspects) Queries",
        "7. Complex Combined Queries",
        "8. Error Handling & Edge Cases"
    ]
    
    found_categories = []
    
    for i, category in enumerate(categories, 1):
        category_name = category["name"]
        found_categories.append(category_name)
        
        if "item" in category:
            test_count = len(category["item"])
            total_tests += test_count
            print(f"{i}. {category_name}: {test_count} tests")
            
            # List individual tests
            for j, test in enumerate(category["item"], 1):
                test_name = test["name"]
                print(f"   {i}.{j} {test_name}")
        else:
            print(f"{i}. {category_name}: 0 tests")
    
    print(f"\n📊 SUMMARY:")
    print(f"✅ Total Categories: {len(categories)}")
    print(f"✅ Total Tests: {total_tests}")
    
    # Check for your specific requirements
    print(f"\n🎯 YOUR REQUIREMENTS CHECK:")
    
    requirement_1_found = False
    requirement_2_found = False
    ultimate_found = False
    
    for category in categories:
        if "item" in category:
            for test in category["item"]:
                test_name = test["name"]
                if "YOUR REQUIREMENT 1" in test_name:
                    requirement_1_found = True
                    print(f"✅ Requirement 1 Test Found: {test_name}")
                elif "YOUR REQUIREMENT 2" in test_name:
                    requirement_2_found = True
                    print(f"✅ Requirement 2 Test Found: {test_name}")
                elif "Ultimate: All 4 Requirements Combined" in test_name:
                    ultimate_found = True
                    print(f"✅ Ultimate Test Found: {test_name}")
    
    if not requirement_1_found:
        print("❌ Requirement 1 test not found")
    if not requirement_2_found:
        print("❌ Requirement 2 test not found")
    if not ultimate_found:
        print("❌ Ultimate combined test not found")
    
    # Validate query types coverage
    print(f"\n🔍 QUERY TYPES COVERAGE:")
    
    query_types_found = {
        "basic_position": False,
        "logical_or": False,
        "logical_and": False,
        "logical_not": False,
        "with_ruling_planet": False,
        "nakshatra": False,
        "aspecting": False,
        "complex_combined": False
    }
    
    for category in categories:
        if "item" in category:
            for test in category["item"]:
                if "request" in test and "body" in test["request"]:
                    body = test["request"]["body"]
                    if "raw" in body:
                        try:
                            request_data = json.loads(body["raw"])
                            query = request_data.get("query", "")
                            
                            # Check query types
                            if " IN house" in query and " OR " not in query and " AND " not in query:
                                query_types_found["basic_position"] = True
                            if " OR " in query:
                                query_types_found["logical_or"] = True
                            if " AND " in query:
                                query_types_found["logical_and"] = True
                            if "NOT " in query:
                                query_types_found["logical_not"] = True
                            if "WITH Ruling_Planet" in query:
                                query_types_found["with_ruling_planet"] = True
                            if "IN_STAR_OF" in query:
                                query_types_found["nakshatra"] = True
                            if "IS_ASPECTING" in query:
                                query_types_found["aspecting"] = True
                            if ("IN_STAR_OF" in query or "IS_ASPECTING" in query) and (" OR " in query or " AND " in query):
                                query_types_found["complex_combined"] = True
                                
                        except json.JSONDecodeError:
                            pass
    
    for query_type, found in query_types_found.items():
        status = "✅" if found else "❌"
        print(f"{status} {query_type.replace('_', ' ').title()}: {'Found' if found else 'Missing'}")
    
    # Validate test structure
    print(f"\n🔧 TEST STRUCTURE VALIDATION:")
    
    structure_issues = []
    
    for category in categories:
        if "item" in category:
            for test in category["item"]:
                test_name = test["name"]
                
                # Check required fields
                if "request" not in test:
                    structure_issues.append(f"Missing 'request' in test: {test_name}")
                    continue
                
                request = test["request"]
                
                if "method" not in request:
                    structure_issues.append(f"Missing 'method' in test: {test_name}")
                if "url" not in request:
                    structure_issues.append(f"Missing 'url' in test: {test_name}")
                if "header" not in request:
                    structure_issues.append(f"Missing 'header' in test: {test_name}")
                
                # Check for test scripts
                if "event" not in test:
                    structure_issues.append(f"Missing test scripts in: {test_name}")
    
    if structure_issues:
        print("❌ Structure Issues Found:")
        for issue in structure_issues[:5]:  # Show first 5 issues
            print(f"   - {issue}")
        if len(structure_issues) > 5:
            print(f"   ... and {len(structure_issues) - 5} more issues")
    else:
        print("✅ All tests have proper structure")
    
    # Final validation
    print(f"\n🎉 FINAL VALIDATION:")
    
    all_good = (
        len(categories) >= 8 and
        total_tests >= 25 and
        requirement_1_found and
        requirement_2_found and
        ultimate_found and
        all(query_types_found.values()) and
        len(structure_issues) == 0
    )
    
    if all_good:
        print("✅ COLLECTION IS COMPLETE AND READY FOR TESTING!")
        print("✅ All query types are covered")
        print("✅ Both your requirements are included")
        print("✅ Test structure is valid")
        print("✅ Ready for Postman import and execution")
    else:
        print("❌ Collection has issues that need to be addressed")
    
    print(f"\n📋 QUICK START:")
    print("1. Import 'Complete_Rule_Engine_Testing_Postman.json' into Postman")
    print("2. Set base_url variable to 'http://127.0.0.1:5003'")
    print("3. Start Flask server: python run.py")
    print("4. Run the entire collection or individual categories")
    print("5. Verify all tests pass, especially YOUR REQUIREMENT tests")
    
    return all_good

def main():
    """Main validation function"""
    print("POSTMAN COLLECTION VALIDATOR")
    print("=" * 80)
    print(f"Validation started at: {datetime.now()}")
    
    success = validate_postman_collection()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 VALIDATION SUCCESSFUL - COLLECTION IS READY!")
    else:
        print("❌ VALIDATION FAILED - PLEASE FIX ISSUES")
    print("=" * 80)
    
    return success

if __name__ == "__main__":
    main()
