#!/usr/bin/env python3
"""
Detailed Analysis with Scoring System
Provide detailed explanations for why each relationship type is TRUE/FALSE
and calculate total marks (1 mark per TRUE relationship type)
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
UNIFIED_ENDPOINT = f"{BASE_URL}/api/rule-engine/"

def analyze_query_with_scoring(query, description):
    """Analyze a query and provide detailed scoring breakdown"""
    print(f"\n{'='*80}")
    print(f"🔍 DETAILED ANALYSIS: {description}")
    print(f"{'='*80}")
    print(f"📝 Query: {query}")
    
    # Make API request
    data = {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": query,
        "chart_type": "D1"
    }
    
    try:
        response = requests.post(UNIFIED_ENDPOINT, 
                               json=data, headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                query_type = result.get('query_type', 'unknown')
                overall_result = result.get('overall_result', False)
                
                print(f"🎯 Query Type: {query_type}")
                print(f"🎯 Overall Result: {overall_result}")
                
                # Initialize scoring
                total_marks = 0
                max_marks = 5
                relationship_scores = {}
                
                # Extract relationship analysis based on query type
                if query_type == 'house_planet_relationship':
                    analyze_house_planet_relationship(result, relationship_scores)
                elif query_type == 'comprehensive_relationship':
                    analyze_comprehensive_relationship(result, relationship_scores)
                elif query_type == 'house_ruling_planet_relationship':
                    analyze_house_ruling_planet_relationship(result, relationship_scores)
                elif query_type == 'basic_rule_evaluation':
                    analyze_basic_rule_evaluation(result, relationship_scores)
                
                # Calculate total marks
                total_marks = sum(1 for score in relationship_scores.values() if score)
                
                # Display scoring summary
                print(f"\n📊 DETAILED SCORING BREAKDOWN:")
                print(f"{'='*60}")
                
                for i, (rel_type, score) in enumerate(relationship_scores.items(), 1):
                    mark = "✅ 1 MARK" if score else "❌ 0 MARKS"
                    print(f"{i}. {rel_type}: {score} → {mark}")
                
                print(f"{'='*60}")
                print(f"🏆 TOTAL MARKS: {total_marks}/{max_marks}")
                print(f"📈 SUCCESS RATE: {(total_marks/max_marks)*100:.1f}%")
                
                # Performance rating
                if total_marks == 5:
                    rating = "🌟 EXCELLENT - All relationships found!"
                elif total_marks >= 3:
                    rating = "⭐ GOOD - Strong relationships found!"
                elif total_marks >= 1:
                    rating = "🔸 MODERATE - Some relationships found!"
                else:
                    rating = "🔹 MINIMAL - No relationships found!"
                
                print(f"🎯 RATING: {rating}")
                
                return {
                    'query': query,
                    'overall_result': overall_result,
                    'total_marks': total_marks,
                    'max_marks': max_marks,
                    'relationship_scores': relationship_scores,
                    'success_rate': (total_marks/max_marks)*100
                }
            else:
                print(f"❌ API Error: {result.get('message', 'Unknown error')}")
                return None
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def analyze_house_planet_relationship(result, relationship_scores):
    """Analyze house to house planet relationship results"""
    summary = result.get('summary', {})
    details = result.get('details', {})
    house1_planets = result.get('house1_planets', [])
    house2_planets = result.get('house2_planets', [])
    house1_num = result.get('house1_number', 0)
    house2_num = result.get('house2_number', 0)
    
    print(f"\n🏠 HOUSE PLANET RELATIONSHIP ANALYSIS:")
    print(f"   House {house1_num}: {house1_planets if house1_planets else '(empty)'}")
    print(f"   House {house2_num}: {house2_planets if house2_planets else '(empty)'}")
    
    # Analyze each relationship type
    relationship_scores['1. Basic Position'] = summary.get('basic_position', False)
    relationship_scores['2. WITH Ruling Planet'] = summary.get('with_ruling_planet', False)
    relationship_scores['3. Together'] = summary.get('together', False)
    relationship_scores['4. Nakshatra'] = summary.get('nakshatra', False)
    relationship_scores['5. Aspecting'] = summary.get('aspecting', False)
    
    # Detailed explanations
    print(f"\n📋 DETAILED EXPLANATIONS:")
    
    # Basic Position
    basic_pos = relationship_scores['1. Basic Position']
    if basic_pos:
        print(f"✅ 1. Basic Position: TRUE - Planets from one house found in the other house")
    else:
        if not house1_planets or not house2_planets:
            print(f"❌ 1. Basic Position: FALSE - One or both houses are empty")
        else:
            print(f"❌ 1. Basic Position: FALSE - No planets from one house found in the other")
    
    # WITH Ruling Planet
    with_ruling = relationship_scores['2. WITH Ruling Planet']
    if with_ruling:
        print(f"✅ 2. WITH Ruling Planet: TRUE - Planets found WITH their house ruling planets")
    else:
        print(f"❌ 2. WITH Ruling Planet: FALSE - No planets found WITH house ruling planets")
    
    # Together
    together = relationship_scores['3. Together']
    together_types = summary.get('together_types', {})
    if together:
        if together_types.get('ruling_planets_same_house'):
            print(f"✅ 3. Together: TRUE - House ruling planets are in the same house")
        else:
            print(f"✅ 3. Together: TRUE - Houses are together through ruling planet logic")
    else:
        if together_types.get('ruling_planets_different_house'):
            print(f"❌ 3. Together: FALSE - House ruling planets are in different houses")
        else:
            print(f"❌ 3. Together: FALSE - Houses are not together")
    
    # Nakshatra
    nakshatra = relationship_scores['4. Nakshatra']
    if nakshatra:
        print(f"✅ 4. Nakshatra: TRUE - Ruling planets have nakshatra relationships")
    else:
        print(f"❌ 4. Nakshatra: FALSE - No nakshatra relationships between ruling planets")
    
    # Aspecting
    aspecting = relationship_scores['5. Aspecting']
    if aspecting:
        print(f"✅ 5. Aspecting: TRUE - Ruling planets are aspecting each other")
    else:
        print(f"❌ 5. Aspecting: FALSE - No aspecting relationships between ruling planets")

def analyze_comprehensive_relationship(result, relationship_scores):
    """Analyze planet to house planet relationship results"""
    relationships = result.get('relationships', {})
    
    if relationships:
        # Get the first house relationship (usually the main one)
        house_key = list(relationships.keys())[0]
        house_result = relationships[house_key]
        
        summary = house_result.get('summary', {})
        planet = house_result.get('planet', 'Unknown')
        target_house = house_result.get('target_house', 0)
        target_planets = house_result.get('target_house_planets', [])
        planet_house = house_result.get('planet_house', 0)
        
        print(f"\n🌟 PLANET TO HOUSE RELATIONSHIP ANALYSIS:")
        print(f"   Planet: {planet} (in House {planet_house})")
        print(f"   Target House {target_house}: {target_planets if target_planets else '(empty)'}")
        
        # Analyze each relationship type
        relationship_scores['1. Basic Position'] = summary.get('basic_position', False)
        relationship_scores['2. WITH Ruling Planet'] = summary.get('with_ruling_planet', False)
        relationship_scores['3. Together'] = summary.get('together', False)
        relationship_scores['4. Nakshatra'] = summary.get('nakshatra', False)
        relationship_scores['5. Aspecting'] = summary.get('aspecting', False)
        
        # Detailed explanations
        print(f"\n📋 DETAILED EXPLANATIONS:")
        
        # Basic Position
        basic_pos = relationship_scores['1. Basic Position']
        if basic_pos:
            print(f"✅ 1. Basic Position: TRUE - {planet} found in House {target_house} or vice versa")
        else:
            if not target_planets:
                print(f"❌ 1. Basic Position: FALSE - House {target_house} is empty")
            else:
                print(f"❌ 1. Basic Position: FALSE - {planet} not in House {target_house}")
        
        # WITH Ruling Planet
        with_ruling = relationship_scores['2. WITH Ruling Planet']
        if with_ruling:
            print(f"✅ 2. WITH Ruling Planet: TRUE - {planet} is WITH house ruling planets")
        else:
            print(f"❌ 2. WITH Ruling Planet: FALSE - {planet} not WITH house ruling planets")
        
        # Together
        together = relationship_scores['3. Together']
        together_types = summary.get('together_types', {})
        if together:
            if together_types.get('ruling_planets_same_house'):
                print(f"✅ 3. Together: TRUE - Ruling planets are in the same house")
            else:
                print(f"✅ 3. Together: TRUE - Planets are together through ruling planet logic")
        else:
            print(f"❌ 3. Together: FALSE - Ruling planets are in different houses")
        
        # Nakshatra
        nakshatra = relationship_scores['4. Nakshatra']
        if nakshatra:
            print(f"✅ 4. Nakshatra: TRUE - Ruling planets have nakshatra relationships")
        else:
            print(f"❌ 4. Nakshatra: FALSE - No nakshatra relationships found")
        
        # Aspecting
        aspecting = relationship_scores['5. Aspecting']
        if aspecting:
            print(f"✅ 5. Aspecting: TRUE - Ruling planets are aspecting each other")
        else:
            print(f"❌ 5. Aspecting: FALSE - No aspecting relationships found")

def analyze_house_ruling_planet_relationship(result, relationship_scores):
    """Analyze house ruling planet to house ruling planet relationship results"""
    house1_num = result.get('house1_number', 0)
    house2_num = result.get('house2_number', 0)
    house1_ruling = result.get('house1_ruling_planet', 'Unknown')
    house2_ruling = result.get('house2_ruling_planet', 'Unknown')
    relationships = result.get('relationships', {})
    
    print(f"\n👑 HOUSE RULING PLANET RELATIONSHIP ANALYSIS:")
    print(f"   House {house1_num} Ruling Planet: {house1_ruling}")
    print(f"   House {house2_num} Ruling Planet: {house2_ruling}")
    
    # Analyze each relationship type
    relationship_scores['1. Basic Position'] = relationships.get('basic_position', False)
    relationship_scores['2. WITH Ruling Planet'] = relationships.get('with_ruling_planet', False)
    relationship_scores['3. Together'] = relationships.get('together', False)
    relationship_scores['4. Nakshatra'] = relationships.get('nakshatra', False)
    relationship_scores['5. Aspecting'] = relationships.get('aspecting', False)
    
    # Detailed explanations
    print(f"\n📋 DETAILED EXPLANATIONS:")
    
    # Basic Position
    basic_pos = relationship_scores['1. Basic Position']
    if basic_pos:
        print(f"✅ 1. Basic Position: TRUE - {house1_ruling} and {house2_ruling} have positional relationships")
    else:
        print(f"❌ 1. Basic Position: FALSE - {house1_ruling} and {house2_ruling} have no positional relationships")
    
    # WITH Ruling Planet
    with_ruling = relationship_scores['2. WITH Ruling Planet']
    if with_ruling:
        print(f"✅ 2. WITH Ruling Planet: TRUE - Ruling planets are WITH other ruling planets")
    else:
        print(f"❌ 2. WITH Ruling Planet: FALSE - Ruling planets not WITH other ruling planets")
    
    # Together
    together = relationship_scores['3. Together']
    if together:
        print(f"✅ 3. Together: TRUE - {house1_ruling} and {house2_ruling} are in the same house")
    else:
        print(f"❌ 3. Together: FALSE - {house1_ruling} and {house2_ruling} are in different houses")
    
    # Nakshatra
    nakshatra = relationship_scores['4. Nakshatra']
    if nakshatra:
        print(f"✅ 4. Nakshatra: TRUE - {house1_ruling} and {house2_ruling} have nakshatra relationships")
    else:
        print(f"❌ 4. Nakshatra: FALSE - No nakshatra relationships between {house1_ruling} and {house2_ruling}")
    
    # Aspecting
    aspecting = relationship_scores['5. Aspecting']
    if aspecting:
        print(f"✅ 5. Aspecting: TRUE - {house1_ruling} and {house2_ruling} are aspecting each other")
    else:
        print(f"❌ 5. Aspecting: FALSE - No aspecting relationships between {house1_ruling} and {house2_ruling}")

def analyze_basic_rule_evaluation(result, relationship_scores):
    """Analyze basic rule evaluation results"""
    query = result.get('query', '')
    basic_result = result.get('result', False)
    
    print(f"\n🔧 BASIC RULE EVALUATION ANALYSIS:")
    print(f"   Rule: {query}")
    print(f"   Result: {basic_result}")
    
    # For basic rules, we only have one result
    relationship_scores['1. Basic Rule Result'] = basic_result
    relationship_scores['2. N/A'] = False
    relationship_scores['3. N/A'] = False
    relationship_scores['4. N/A'] = False
    relationship_scores['5. N/A'] = False
    
    print(f"\n📋 DETAILED EXPLANATIONS:")
    if basic_result:
        print(f"✅ 1. Basic Rule Result: TRUE - The rule condition is satisfied")
    else:
        print(f"❌ 1. Basic Rule Result: FALSE - The rule condition is not satisfied")
    
    print(f"❌ 2-5. N/A: Basic rules only have one result type")

def main():
    """Main analysis function"""
    print("🔍 DETAILED ANALYSIS WITH SCORING SYSTEM")
    print("=" * 80)
    print(f"Analysis started at: {datetime.now()}")
    print("Analyzing all your queries with detailed explanations and scoring")
    
    # Your specific queries to analyze
    queries_to_analyze = [
        {
            "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
            "description": "Your Original Query - House 6 to House 10"
        },
        {
            "query": "Ketu IS RELATED TO 10th_House_Planet", 
            "description": "Your Requested Query - Ketu to 10th House Planets"
        },
        {
            "query": "6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet",
            "description": "Your House Ruling Planet Query"
        },
        {
            "query": "10th_House_Planet IS RELATED TO 11th_House_Planet",
            "description": "Together Example - Houses with Same Ruling Planet"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 8th_House_Planet",
            "description": "Together Example - Houses with Same Ruling Planet (MARS)"
        }
    ]
    
    all_results = []
    
    for query_info in queries_to_analyze:
        result = analyze_query_with_scoring(query_info["query"], query_info["description"])
        if result:
            all_results.append(result)
    
    # Overall summary
    print(f"\n{'='*80}")
    print("🏆 OVERALL SCORING SUMMARY")
    print(f"{'='*80}")
    
    total_queries = len(all_results)
    total_marks_earned = sum(r['total_marks'] for r in all_results)
    total_marks_possible = sum(r['max_marks'] for r in all_results)
    overall_success_rate = (total_marks_earned / total_marks_possible) * 100 if total_marks_possible > 0 else 0
    
    for i, result in enumerate(all_results, 1):
        print(f"{i}. {result['query'][:50]}...")
        print(f"   Marks: {result['total_marks']}/{result['max_marks']} ({result['success_rate']:.1f}%)")
    
    print(f"\n🎯 GRAND TOTAL:")
    print(f"   Total Marks Earned: {total_marks_earned}")
    print(f"   Total Marks Possible: {total_marks_possible}")
    print(f"   Overall Success Rate: {overall_success_rate:.1f}%")
    
    if overall_success_rate >= 80:
        final_rating = "🌟 EXCELLENT - Strong astrological relationships!"
    elif overall_success_rate >= 60:
        final_rating = "⭐ GOOD - Moderate astrological relationships!"
    elif overall_success_rate >= 40:
        final_rating = "🔸 FAIR - Some astrological relationships!"
    else:
        final_rating = "🔹 MINIMAL - Limited astrological relationships!"
    
    print(f"   Final Rating: {final_rating}")
    
    print(f"\n{'='*80}")
    print("🎯 ANALYSIS COMPLETE")
    print(f"{'='*80}")

if __name__ == "__main__":
    main()
