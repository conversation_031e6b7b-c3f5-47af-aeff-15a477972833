# Fortune Lens Project Structure

This document provides a detailed explanation of the Fortune Lens project structure.

## Directory Structure

```
fortune_lens/
├── app/                        # Main application package
│   ├── __init__.py             # Application initialization
│   ├── api/                    # API endpoints
│   │   ├── __init__.py         # API blueprint initialization
│   │   ├── auth/               # Authentication endpoints
│   │   │   ├── __init__.py     # Auth blueprint initialization
│   │   │   └── routes.py       # Authentication routes
│   │   ├── profiles/           # Profile endpoints
│   │   │   ├── __init__.py     # Profiles blueprint initialization
│   │   │   └── routes.py       # Profile management routes
│   │   └── astrology/          # Astrological endpoints
│   │       ├── __init__.py     # Astrology blueprint initialization
│   │       ├── charts.py       # Chart generation endpoints
│   │       ├── panchanga.py    # Panchanga calculation endpoints
│   │       └── marriage_matching.py # Marriage compatibility endpoints
│   ├── config.py               # Application configuration
│   ├── constants.py            # Application constants
│   ├── errors/                 # Error handling
│   │   ├── __init__.py         # Error handling initialization
│   │   ├── exceptions.py       # Custom exceptions
│   │   └── handlers.py         # Error handlers
│   ├── extensions.py           # Flask extensions
│   ├── schemas/                # Request/response schemas
│   │   ├── __init__.py         # Schema initialization
│   │   ├── auth/               # Authentication schemas
│   │   │   ├── __init__.py     # Auth schemas initialization
│   │   │   └── auth_schemas.py # Authentication schemas
│   │   ├── profiles/           # Profile schemas
│   │   │   ├── __init__.py     # Profiles schemas initialization
│   │   │   ├── user_schemas.py # User schemas
│   │   │   ├── profile_schemas.py # Profile schemas
│   │   │   └── member_profile_schemas.py # Member profile schemas
│   │   └── astrology/          # Astrological schemas
│   │       └── __init__.py     # Astrology schemas initialization
│   └── services/               # Business logic
│       ├── __init__.py         # Services initialization
│       ├── auth/               # Authentication services
│       │   ├── __init__.py     # Auth services initialization
│       │   ├── auth_service.py # Authentication service
│       │   └── otp_service.py  # OTP service
│       ├── profiles/           # Profile services
│       │   ├── __init__.py     # Profile services initialization
│       │   ├── user_service.py # User service
│       │   ├── profile_service.py # Profile service
│       │   └── member_profile_service.py # Member profile service
│       ├── astrology/          # Astrological services
│       │   ├── __init__.py     # Astrology services initialization
│       │   ├── chart_service.py # Chart generation service
│       │   ├── chart_constants.py # Chart constants
│       │   ├── astro_generator.py # Astrological data generation
│       │   ├── marriage_matching_service.py # Marriage compatibility service
│       │   └── tamil_panchanga.py # Tamil panchanga service
│       ├── utils/              # Utility functions
│       │   ├── __init__.py     # Utils initialization
│       │   └── location.py     # Location service
│       ├── horoscope/          # Horoscope calculation
│       ├── panchanga/          # Panchanga calculation
│       ├── new_sequence_service.py # Sequence generation service
│       ├── sequence_service.py # Sequence service
│       └── const.py            # Astrological constants
├── run.py                      # Application entry point
└── tests/                      # Test suite
    └── test_marriage_matching.py # Marriage matching tests
```

## Key Components

### API Endpoints

The API endpoints are organized into three main blueprints:

1. **auth**: Authentication-related endpoints
   - `/api/auth/register` - Register a new user
   - `/api/auth/login` - Login a user
   - `/api/auth/refresh` - Refresh access token
   - `/api/auth/me` - Get current user information

2. **profiles**: User and member profile management
   - `/api/profiles/users/<user_id>` - Get, update, or delete a user
   - `/api/profiles/member-profiles` - Create or list member profiles
   - `/api/profiles/member-profiles/<profile_id>` - Get, update, or delete a member profile

3. **astrology**: Astrological features
   - `/api/astrology/charts/generate` - Generate astrological charts
   - `/api/astrology/charts/<member_profile_id>` - Get stored chart data
   - `/api/astrology/panchanga/daily` - Calculate daily panchanga
   - `/api/astrology/panchanga/tamil` - Calculate Tamil panchanga
   - `/api/astrology/marriage-matching` - Analyze marriage compatibility

### Services

The business logic is organized into service modules:

1. **auth**: Authentication services
   - `auth_service.py` - User authentication
   - `otp_service.py` - OTP generation and verification

2. **profiles**: User and member profile services
   - `user_service.py` - User management
   - `profile_service.py` - Profile management
   - `member_profile_service.py` - Member profile management

3. **astrology**: Astrological services
   - `chart_service.py` - Chart generation
   - `marriage_matching_service.py` - Marriage compatibility analysis
   - `tamil_panchanga.py` - Tamil panchanga calculations

4. **utils**: Utility functions
   - `location.py` - Location-related utilities

### Schemas

The schema validation modules are organized by functionality:

1. **auth**: Authentication schemas
   - `auth_schemas.py` - Login and registration validation

2. **profiles**: User and member profile schemas
   - `user_schemas.py` - User validation
   - `profile_schemas.py` - Profile validation
   - `member_profile_schemas.py` - Member profile validation

3. **astrology**: Astrological schemas
   - (To be implemented as needed)

### Error Handling

The application uses custom exceptions and error handlers to provide consistent error responses:

- `APIError` - Base exception class
- `ValidationError` - Input validation errors
- `ResourceNotFoundError` - Resource not found errors
- `AuthenticationError` - Authentication failures
- `AuthorizationError` - Authorization failures

## Database Collections

The application uses the following MongoDB collections:

- **user_profile**: User account information
  - _id: ObjectId (Primary key)
  - user_profile_id: Integer (Sequential ID)
  - email: String (Unique)
  - password: String (Hashed)
  - name: String
  - mobile: String (Optional)
  - created_at: DateTime
  - updated_at: DateTime

- **member_profile**: Member profile information
  - _id: ObjectId (Primary key)
  - member_profile_id: Integer (Sequential ID)
  - user_profile_id: Integer (Foreign key to user_profile)
  - name: String
  - relation: String
  - birth_date: String
  - birth_time: String
  - birth_place: String
  - latitude: Float
  - longitude: Float
  - gender: String
  - created_at: DateTime
  - updated_at: DateTime

- **user_member_astro_profile_data**: Astrological chart data for members
  - _id: ObjectId (Primary key)
  - user_id: ObjectId (Foreign key to user_profile)
  - member_id: ObjectId (Foreign key to member_profile)
  - chart_data: Object (Contains all 23 divisional charts)
  - created_at: DateTime
  - updated_at: DateTime

- **otps**: OTP verification codes
  - _id: ObjectId (Primary key)
  - email: String
  - otp: String
  - otp_type: String
  - expires_at: DateTime
  - created_at: DateTime
