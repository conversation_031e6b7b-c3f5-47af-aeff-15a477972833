# Rule Engine Functions - Complete Explanation with Examples

## Overview

The Rule Engine is a powerful system that evaluates astrological rules and conditions. It supports basic planet-house relationships, complex logical operations (AND/OR), and advanced planetary relationships.

## Core Functions Explained

### 1. `get_chart_data(user_profile_id, member_profile_id)`

**Purpose**: Retrieves chart data from MongoDB for a specific user and member.

**Input**:
```python
user_profile_id = "1"
member_profile_id = "1"
```

**Output**:
```python
{
    "_id": ObjectId("..."),
    "user_profile_id": 1,
    "member_profile_id": 1,
    "chart_data": {
        "D1": {
            "houses": [
                {
                    "house_number": 1,
                    "planets": ["sun", "mercury"],
                    "planet_degrees": [15.5, 20.3]
                },
                {
                    "house_number": 4,
                    "planets": ["moon"],
                    "planet_degrees": [25.7]
                }
            ],
            "lagna": {"sign": "MESHAM"},
            "planets_precise": {
                "sun": {"sign": "MESHAM", "degree": 15.5},
                "moon": {"sign": "KADAGAM", "degree": 25.7}
            }
        }
    }
}
```

### 2. `get_planet_house_mapping(chart_data, chart_type="D1")`

**Purpose**: Extracts planet-to-house mapping from chart data.

**Input**:
```python
chart_data = {
    "chart_data": {
        "D1": {
            "houses": [
                {"house_number": 1, "planets": ["sun", "mercury"]},
                {"house_number": 4, "planets": ["moon"]},
                {"house_number": 11, "planets": ["mars"]}
            ]
        }
    }
}
```

**Output**:
```python
{
    "SUN": 1,
    "MERCURY": 1,
    "MOON": 4,
    "MARS": 11
}
```

### 3. `normalize_planet_name(planet_name)`

**Purpose**: Standardizes planet names to uppercase format.

**Examples**:
```python
normalize_planet_name("moon") → "MOON"
normalize_planet_name("Sun") → "SUN"
normalize_planet_name("JUPITER") → "JUPITER"
normalize_planet_name("lagnam") → "LAGNA"
normalize_planet_name("invalid") → None
```

### 4. `parse_condition(condition)`

**Purpose**: Parses a single rule condition into components.

**Examples**:

**Basic Rules**:
```python
parse_condition("Moon IN House4")
→ ("MOON", "IN", 4, "BASIC")

parse_condition("Sun NOT IN House8")
→ ("SUN", "NOT IN", 8, "BASIC")
```

**Advanced Rules**:
```python
parse_condition("Ketu IN House6 WITH Ruling_Planet")
→ ("KETU", "IN", 6, "WITH_RULING_PLANET")

parse_condition("Ketu IS RELATED TO House6_Ruling_Planet")
→ ("KETU", "IS_RELATED_TO", 6, "RELATED_TO_RULING_PLANET")

parse_condition("Ketu IS ASPECTING_BIRTH House6_Ruling_Planet")
→ ("KETU", "IS_ASPECTING_BIRTH", 6, "ASPECTING_BIRTH_RULING_PLANET")
```

**Invalid Rules**:
```python
parse_condition("Invalid Query")
→ (None, None, None, None)
```

### 5. `parse_complex_query(query)`

**Purpose**: Parses complex queries with AND/OR logic.

**Examples**:

**Single Condition**:
```python
parse_complex_query("Moon IN House4")
→ [("SINGLE", ("MOON", "IN", 4, "BASIC"))]
```

**OR Logic**:
```python
parse_complex_query("Moon IN House1 OR Moon IN House4 OR Moon IN House7")
→ [
    ("SINGLE", ("MOON", "IN", 1, "BASIC")),
    ("SINGLE", ("MOON", "IN", 4, "BASIC")),
    ("SINGLE", ("MOON", "IN", 7, "BASIC"))
]
```

**AND Logic**:
```python
parse_complex_query("Sun IN House1 AND Mercury IN House1")
→ [("AND", [
    ("SUN", "IN", 1, "BASIC"),
    ("MERCURY", "IN", 1, "BASIC")
])]
```

**Mixed Logic**:
```python
parse_complex_query("Sun IN House1 AND Mercury IN House1 OR Jupiter IN House9")
→ [
    ("AND", [
        ("SUN", "IN", 1, "BASIC"),
        ("MERCURY", "IN", 1, "BASIC")
    ]),
    ("SINGLE", ("JUPITER", "IN", 9, "BASIC"))
]
```

### 6. `evaluate_condition(planet, operator, value, condition_type, planet_house_mapping)`

**Purpose**: Evaluates a single condition against planet positions.

**Input**:
```python
planet_house_mapping = {
    "SUN": 1,
    "MOON": 4,
    "MARS": 11,
    "MERCURY": 1,
    "JUPITER": 9,
    "VENUS": 2,
    "SATURN": 7,
    "RAHU": 12,
    "KETU": 6
}
```

**Examples**:

**Basic Conditions**:
```python
evaluate_condition("MOON", "IN", 4, "BASIC", planet_house_mapping)
→ True  # Moon is in House 4

evaluate_condition("MOON", "IN", 1, "BASIC", planet_house_mapping)
→ False  # Moon is not in House 1

evaluate_condition("SUN", "NOT IN", 8, "BASIC", planet_house_mapping)
→ True  # Sun is not in House 8 (it's in House 1)
```

**Advanced Conditions**:
```python
evaluate_condition("KETU", "IN", 6, "WITH_RULING_PLANET", planet_house_mapping)
→ True/False  # Depends if Mercury (House 6 ruler) is also in House 6

evaluate_condition("KETU", "IS_RELATED_TO", 6, "RELATED_TO_RULING_PLANET", planet_house_mapping)
→ True/False  # Depends on Ketu's relationship with Mercury

evaluate_condition("KETU", "IS_ASPECTING_BIRTH", 6, "ASPECTING_BIRTH_RULING_PLANET", planet_house_mapping)
→ True/False  # Depends if Ketu aspects Mercury
```

### 7. `evaluate_parsed_query(parsed_query, planet_house_mapping)`

**Purpose**: Evaluates a complete parsed query with logical operations.

**Example**:
```python
parsed_query = [
    ("AND", [
        ("SUN", "IN", 1, "BASIC"),
        ("MERCURY", "IN", 1, "BASIC")
    ]),
    ("SINGLE", ("JUPITER", "IN", 9, "BASIC"))
]

planet_house_mapping = {
    "SUN": 1,
    "MERCURY": 1,
    "JUPITER": 9
}

evaluate_parsed_query(parsed_query, planet_house_mapping)
→ True  # Both AND conditions are true OR Jupiter condition is true
```

### 8. `evaluate_rule(query, user_profile_id, member_profile_id, chart_type="D1")`

**Purpose**: Main function that evaluates a complete rule query.

**Input**:
```python
query = "Moon IN House4 OR Sun IN House1"
user_profile_id = "1"
member_profile_id = "1"
chart_type = "D1"
```

**Output**:
```python
{
    "success": True,
    "query": "Moon IN House4 OR Sun IN House1",
    "chart_type": "D1",
    "result": True,
    "planet_positions": {
        "SUN": 1,
        "MOON": 4,
        "MARS": 11,
        "MERCURY": 1,
        "JUPITER": 9,
        "VENUS": 2,
        "SATURN": 7,
        "RAHU": 12,
        "KETU": 6
    },
    "user_profile_id": "1",
    "member_profile_id": "1"
}
```

### 9. `debug_chart_structure(chart_data, chart_type="D1")`

**Purpose**: Analyzes and debugs chart data structure.

**Output**:
```python
{
    "chart_data_exists": True,
    "chart_type_exists": True,
    "chart_structure": {
        "keys": ["houses", "lagna", "planets_precise"],
        "has_houses": True,
        "has_planets_precise": True,
        "has_lagna": True
    },
    "available_charts": ["D1", "D2", "D3", "D9", "D10"],
    "houses_structure": {
        "houses_count": 12,
        "sample_house": {
            "house_number": 1,
            "planets": ["sun", "mercury"]
        },
        "house_keys": ["house_number", "planets", "planet_degrees"]
    },
    "planets_found": ["sun", "moon", "mars", "mercury", "jupiter", "venus", "saturn", "rahu", "ketu"]
}
```

### 10. `get_house_ruling_planet(house_number)`

**Purpose**: Gets the ruling planet for a specific house.

**Examples**:
```python
get_house_ruling_planet(1) → "MARS"     # Aries
get_house_ruling_planet(4) → "MOON"     # Cancer
get_house_ruling_planet(5) → "SUN"      # Leo
get_house_ruling_planet(9) → "JUPITER"  # Sagittarius
get_house_ruling_planet(10) → "SATURN"  # Capricorn
```

## House Ruling Planets Mapping

```python
HOUSE_RULING_PLANETS = {
    1: 'MARS',      # Aries
    2: 'VENUS',     # Taurus
    3: 'MERCURY',   # Gemini
    4: 'MOON',      # Cancer
    5: 'SUN',       # Leo
    6: 'MERCURY',   # Virgo
    7: 'VENUS',     # Libra
    8: 'MARS',      # Scorpio
    9: 'JUPITER',   # Sagittarius
    10: 'SATURN',   # Capricorn
    11: 'SATURN',   # Aquarius
    12: 'JUPITER'   # Pisces
}
```

## Complete Rule Examples with Expected Outputs

### Example 1: Basic Planet Position Check

**Query**: `"Moon IN House4"`

**Process**:
1. `parse_condition("Moon IN House4")` → `("MOON", "IN", 4, "BASIC")`
2. `parse_complex_query("Moon IN House4")` → `[("SINGLE", ("MOON", "IN", 4, "BASIC"))]`
3. `evaluate_condition("MOON", "IN", 4, "BASIC", {"MOON": 4})` → `True`

**API Response**:
```json
{
  "success": true,
  "query": "Moon IN House4",
  "chart_type": "D1",
  "result": true,
  "planet_positions": {"MOON": 4, "SUN": 1, "MARS": 11},
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

### Example 2: Complex OR Logic

**Query**: `"Moon IN House1 OR Moon IN House4 OR Moon IN House7"`

**Process**:
1. Parse into 3 separate conditions
2. Evaluate each: `False OR True OR False` → `True`

**API Response**:
```json
{
  "success": true,
  "query": "Moon IN House1 OR Moon IN House4 OR Moon IN House7",
  "result": true,
  "planet_positions": {"MOON": 4}
}
```

### Example 3: Complex AND Logic

**Query**: `"Sun IN House1 AND Mercury IN House1"`

**Process**:
1. Parse into AND group: `[("AND", [("SUN", "IN", 1, "BASIC"), ("MERCURY", "IN", 1, "BASIC")])]`
2. Evaluate: `True AND True` → `True`

**API Response**:
```json
{
  "success": true,
  "query": "Sun IN House1 AND Mercury IN House1",
  "result": true,
  "planet_positions": {"SUN": 1, "MERCURY": 1}
}
```

### Example 4: Advanced Ruling Planet Rule

**Query**: `"Ketu IN House6 WITH Ruling_Planet"`

**Process**:
1. Parse: `("KETU", "IN", 6, "WITH_RULING_PLANET")`
2. Check if Ketu is in House 6: `True`
3. Check if Mercury (House 6 ruler) is also in House 6: `True/False`

**API Response**:
```json
{
  "success": true,
  "query": "Ketu IN House6 WITH Ruling_Planet",
  "result": true,
  "planet_positions": {"KETU": 6, "MERCURY": 6}
}
```

### Example 5: Planetary Relationship Rule

**Query**: `"Ketu IS RELATED TO House6_Ruling_Planet"`

**Process**:
1. Parse: `("KETU", "IS_RELATED_TO", 6, "RELATED_TO_RULING_PLANET")`
2. Get House 6 ruling planet: `Mercury`
3. Check Ketu-Mercury relationship: `FRIEND/ENEMY/NEUTRAL`

**API Response**:
```json
{
  "success": true,
  "query": "Ketu IS RELATED TO House6_Ruling_Planet",
  "result": true,
  "planet_positions": {"KETU": 6, "MERCURY": 1}
}
```

## Error Handling Examples

### Missing Required Fields
**Request**:
```json
{
  "user_profile_id": "1",
  "query": "Moon IN House1"
}
```

**Response**:
```json
{
  "success": false,
  "message": "Missing required fields: member_profile_id",
  "error_code": "MISSING_REQUIRED_FIELDS",
  "missing_fields": ["member_profile_id"]
}
```

### Invalid Chart Type
**Request**:
```json
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Moon IN House1",
  "chart_type": "INVALID"
}
```

**Response**:
```json
{
  "success": false,
  "message": "Invalid chart_type: INVALID. Must be one of: D1, D2, D3...",
  "error_code": "INVALID_CHART_TYPE",
  "valid_chart_types": ["D1", "D2", "D3", "D9", "D10"]
}
```

### Invalid Query Format
**Request**:
```json
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Invalid Query Format",
  "chart_type": "D1"
}
```

**Response**:
```json
{
  "success": false,
  "message": "Invalid query format. Please check the query syntax.",
  "error_code": "INVALID_QUERY_FORMAT"
}
```

### Chart Data Not Found
**Request**:
```json
{
  "user_profile_id": "999",
  "member_profile_id": "999",
  "query": "Moon IN House1",
  "chart_type": "D1"
}
```

**Response**:
```json
{
  "success": false,
  "message": "Chart data not found for user_profile_id: 999, member_profile_id: 999",
  "error_code": "CHART_DATA_NOT_FOUND"
}
```

## Supported Rule Types Summary

### 1. Basic Rules
- `Planet IN House#` - Check if planet is in specific house
- `Planet NOT IN House#` - Check if planet is not in specific house

### 2. Logical Operations
- `Condition1 OR Condition2` - Any condition true
- `Condition1 AND Condition2` - All conditions true
- `Condition1 AND Condition2 OR Condition3` - Mixed logic

### 3. Advanced Rules
- `Planet IN House# WITH Ruling_Planet` - Planet with house ruler
- `Planet IS RELATED TO House#_Ruling_Planet` - Planetary relationships
- `Planet IS ASPECTING_BIRTH House#_Ruling_Planet` - Planetary aspects

### 4. Supported Planets
- SUN, MOON, MARS, MERCURY, JUPITER, VENUS, SATURN, RAHU, KETU

### 5. Supported Chart Types
- All 23 divisional charts: D1, D2, D3, D4, D5, D6, D7, D8, D9, D10, D11, D12, D16, D20, D24, D27, D30, D40, D45, D60, D81, D108, D144

## API Endpoints Summary

### 1. POST /api/rule-engine/evaluate
**Purpose**: Evaluate astrological rules
**Authentication**: Required (JWT Bearer token)

### 2. POST /api/rule-engine/debug-chart
**Purpose**: Debug chart data structure
**Authentication**: Required (JWT Bearer token)

### 3. GET /api/rule-engine/suggestions
**Purpose**: Get rule building suggestions
**Authentication**: Required (JWT Bearer token)

## Function Flow Diagram

```
User Query → parse_complex_query() → parse_condition() → evaluate_parsed_query() → evaluate_condition() → Result
     ↓                ↓                      ↓                      ↓                      ↓
"Moon IN House4" → [("SINGLE", ...)] → ("MOON", "IN", 4) → evaluate_condition() → True/False
```

## MongoDB Data Structure Support

The Rule Engine supports multiple MongoDB chart data formats:

### Format 1: Houses Array
```json
{
  "chart_data": {
    "D1": {
      "houses": [
        {"house_number": 1, "planets": ["sun", "mercury"]},
        {"house_number": 4, "planets": ["moon"]}
      ]
    }
  }
}
```

### Format 2: Planets Precise
```json
{
  "chart_data": {
    "D1": {
      "lagna": {"sign": "MESHAM"},
      "planets_precise": {
        "sun": {"sign": "MESHAM", "degree": 15.5},
        "moon": {"sign": "KADAGAM", "degree": 25.7}
      }
    }
  }
}
```

The Rule Engine automatically detects and handles both formats, ensuring compatibility with different data structures in your MongoDB database.
