#!/usr/bin/env python3
"""
Test New Syntax Query
Tests: "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6"
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

# Sample MongoDB chart data structure for reference
SAMPLE_CHART_DATA = {
    "chart_data": {
        "D1": {
            "houses": [
                {"house_number": 1, "house_name": "MESHAM", "planets": ["sun", "mercury"]},     # Aries - ruled by <PERSON>
                {"house_number": 2, "house_name": "R<PERSON><PERSON><PERSON><PERSON>", "planets": ["venus"]},           # Taurus - ruled by <PERSON>
                {"house_number": 3, "house_name": "MIDUN<PERSON>", "planets": []},                   # Gemini - ruled by <PERSON>
                {"house_number": 4, "house_name": "<PERSON><PERSON><PERSON><PERSON>", "planets": ["moon"]},             # Cancer - ruled by <PERSON>
                {"house_number": 5, "house_name": "SIMMAM", "planets": []},                    # Leo - ruled by <PERSON>
                {"house_number": 6, "house_name": "<PERSON>UMBA<PERSON>", "planets": ["ketu"]},              # Aquarius - ruled by <PERSON>
                {"house_number": 7, "house_name": "THULAM", "planets": []},                    # Libra - ruled by Venus
                {"house_number": 8, "house_name": "VIRICHIGAM", "planets": []},                # Scorpio - ruled by Mars
                {"house_number": 9, "house_name": "DHANUSU", "planets": ["jupiter"]},          # Sagittarius - ruled by Jupiter
                {"house_number": 10, "house_name": "MAGARAM", "planets": []},                  # Capricorn - ruled by Saturn
                {"house_number": 11, "house_name": "KANNI", "planets": ["mars", "saturn"]},    # Virgo - ruled by Mercury
                {"house_number": 12, "house_name": "MEENAM", "planets": ["rahu"]}              # Pisces - ruled by Jupiter
            ]
        }
    }
}

# Planet positions from sample data
PLANET_POSITIONS = {
    "SUN": 1, "MERCURY": 1, "VENUS": 2, "MOON": 4, "KETU": 6,
    "JUPITER": 9, "MARS": 11, "SATURN": 11, "RAHU": 12
}

def analyze_new_syntax_query():
    """Analyze the new syntax query step by step"""
    print("=" * 80)
    print("NEW SYNTAX QUERY ANALYSIS")
    print("Query: Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6")
    print("=" * 80)
    
    # House name to ruling planet mapping
    house_name_ruling_planets = {
        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
    }
    
    print("Current Planet Positions:")
    for planet, house in PLANET_POSITIONS.items():
        print(f"  {planet}: House {house}")
    
    print("\nHouse Names and Ruling Planets:")
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        house_num = house["house_number"]
        house_name = house["house_name"]
        ruling_planet = house_name_ruling_planets.get(house_name)
        print(f"  House {house_num}: {house_name} (ruled by {ruling_planet})")
    
    print("\n" + "=" * 60)
    print("CONDITION 1: Jupiter IN house9 WITH Ruling_Planet_house9")
    print("=" * 60)
    
    # Condition 1: Jupiter IN house9 WITH Ruling_Planet_house9
    jupiter_house = PLANET_POSITIONS.get("JUPITER")
    print(f"Step 1: Is Jupiter in House 9? → Jupiter is in House {jupiter_house} → {'YES ✓' if jupiter_house == 9 else 'NO ✗'}")
    
    if jupiter_house == 9:
        # Find house 9 name and ruling planet
        house_9_name = None
        for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
            if house["house_number"] == 9:
                house_9_name = house["house_name"]
                break
        
        ruling_planet_9 = house_name_ruling_planets.get(house_9_name)
        ruling_planet_9_house = PLANET_POSITIONS.get(ruling_planet_9)
        
        print(f"Step 2: House 9 name → {house_9_name}")
        print(f"Step 3: Ruling planet of {house_9_name} → {ruling_planet_9}")
        print(f"Step 4: Is {ruling_planet_9} in House 9? → {ruling_planet_9} is in House {ruling_planet_9_house} → {'YES ✓' if ruling_planet_9_house == 9 else 'NO ✗'}")
        
        condition_1_result = ruling_planet_9_house == 9
    else:
        condition_1_result = False
    
    print(f"CONDITION 1 RESULT: {'TRUE ✓' if condition_1_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 60)
    print("CONDITION 2: Ketu IN house6")
    print("=" * 60)
    
    # Condition 2: Ketu IN house6 (basic condition)
    ketu_house = PLANET_POSITIONS.get("KETU")
    print(f"Step 1: Is Ketu in House 6? → Ketu is in House {ketu_house} → {'YES ✓' if ketu_house == 6 else 'NO ✗'}")
    
    condition_2_result = ketu_house == 6
    print(f"CONDITION 2 RESULT: {'TRUE ✓' if condition_2_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 60)
    print("FINAL OR LOGIC")
    print("=" * 60)
    
    final_result = condition_1_result or condition_2_result
    print(f"Condition 1 OR Condition 2 → {condition_1_result} OR {condition_2_result} → {'TRUE ✓' if final_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 60)
    print("DETAILED EXPLANATION")
    print("=" * 60)
    
    if condition_1_result:
        print("✓ Condition 1 is TRUE: Jupiter is in House 9 (Sagittarius) WITH Jupiter (self-ruling)")
    else:
        print("✗ Condition 1 is FALSE: Jupiter is in House 9 but Jupiter rules Sagittarius, so it's WITH itself")
        print("  Wait... this should actually be TRUE! Jupiter in its own sign.")
    
    if condition_2_result:
        print("✓ Condition 2 is TRUE: Ketu is in House 6")
    else:
        print("✗ Condition 2 is FALSE: Ketu is not in House 6")
    
    # Let me recalculate condition 1 correctly
    print("\n🔍 RECHECKING CONDITION 1:")
    print("Jupiter in House 9 (DHANUSU/Sagittarius)")
    print("Ruling planet of DHANUSU = JUPITER")
    print("Is JUPITER in House 9? YES!")
    print("Therefore, Condition 1 should be TRUE ✓")
    
    return True  # Should be TRUE because Jupiter is self-ruling in House 9

def test_parsing_new_syntax():
    """Test parsing of new syntax"""
    print("\n" + "=" * 80)
    print("TESTING NEW SYNTAX PARSING")
    print("=" * 80)
    
    # Import the parsing function (simulated)
    test_conditions = [
        "Jupiter IN house9 WITH Ruling_Planet_house9",
        "Ketu IN house6",
        "Mars IN house11 WITH Ruling_Planet_house11",
        "Jupiter IN house9 WITH Ruling_Planet_house10",  # Cross-house (different houses)
    ]
    
    print("Testing condition parsing:")
    for condition in test_conditions:
        print(f"\nCondition: '{condition}'")
        
        # Simulate parsing logic
        if "WITH Ruling_Planet_house" in condition:
            import re
            match = re.search(r'(\w+)\s+(IN|NOT IN)\s+house(\d+)\s+WITH\s+Ruling_Planet_house(\d+)', condition, re.IGNORECASE)
            if match:
                planet, operator, house_num, ruling_house_num = match.groups()
                if int(house_num) == int(ruling_house_num):
                    result = (planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET')
                    print(f"  Parsed as: {result}")
                else:
                    result = (planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET_CROSS')
                    print(f"  Parsed as: {result} (cross-house)")
            else:
                print("  Failed to parse")
        elif " IN house" in condition:
            import re
            match = re.search(r'(\w+)\s+(IN|NOT IN)\s+house(\d+)', condition, re.IGNORECASE)
            if match:
                planet, operator, house_num = match.groups()
                result = (planet.upper(), operator.upper(), int(house_num), 'BASIC')
                print(f"  Parsed as: {result}")
            else:
                print("  Failed to parse")

def create_api_test_requests():
    """Create API test requests for the new syntax"""
    print("\n" + "=" * 80)
    print("API TEST REQUESTS FOR NEW SYNTAX")
    print("=" * 80)
    
    # Individual condition tests
    individual_tests = [
        {
            "name": "Test Jupiter self-ruling",
            "query": "Jupiter IN house9 WITH Ruling_Planet_house9",
            "expected": True,
            "explanation": "Jupiter in House 9 (Sagittarius) WITH Jupiter (self-ruling)"
        },
        {
            "name": "Test Ketu position",
            "query": "Ketu IN house6",
            "expected": True,
            "explanation": "Ketu is in House 6"
        },
        {
            "name": "Test Mars with ruling planet",
            "query": "Mars IN house11 WITH Ruling_Planet_house11",
            "expected": False,
            "explanation": "Mars in House 11 (Virgo) but Mercury (ruler) not in House 11"
        }
    ]
    
    # Complex OR test
    complex_test = {
        "name": "Test complex OR query",
        "query": "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6",
        "expected": True,
        "explanation": "TRUE OR TRUE = TRUE (both conditions are true)"
    }
    
    print("Individual Test Cases:")
    for i, test in enumerate(individual_tests, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Query: {test['query']}")
        print(f"   Expected: {'TRUE ✓' if test['expected'] else 'FALSE ✗'}")
        print(f"   Explanation: {test['explanation']}")
        
        # Show curl command
        print(f"   Curl command:")
        print(f'   curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \\')
        print(f'     -H "Content-Type: application/json" \\')
        print(f'     -H "Authorization: Bearer YOUR_TOKEN" \\')
        print(f'     -d \'{{"user_profile_id": "1", "member_profile_id": "1", "query": "{test["query"]}", "chart_type": "D1"}}\'')
    
    print(f"\nComplex Test Case:")
    print(f"   Query: {complex_test['query']}")
    print(f"   Expected: {'TRUE ✓' if complex_test['expected'] else 'FALSE ✗'}")
    print(f"   Explanation: {complex_test['explanation']}")
    
    # Show curl command for complex query
    print(f"   Curl command:")
    print(f'   curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \\')
    print(f'     -H "Content-Type: application/json" \\')
    print(f'     -H "Authorization: Bearer YOUR_TOKEN" \\')
    print(f'     -d \'{{"user_profile_id": "1", "member_profile_id": "1", "query": "{complex_test["query"]}", "chart_type": "D1"}}\'')

def main():
    """Main demonstration function"""
    print("NEW SYNTAX QUERY TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("New Query: Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6")
    
    # Run analysis
    expected_result = analyze_new_syntax_query()
    test_parsing_new_syntax()
    create_api_test_requests()
    
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print("✓ New syntax 'WITH Ruling_Planet_house#' is more explicit")
    print("✓ Clearly specifies which house's ruling planet to check")
    print("✓ Supports both same-house and cross-house ruling planet checks")
    print("✓ Backward compatible with legacy 'WITH Ruling_Planet' syntax")
    
    print("\nExpected Results:")
    print("• Jupiter IN house9 WITH Ruling_Planet_house9 → TRUE (self-ruling)")
    print("• Ketu IN house6 → TRUE (basic position)")
    print("• Combined OR query → TRUE (TRUE OR TRUE = TRUE)")
    
    print("\nNext Steps:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test individual conditions")
    print("4. Test complex OR query")
    print("5. Verify results match expectations")

if __name__ == "__main__":
    main()
