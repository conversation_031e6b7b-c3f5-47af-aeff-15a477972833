#!/usr/bin/env python3
"""
Enhanced Rule Engine Demonstration
Shows how the rule engine now uses actual chart data to determine ruling planets
"""

import json
from datetime import datetime

# Sample chart data with actual signs for houses
SAMPLE_CHART_DATA = {
    "chart_data": {
        "D1": {
            "lagna": {"sign": "MESHAM"},  # Aries lagna
            "houses": [
                {
                    "house_number": 1,
                    "sign": "MESHAM",      # Aries - ruled by Mars
                    "planets": ["sun", "mercury"],
                    "planet_degrees": [15.5, 20.3]
                },
                {
                    "house_number": 2,
                    "sign": "RISHABAM",    # Taurus - ruled by Venus
                    "planets": ["venus"],
                    "planet_degrees": [25.7]
                },
                {
                    "house_number": 3,
                    "sign": "MIDUNAM",     # Gemini - ruled by Mercury
                    "planets": [],
                    "planet_degrees": []
                },
                {
                    "house_number": 4,
                    "sign": "KADAGAM",     # Cancer - ruled by Moon
                    "planets": ["moon"],
                    "planet_degrees": [12.3]
                },
                {
                    "house_number": 5,
                    "sign": "SIMMAM",      # Leo - ruled by Sun
                    "planets": [],
                    "planet_degrees": []
                },
                {
                    "house_number": 6,
                    "sign": "<PERSON><PERSON><PERSON>",       # Virgo - ruled by Mercury
                    "planets": ["ketu"],
                    "planet_degrees": [8.5]
                },
                {
                    "house_number": 7,
                    "sign": "THULAM",      # Libra - ruled by Venus
                    "planets": [],
                    "planet_degrees": []
                },
                {
                    "house_number": 8,
                    "sign": "VIRICHIGAM",  # Scorpio - ruled by Mars
                    "planets": [],
                    "planet_degrees": []
                },
                {
                    "house_number": 9,
                    "sign": "DHANUSU",     # Sagittarius - ruled by Jupiter
                    "planets": ["jupiter"],
                    "planet_degrees": [22.1]
                },
                {
                    "house_number": 10,
                    "sign": "MAGARAM",     # Capricorn - ruled by Saturn
                    "planets": [],
                    "planet_degrees": []
                },
                {
                    "house_number": 11,
                    "sign": "KUMBAM",      # Aquarius - ruled by Saturn
                    "planets": ["mars", "saturn"],
                    "planet_degrees": [18.7, 5.2]
                },
                {
                    "house_number": 12,
                    "sign": "MEENAM",      # Pisces - ruled by Jupiter
                    "planets": ["rahu"],
                    "planet_degrees": [8.5]
                }
            ]
        }
    }
}

# Planet positions extracted from chart data
PLANET_POSITIONS = {
    "SUN": 1,
    "MERCURY": 1,
    "VENUS": 2,
    "MOON": 4,
    "KETU": 6,
    "JUPITER": 9,
    "MARS": 11,
    "SATURN": 11,
    "RAHU": 12
}

def demo_get_house_sign_and_ruling_planet():
    """Demonstrate getting house signs and ruling planets from chart data"""
    print("=" * 70)
    print("DEMO: Getting House Signs and Ruling Planets from Chart Data")
    print("=" * 70)
    
    print("House → Sign → Ruling Planet (from actual chart data)")
    print("-" * 50)
    
    # Import the function (simulated)
    def get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type="D1"):
        chart = chart_data["chart_data"].get(chart_type, {})
        if "houses" in chart:
            for house in chart["houses"]:
                if house.get("house_number") == house_number:
                    house_sign = house.get("sign")
                    if house_sign:
                        sign_ruling_planets = {
                            'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
                            'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
                            'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
                            'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
                        }
                        ruling_planet = sign_ruling_planets.get(house_sign.upper())
                        return house_sign.upper(), ruling_planet
        return None, None
    
    for house_num in range(1, 13):
        sign, ruling_planet = get_house_sign_and_ruling_planet_from_chart(SAMPLE_CHART_DATA, house_num)
        print(f"House {house_num:2d} → {sign:10s} → {ruling_planet}")

def demo_enhanced_with_ruling_planet():
    """Demonstrate enhanced WITH Ruling Planet rule"""
    print("\n" + "=" * 70)
    print("DEMO: Enhanced 'WITH Ruling_Planet' Rule Evaluation")
    print("=" * 70)
    
    print("Planet Positions:")
    for planet, house in PLANET_POSITIONS.items():
        print(f"  {planet}: House {house}")
    
    print("\nRule Evaluations:")
    print("-" * 50)
    
    test_cases = [
        ("KETU", 6),   # Ketu in House 6 (Virgo - ruled by Mercury)
        ("SUN", 1),    # Sun in House 1 (Aries - ruled by Mars)
        ("JUPITER", 9), # Jupiter in House 9 (Sagittarius - ruled by Jupiter)
        ("MARS", 11),  # Mars in House 11 (Aquarius - ruled by Saturn)
    ]
    
    def evaluate_with_ruling_planet(planet, house_num):
        # Check if planet is in the house
        if PLANET_POSITIONS.get(planet) != house_num:
            return False, f"{planet} is not in House {house_num}"
        
        # Get the ruling planet of the house from chart data
        chart = SAMPLE_CHART_DATA["chart_data"]["D1"]
        for house in chart["houses"]:
            if house.get("house_number") == house_num:
                house_sign = house.get("sign")
                if house_sign:
                    sign_ruling_planets = {
                        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
                        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
                        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
                        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
                    }
                    ruling_planet = sign_ruling_planets.get(house_sign.upper())
                    
                    # Check if ruling planet is also in the same house
                    if ruling_planet and PLANET_POSITIONS.get(ruling_planet) == house_num:
                        return True, f"{planet} is in House {house_num} WITH {ruling_planet} (ruling planet of {house_sign})"
                    else:
                        ruling_planet_house = PLANET_POSITIONS.get(ruling_planet, "Not found")
                        return False, f"{planet} is in House {house_num} but {ruling_planet} (ruling planet of {house_sign}) is in House {ruling_planet_house}"
        
        return False, f"Could not determine ruling planet for House {house_num}"
    
    for planet, house_num in test_cases:
        result, explanation = evaluate_with_ruling_planet(planet, house_num)
        status = "✓ TRUE" if result else "✗ FALSE"
        print(f"{planet} IN House{house_num} WITH Ruling_Planet → {status}")
        print(f"  Explanation: {explanation}")
        print()

def demo_comparison_static_vs_dynamic():
    """Compare static vs dynamic ruling planet determination"""
    print("=" * 70)
    print("DEMO: Static vs Dynamic Ruling Planet Determination")
    print("=" * 70)
    
    # Static mapping (traditional approach)
    static_house_rulers = {
        1: 'MARS', 2: 'VENUS', 3: 'MERCURY', 4: 'MOON',
        5: 'SUN', 6: 'MERCURY', 7: 'VENUS', 8: 'MARS',
        9: 'JUPITER', 10: 'SATURN', 11: 'SATURN', 12: 'JUPITER'
    }
    
    print("House → Static Ruler → Dynamic Ruler (from chart)")
    print("-" * 55)
    
    for house_num in range(1, 13):
        static_ruler = static_house_rulers[house_num]
        
        # Get dynamic ruler from chart
        chart = SAMPLE_CHART_DATA["chart_data"]["D1"]
        dynamic_ruler = None
        house_sign = None
        
        for house in chart["houses"]:
            if house.get("house_number") == house_num:
                house_sign = house.get("sign")
                if house_sign:
                    sign_ruling_planets = {
                        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
                        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
                        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
                        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
                    }
                    dynamic_ruler = sign_ruling_planets.get(house_sign.upper())
                break
        
        match_indicator = "✓" if static_ruler == dynamic_ruler else "✗"
        print(f"House {house_num:2d} → {static_ruler:7s} → {dynamic_ruler:7s} ({house_sign:10s}) {match_indicator}")

def demo_api_response_format():
    """Show enhanced API response format"""
    print("\n" + "=" * 70)
    print("DEMO: Enhanced API Response Format")
    print("=" * 70)
    
    # Simulate API response for "Ketu IN House6 WITH Ruling_Planet"
    api_response = {
        "success": True,
        "query": "Ketu IN House6 WITH Ruling_Planet",
        "chart_type": "D1",
        "result": False,  # Ketu is in House 6 but Mercury (ruler) is in House 1
        "planet_positions": PLANET_POSITIONS,
        "house_analysis": {
            "house_6": {
                "sign": "KANNI",
                "ruling_planet": "MERCURY",
                "ruling_planet_position": "House 1",
                "planets_in_house": ["KETU"],
                "explanation": "Ketu is in House 6 (Virgo) but Mercury (ruling planet) is in House 1, not House 6"
            }
        },
        "user_profile_id": "1",
        "member_profile_id": "1"
    }
    
    print("Enhanced API Response:")
    print(json.dumps(api_response, indent=2))

def main():
    """Main demonstration function"""
    print("ENHANCED RULE ENGINE - DYNAMIC RULING PLANET DEMONSTRATION")
    print("=" * 70)
    print(f"Demo started at: {datetime.now()}")
    
    # Run demonstrations
    demo_get_house_sign_and_ruling_planet()
    demo_enhanced_with_ruling_planet()
    demo_comparison_static_vs_dynamic()
    demo_api_response_format()
    
    print("\n" + "=" * 70)
    print("KEY IMPROVEMENTS")
    print("=" * 70)
    print("✓ Rule engine now reads actual house signs from chart data")
    print("✓ Ruling planets determined dynamically based on chart signs")
    print("✓ More accurate astrological analysis")
    print("✓ Supports different chart types (D1, D9, etc.)")
    print("✓ Fallback to static mapping if chart data unavailable")
    print("✓ Enhanced error handling and explanations")
    
    print("\nExample: 'Ketu IN House6 WITH Ruling_Planet'")
    print("- OLD: Uses static mapping (House 6 = Mercury)")
    print("- NEW: Checks actual chart (House 6 = Virgo = Mercury)")
    print("- RESULT: More accurate based on individual's chart")

if __name__ == "__main__":
    main()
