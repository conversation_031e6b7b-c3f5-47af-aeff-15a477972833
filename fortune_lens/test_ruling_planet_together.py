#!/usr/bin/env python3
"""
Test Enhanced Together Functionality with Ruling Planets
Tests the new ruling planet together logic
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app import create_app
from app.services.rule_engine import get_chart_data, get_planets_in_house, check_house_planet_relationship, get_house_ruling_planet

def test_ruling_planet_together():
    """Test the enhanced together functionality with ruling planets"""
    print("=" * 80)
    print("🔗 TESTING ENHANCED TOGETHER WITH RULING PLANETS")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            user_profile_id = "1"
            member_profile_id = "1"
            
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            print("Enhanced 'Together' now includes THREE types:")
            print("\n1. 📍 Same House Together:")
            print("   • Two planets physically placed in the same house")
            
            print("\n2. 🔗 Ruling Planets Together:")
            print("   • Planets in different houses, but their house ruling planets are together")
            print("   • Example: Planet A in House 1, Planet B in House 5")
            print("   • If House 1's ruling planet and House 5's ruling planet are in the same house")
            print("   • Then Planet A and Planet B are considered 'together'")
            
            print("\n3. 🌟 Angular Houses Together:")
            print("   • Opposite houses (6 apart), Trine houses (4/8 apart), Square houses (3/9 apart)")
            
            # Show actual house ruling planets
            print("\n" + "=" * 60)
            print("HOUSE RULING PLANETS")
            print("=" * 60)
            
            for house_num in range(1, 13):
                ruling_planet = get_house_ruling_planet(house_num)
                planets_in_house = get_planets_in_house(chart_data, house_num, "D1")
                planet_list = ', '.join(planets_in_house) if planets_in_house else "(empty)"
                print(f"House {house_num:2d}: Ruling Planet = {ruling_planet:7s} | Planets = {planet_list}")
            
            # Test ruling planet together logic
            print("\n" + "=" * 60)
            print("TESTING RULING PLANET TOGETHER LOGIC")
            print("=" * 60)
            
            # Test case 1: Houses with different ruling planets
            print("\n🧪 Test Case 1: 1st House ↔ 5th House")
            house1_ruling = get_house_ruling_planet(1)
            house5_ruling = get_house_ruling_planet(5)
            print(f"House 1 ruling planet: {house1_ruling}")
            print(f"House 5 ruling planet: {house5_ruling}")
            
            if house1_ruling and house5_ruling:
                house1_ruling_location = None
                house5_ruling_location = None
                
                # Find where these ruling planets are located
                for house_num in range(1, 13):
                    planets = get_planets_in_house(chart_data, house_num, "D1")
                    if house1_ruling.upper() in [p.upper() for p in planets]:
                        house1_ruling_location = house_num
                    if house5_ruling.upper() in [p.upper() for p in planets]:
                        house5_ruling_location = house_num
                
                print(f"{house1_ruling} (House 1's ruler) is in House {house1_ruling_location}")
                print(f"{house5_ruling} (House 5's ruler) is in House {house5_ruling_location}")
                
                if house1_ruling_location and house5_ruling_location:
                    if house1_ruling_location == house5_ruling_location:
                        print(f"✅ RULING PLANETS TOGETHER: Both in House {house1_ruling_location}")
                        print("✅ Therefore, planets in House 1 and House 5 are 'together'")
                    else:
                        print("❌ Ruling planets are in different houses")
                        print("❌ Therefore, planets in House 1 and House 5 are NOT 'together' by ruling planet logic")
            
            # Test the API with this logic
            print("\n" + "=" * 60)
            print("API TESTING WITH ENHANCED TOGETHER")
            print("=" * 60)
            
            test_cases = [
                {
                    "query": "1st_House_Planet IS RELATED TO 5th_House_Planet",
                    "description": "Test ruling planet together logic"
                },
                {
                    "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
                    "description": "Test multiple together types"
                },
                {
                    "query": "9th_House_Planet IS RELATED TO 11th_House_Planet",
                    "description": "Test ruling planet together for single planets"
                }
            ]
            
            for test_case in test_cases:
                print(f"\n🧪 Testing: {test_case['query']}")
                print(f"Description: {test_case['description']}")
                
                # Extract house numbers from query
                parts = test_case['query'].split('_')
                house1 = int(parts[0][:-2])  # Remove 'th' from '1st'
                house2 = int(parts[4][:-2])  # Remove 'th' from '5th'
                
                result = check_house_planet_relationship(chart_data, house1, house2, "D1")
                
                print(f"Result: {result['overall_result']}")
                print(f"House {house1} planets: {result['house1_planets']}")
                print(f"House {house2} planets: {result['house2_planets']}")
                print(f"Together: {result['summary']['together']}")
                print(f"Together types: {result['summary']['together_types']}")
                
                if result['summary']['together_types']['ruling_planets_together']:
                    print("✅ RULING PLANETS TOGETHER detected!")
                
                # Show relationship details
                relationships = result['planet_relationships']
                for rel_key, rel_data in relationships.items():
                    if rel_data['together']:
                        print(f"  • {rel_key}: together_type = {rel_data.get('together_type', 'unknown')}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing ruling planet together: {e}")
            import traceback
            traceback.print_exc()
            return False

def show_enhanced_together_summary():
    """Show summary of enhanced together functionality"""
    print("\n" + "=" * 80)
    print("📊 ENHANCED TOGETHER FUNCTIONALITY SUMMARY")
    print("=" * 80)
    
    print("✅ THREE TYPES OF TOGETHER RELATIONSHIPS:")
    
    print("\n1. 📍 Same House Together:")
    print("   • Condition: Both planets in the same house")
    print("   • Example: LAGNAM and RAHU both in House 1")
    print("   • Logic: Physical proximity")
    
    print("\n2. 🔗 Ruling Planets Together:")
    print("   • Condition: Planets in different houses, but their house ruling planets are together")
    print("   • Example: Planet A in House 1, Planet B in House 5")
    print("   • If House 1's ruling planet and House 5's ruling planet are in the same house")
    print("   • Then Planet A and Planet B are considered 'together'")
    print("   • Logic: Astrological connection through ruling planet relationships")
    
    print("\n3. 🌟 Angular Houses Together:")
    print("   • Opposite Houses: 6 houses apart (1-7, 2-8, 3-9, 4-10, 5-11, 6-12)")
    print("   • Trine Houses: 4 or 8 houses apart (1-5-9, 2-6-10, 3-7-11, 4-8-12)")
    print("   • Square Houses: 3 or 9 houses apart (1-4-7-10, 2-5-8-11, 3-6-9-12)")
    print("   • Logic: Traditional astrological angular relationships")
    
    print("\n✅ ENHANCED API RESPONSE:")
    print("   • together_types.same_house: Physical proximity")
    print("   • together_types.ruling_planets_together: Ruling planet connection")
    print("   • together_types.opposite_houses: Opposition aspect")
    print("   • together_types.trine_houses: Trine aspect")
    print("   • together_types.square_houses: Square aspect")

def main():
    """Main testing function"""
    print("ENHANCED TOGETHER FUNCTIONALITY WITH RULING PLANETS")
    print("=" * 80)
    print("Testing the new ruling planet together logic")
    
    # Test enhanced together functionality
    if test_ruling_planet_together():
        show_enhanced_together_summary()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ Enhanced together functionality implemented")
    print("✅ Ruling planet together logic added")
    print("✅ Three types of together relationships supported")
    print("✅ Comprehensive astrological together analysis")
    
    print("\n🎯 TOGETHER TYPES NOW SUPPORTED:")
    print("✅ Same House Together: Planets in same house")
    print("✅ Ruling Planets Together: House ruling planets are together")
    print("✅ Angular Houses Together: Opposite, trine, square relationships")
    
    print("\n📋 NEXT STEPS:")
    print("1. Test the enhanced API with ruling planet logic")
    print("2. Verify ruling planet together relationships")
    print("3. Check comprehensive together type analysis")

if __name__ == "__main__":
    main()
