# Complex Ketu Query API Testing Guide

## Query to Test
```
Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet
```

## Query Analysis

### Current Chart Data (Sample)
Based on the test data, here's the chart structure:

**Planet Positions:**
- SUN: House 1
- MERCURY: House 1  
- VENUS: House 2
- MOON: House 4
- KETU: House 6 ⭐
- JUPITER: House 9
- MARS: House 11
- SATURN: House 11 ⭐
- RAHU: House 12

**House Names and Ruling Planets:**
- House 1: MESHAM (ruled by MARS)
- House 2: RISHABAM (ruled by VENUS)
- House 3: MIDUNAM (ruled by <PERSON>RCURY)
- House 4: KADAGAM (ruled by <PERSON><PERSON><PERSON>)
- House 5: SIMMAM (ruled by <PERSON>UN)
- House 6: KUMBAM (ruled by SATURN) ⭐
- House 7: THULAM (ruled by VENUS)
- House 8: VIRICHIGAM (ruled by MARS)
- House 9: DHANUSU (ruled by <PERSON><PERSON><PERSON><PERSON>)
- House 10: MAGARAM (ruled by SATURN) ⭐
- House 11: KANNI (ruled by MERCUR<PERSON>)
- House 12: MEENAM (ruled by <PERSON><PERSON><PERSON><PERSON>)

### Step-by-Step Analysis

#### Condition 1: "Ketu IN house6 WITH Ruling_Planet"

1. **Is Ketu in House 6?** → YES ✓ (Ketu is in House 6)
2. **What is House 6 name?** → KUMBAM (Aquarius)
3. **Who rules KUMBAM?** → SATURN
4. **Is SATURN in House 6?** → NO ✗ (Saturn is in House 11)

**Condition 1 Result: FALSE ✗**

#### Condition 2: "Ketu IN house10 WITH Ruling_Planet"

1. **Is Ketu in House 10?** → NO ✗ (Ketu is in House 6, not House 10)

**Condition 2 Result: FALSE ✗**

#### Final OR Logic
```
Condition 1 OR Condition 2 → FALSE OR FALSE → FALSE ✗
```

**Expected Final Result: FALSE**

## API Testing with Postman

### Step 1: Authentication

Since the API requires JWT authentication, you need to first get an access token.

#### Register a Test User
```bash
POST http://127.0.0.1:5003/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123", 
  "name": "Ketu Test User",
  "mobile": "9876543210"
}
```

#### Verify OTP (if required)
```bash
POST http://127.0.0.1:5003/api/auth/verify-otp
Content-Type: application/json

{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

#### Login to Get Token
```bash
POST http://127.0.0.1:5003/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Expected Response:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user_profile_id": 1,
  "message": "Login successful"
}
```

### Step 2: Debug Chart Data

First, check the actual chart data structure:

```bash
POST http://127.0.0.1:5003/api/rule-engine/debug-chart
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "chart_type": "D1"
}
```

**Expected Response:**
```json
{
  "success": true,
  "chart_data_exists": true,
  "available_charts": ["D1", "D2", "D3", "D9", "D10"],
  "houses_structure": {
    "houses_count": 12,
    "sample_house": {
      "house_number": 6,
      "house_name": "KUMBAM",
      "planets": ["ketu"]
    }
  },
  "planets_found": ["sun", "moon", "mars", "mercury", "jupiter", "venus", "saturn", "rahu", "ketu"]
}
```

### Step 3: Test Individual Conditions

#### Test Condition 1
```bash
POST http://127.0.0.1:5003/api/rule-engine/evaluate
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Ketu IN house6 WITH Ruling_Planet",
  "chart_type": "D1"
}
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Ketu IN house6 WITH Ruling_Planet",
  "chart_type": "D1",
  "result": false,
  "planet_positions": {
    "KETU": 6,
    "SATURN": 11
  },
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

#### Test Condition 2
```bash
POST http://127.0.0.1:5003/api/rule-engine/evaluate
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "user_profile_id": "1",
  "member_profile_id": "1", 
  "query": "Ketu IN house10 WITH Ruling_Planet",
  "chart_type": "D1"
}
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Ketu IN house10 WITH Ruling_Planet",
  "chart_type": "D1",
  "result": false,
  "planet_positions": {
    "KETU": 6
  },
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

### Step 4: Test Complex OR Query

```bash
POST http://127.0.0.1:5003/api/rule-engine/evaluate
Authorization: Bearer YOUR_ACCESS_TOKEN
Content-Type: application/json

{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet",
  "chart_type": "D1"
}
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Ketu IN house6 WITH Ruling_Planet OR Ketu IN house10 WITH Ruling_Planet",
  "chart_type": "D1",
  "result": false,
  "planet_positions": {
    "SUN": 1,
    "MERCURY": 1,
    "VENUS": 2,
    "MOON": 4,
    "KETU": 6,
    "JUPITER": 9,
    "MARS": 11,
    "SATURN": 11,
    "RAHU": 12
  },
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

## Key Points

### 1. MongoDB Structure
- Chart data uses `house_name` field (e.g., "KUMBAM")
- Houses array index 0-11 maps to house_number 1-12
- Rule engine reads actual house_name from chart data

### 2. WITH Ruling_Planet Logic
- Checks if planet is in specified house
- Gets house_name from MongoDB chart data
- Determines ruling planet from house_name
- Verifies if ruling planet is also in same house

### 3. OR Logic
- Evaluates both conditions independently
- Returns TRUE if ANY condition is TRUE
- Returns FALSE only if ALL conditions are FALSE

### 4. Expected Result for This Query
- **Condition 1**: FALSE (Ketu in House 6 but Saturn not in House 6)
- **Condition 2**: FALSE (Ketu not in House 10)
- **Final Result**: FALSE (FALSE OR FALSE = FALSE)

## Alternative Test Scenarios

To see TRUE results, you could test with different chart data where:

1. **Ketu in House 6 WITH Saturn also in House 6**
2. **Ketu in House 10 WITH Saturn also in House 10**
3. **Jupiter in House 9 WITH Jupiter (self-ruling)**

Example:
```
Jupiter IN house9 WITH Ruling_Planet
```
This would return TRUE because Jupiter is in House 9 (Sagittarius) and Jupiter rules Sagittarius.

## Troubleshooting

### Authentication Issues
- Ensure you have a valid access token
- Check token hasn't expired
- Verify user exists in database

### Chart Data Issues
- Use debug-chart endpoint to verify data structure
- Check if user_profile_id and member_profile_id exist
- Ensure chart data is properly generated

### Query Format Issues
- Verify query syntax matches supported patterns
- Check planet names are valid (SUN, MOON, MARS, etc.)
- Ensure house numbers are 1-12
- Use exact syntax: "Planet IN house# WITH Ruling_Planet"
