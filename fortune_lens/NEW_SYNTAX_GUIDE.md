# New Explicit Syntax for Rule Engine

## Overview

The Rule Engine now supports a more explicit syntax for specifying ruling planet relationships. This makes queries clearer and more precise.

## New Syntax Format

### Before (Ambiguous)
```
Ketu IN house6 WITH Ruling_Planet
```
*Question: Ruling planet of which house?*

### After (Explicit)
```
Ketu IN house6 WITH Ruling_Planet_house6
```
*Clear: Ruling planet of house 6*

## Query Example

### **Target Query:**
```
Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6
```

### **Analysis:**

#### **Condition 1: Jupiter IN house9 WITH Ruling_Planet_house9**
- ✅ **Jupiter in House 9?** → YES (Jupiter is in House 9)
- ✅ **House 9 name?** → DHANUSU (Sagittarius)
- ✅ **Ruling planet of DHANUSU?** → JUPITER
- ✅ **Is JUPITER in House 9?** → YES (self-ruling case)

**Result: TRUE ✅**

#### **Condition 2: Ketu IN house6**
- ✅ **Ketu in House 6?** → YES (<PERSON><PERSON> is in House 6)

**Result: TRUE ✅**

#### **Final OR Logic:**
```
TRUE OR TRUE = TRUE ✅
```

**Expected API Result: TRUE**

---

## Supported Syntax Patterns

### 1. **Explicit Ruling Planet (New)**
```
Planet IN house# WITH Ruling_Planet_house#
```
**Examples:**
- `Jupiter IN house9 WITH Ruling_Planet_house9`
- `Mars IN house11 WITH Ruling_Planet_house11`
- `Ketu IN house6 WITH Ruling_Planet_house6`

### 2. **Cross-House Ruling Planet (Advanced)**
```
Planet IN house# WITH Ruling_Planet_house#
```
**Examples:**
- `Jupiter IN house9 WITH Ruling_Planet_house10` (Jupiter in 9th with 10th house ruler)
- `Mars IN house1 WITH Ruling_Planet_house7` (Mars in 1st with 7th house ruler)

### 3. **Legacy Support (Backward Compatible)**
```
Planet IN house# WITH Ruling_Planet
```
**Examples:**
- `Ketu IN house6 WITH Ruling_Planet` (assumes same house)

### 4. **Basic Position (Unchanged)**
```
Planet IN house#
Planet NOT IN house#
```
**Examples:**
- `Ketu IN house6`
- `Sun NOT IN house8`

### 5. **Complex Logic (Enhanced)**
```
Condition1 OR Condition2
Condition1 AND Condition2
Mixed AND/OR combinations
```

---

## API Testing Guide

### **Step 1: Authentication**
```bash
curl -X POST "http://127.0.0.1:5003/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### **Step 2: Test Individual Conditions**

#### **Test 1: Jupiter Self-Ruling**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Jupiter IN house9 WITH Ruling_Planet_house9",
    "chart_type": "D1"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Jupiter IN house9 WITH Ruling_Planet_house9",
  "result": true,
  "planet_positions": {"JUPITER": 9}
}
```

#### **Test 2: Basic Position**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Ketu IN house6",
    "chart_type": "D1"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Ketu IN house6",
  "result": true,
  "planet_positions": {"KETU": 6}
}
```

### **Step 3: Test Complex OR Query**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6",
    "chart_type": "D1"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "query": "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6",
  "result": true,
  "planet_positions": {
    "JUPITER": 9,
    "KETU": 6,
    "SUN": 1,
    "MERCURY": 1,
    "VENUS": 2,
    "MOON": 4,
    "MARS": 11,
    "SATURN": 11,
    "RAHU": 12
  }
}
```

---

## Postman Testing

### **Import Collection**
1. Import `New_Syntax_Query_Postman.json` into Postman
2. Set `base_url` = `http://127.0.0.1:5003`
3. Run "Login to Get Token" to authenticate

### **Test Sequence**
1. **Authentication** → Get access token
2. **Debug Chart** → Verify chart data structure
3. **Individual Tests** → Test each condition separately
4. **Complex Query** → Test full OR logic
5. **Comparison Tests** → Verify backward compatibility

---

## Key Benefits

### **1. Clarity**
- **Old**: `Ketu IN house6 WITH Ruling_Planet` (ambiguous)
- **New**: `Ketu IN house6 WITH Ruling_Planet_house6` (explicit)

### **2. Flexibility**
- Same-house: `Mars IN house11 WITH Ruling_Planet_house11`
- Cross-house: `Mars IN house11 WITH Ruling_Planet_house10`

### **3. Backward Compatibility**
- Legacy syntax still works
- No breaking changes to existing queries

### **4. Enhanced Logic**
- Complex OR/AND combinations
- Mixed basic and advanced conditions
- Clear parsing and evaluation

---

## Real-World Examples

### **Self-Ruling Planets**
```
Jupiter IN house9 WITH Ruling_Planet_house9  → TRUE (Jupiter rules Sagittarius)
Sun IN house5 WITH Ruling_Planet_house5      → TRUE (Sun rules Leo)
Moon IN house4 WITH Ruling_Planet_house4     → TRUE (Moon rules Cancer)
```

### **Non-Self-Ruling**
```
Mars IN house11 WITH Ruling_Planet_house11   → FALSE (Mars in Virgo, Mercury rules Virgo)
Ketu IN house6 WITH Ruling_Planet_house6     → FALSE (Ketu in Aquarius, Saturn rules Aquarius)
```

### **Complex Combinations**
```
Jupiter IN house9 WITH Ruling_Planet_house9 OR Sun IN house5 WITH Ruling_Planet_house5
→ TRUE OR TRUE = TRUE (both self-ruling)

Mars IN house11 WITH Ruling_Planet_house11 AND Ketu IN house6
→ FALSE AND TRUE = FALSE (Mars not with ruler, but Ketu in position)
```

---

## MongoDB Integration

The new syntax works seamlessly with the MongoDB structure:

### **Chart Data Structure**
```json
{
  "chart_data": {
    "D1": {
      "houses": [
        {
          "house_number": 9,
          "house_name": "DHANUSU",
          "planets": ["jupiter"]
        }
      ]
    }
  }
}
```

### **Dynamic Ruling Planet Determination**
1. Read `house_name` from MongoDB (e.g., "DHANUSU")
2. Map to ruling planet (DHANUSU → JUPITER)
3. Check if ruling planet is in specified house
4. Return accurate result based on actual chart data

---

## Error Handling

### **Invalid Syntax**
```json
{
  "success": false,
  "message": "Invalid query format",
  "error_code": "INVALID_QUERY_FORMAT"
}
```

### **Missing Chart Data**
```json
{
  "success": false,
  "message": "Chart data not found",
  "error_code": "CHART_DATA_NOT_FOUND"
}
```

### **Authentication Required**
```json
{
  "msg": "Missing Authorization Header"
}
```

---

The new explicit syntax makes the Rule Engine more powerful, clear, and user-friendly while maintaining full backward compatibility!
