# Fortune Lens API Documentation

This document provides details about the API endpoints available in the Fortune Lens application.

## Base URL

All API endpoints are prefixed with `/api`.

## Authentication

Most endpoints require authentication using JWT (JSON Web Token). To authenticate, include the JW<PERSON> token in the Authorization header:

```
Authorization: Bearer <token>
```

You can obtain a token by logging in through the `/api/auth/login` endpoint.

## Endpoints

### Authentication

#### Register a new user

- **URL**: `/api/auth/register`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123",
    "name": "<PERSON>",
    "mobile": "1234567890"
  }
  ```
- **Success Response**: `201 Created`
  ```json
  {
    "message": "User registered successfully",
    "user_profile_id": "123456789"
  }
  ```

#### Login

- **URL**: `/api/auth/login`
- **Method**: `POST`
- **Auth required**: No
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password123"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Login successful",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "user_profile_id": "123456789",
      "email": "<EMAIL>",
      "name": "John Doe"
    }
  }
  ```

### User Profile

#### Get current user profile

- **URL**: `/api/auth/me`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`
  ```json
  {
    "user_profile_id": "123456789",
    "email": "<EMAIL>",
    "name": "John Doe",
    "mobile": "1234567890",
    "created_at": "2023-01-01T00:00:00Z"
  }
  ```

#### Update user profile

- **URL**: `/api/users/<user_profile_id>`
- **Method**: `PUT`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "John Smith",
    "mobile": "0987654321"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "User updated successfully",
    "user": {
      "user_profile_id": "123456789",
      "email": "<EMAIL>",
      "name": "John Smith",
      "mobile": "0987654321"
    }
  }
  ```

### Member Profiles

#### Create a member profile

- **URL**: `/api/member-profiles`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "Jane Doe",
    "user_birthdate": "1990-01-01",
    "user_birthtime": "12:00:00",
    "user_birthplace": "New York, USA",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "user_state": "New York",
    "user_country": "USA"
  }
  ```
- **Success Response**: `201 Created`
  ```json
  {
    "message": "Member profile created successfully",
    "member_profile": {
      "member_profile_id": "987654321",
      "user_profile_id": "123456789",
      "name": "Jane Doe",
      "user_birthdate": "1990-01-01",
      "user_birthtime": "12:00:00",
      "user_birthplace": "New York, USA",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "user_state": "New York",
      "user_country": "USA",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

#### Get all member profiles for current user

- **URL**: `/api/member-profiles`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`
  ```json
  {
    "member_profiles": [
      {
        "member_profile_id": "987654321",
        "user_profile_id": "123456789",
        "name": "Jane Doe",
        "user_birthdate": "1990-01-01",
        "user_birthtime": "12:00:00",
        "user_birthplace": "New York, USA",
        "latitude": 40.7128,
        "longitude": -74.0060,
        "user_state": "New York",
        "user_country": "USA",
        "created_at": "2023-01-01T00:00:00Z"
      }
    ]
  }
  ```

#### Get a specific member profile

- **URL**: `/api/member-profiles/<member_profile_id>`
- **Method**: `GET`
- **Auth required**: Yes
- **Success Response**: `200 OK`
  ```json
  {
    "member_profile_id": "987654321",
    "user_profile_id": "123456789",
    "name": "Jane Doe",
    "user_birthdate": "1990-01-01",
    "user_birthtime": "12:00:00",
    "user_birthplace": "New York, USA",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "user_state": "New York",
    "user_country": "USA",
    "created_at": "2023-01-01T00:00:00Z"
  }
  ```

#### Update a member profile

- **URL**: `/api/member-profiles/<member_profile_id>`
- **Method**: `PUT`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "name": "Jane Smith",
    "user_birthtime": "13:00:00"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Member profile updated successfully",
    "member_profile": {
      "member_profile_id": "987654321",
      "user_profile_id": "123456789",
      "name": "Jane Smith",
      "user_birthdate": "1990-01-01",
      "user_birthtime": "13:00:00",
      "user_birthplace": "New York, USA",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "user_state": "New York",
      "user_country": "USA",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

#### Delete a member profile

- **URL**: `/api/member-profiles/<member_profile_id>`
- **Method**: `DELETE`
- **Auth required**: Yes
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Member profile deleted successfully"
  }
  ```

### Charts

#### Generate chart for a member

- **URL**: `/api/charts/generate`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "member_profile_id": "987654321",
    "chart_type": "all"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Chart generated successfully",
    "chart_data": {
      "chart_info": {
        "name": "Jane Smith",
        "birthdate": "1990-01-01",
        "birthtime": "13:00:00",
        "birthplace": "New York, USA"
      },
      "divisional_charts": {
        "D1": {
          "houses": [
            {
              "house_number": 1,
              "house_name": "Aries",
              "planets": ["Sun"],
              "planet_degrees": ["15°30'45\""],
              "planet_nakshatras": ["Bharani"],
              "planet_padas": [2]
            },
            // ... other houses
          ],
          "dashas": {
            "current_dasha": "Venus",
            "current_bhukti": "Mercury",
            "remaining_years": 3,
            "remaining_months": 6,
            "remaining_days": 15
          }
        },
        // ... other divisional charts (D2-D23)
      }
    }
  }
  ```

### Marriage Matching

#### Calculate marriage compatibility

- **URL**: `/api/marriage-matching`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "boy_member_profile_id": "987654321",
    "girl_member_profile_id": "123987456"
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Marriage compatibility calculated successfully",
    "compatibility_score": 28,
    "max_score": 36,
    "compatibility_percentage": 77.78,
    "details": {
      "varna": {
        "score": 1,
        "max_score": 1,
        "description": "Varna match is good"
      },
      "vasiya": {
        "score": 2,
        "max_score": 2,
        "description": "Vasiya match is good"
      },
      // ... other compatibility factors
    },
    "recommendation": "This is a good match with 77.78% compatibility."
  }
  ```

### Panchanga

#### Calculate daily panchanga

- **URL**: `/api/panchanga/daily`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "date": "2023-04-01",
    "latitude": 13.0827,
    "longitude": 80.2707,
    "timezone": 5.5
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Panchanga calculated successfully",
    "panchanga_data": {
      "date": "2023-04-01",
      "location": "Chennai, India",
      "sunrise": "06:05:23",
      "sunset": "18:23:45",
      "tithi": "Shukla Ekadashi",
      "nakshatra": "Rohini",
      "yoga": "Siddhi",
      "karana": "Bava",
      "weekday": "Saturday",
      "month": "Chaitra",
      "year": "Shubhakrut"
    }
  }
  ```

#### Calculate Tamil panchanga

- **URL**: `/api/panchanga/tamil`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "date": "2023-04-01",
    "latitude": 13.0827,
    "longitude": 80.2707,
    "timezone": 5.5
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "message": "Tamil panchanga calculated successfully",
    "panchanga_data": {
      "date": "2023-04-01",
      "location": "Chennai, India",
      "tamil_date": "Panguni 19",
      "tamil_year": "Pilava",
      "sunrise": "06:05:23",
      "sunset": "18:23:45",
      "tithi": "Shukla Ekadashi",
      "nakshatra": "Rohini",
      "yoga": "Siddhi",
      "karana": "Bava",
      "weekday": "Saturday",
      "special_days": ["Ekadashi"]
    }
  }
  ```

### Marriage Date Prediction

#### Predict marriage dates

- **URL**: `/api/marriage-date-prediction`
- **Method**: `POST`
- **Auth required**: Yes
- **Request Body**:
  ```json
  {
    "member_id": "65f1a2b3c4d5e6f7a8b9c0d1",
    "start_age": 21,
    "end_age": 40
  }
  ```
- **Success Response**: `200 OK`
  ```json
  {
    "success": true,
    "member": {
      "id": "65f1a2b3c4d5e6f7a8b9c0d1",
      "name": "John Doe",
      "birth_date": "1990-05-15"
    },
    "prediction_age_range": {
      "start_age": 21,
      "end_age": 40,
      "start_date": "15-05-2011",
      "end_date": "15-05-2030"
    },
    "marriage_houses": ["RISHABAM", "THULAM"],
    "rasi": "KADAGAM",
    "potential_marriage_periods": [
      "05-2015 to 08-2016",
      "11-2022 to 03-2024"
    ],
    "jupiter_aspects": {
      "houses": {
        "5th_aspect": [
          {"period": "VENUS-JUPITER", "start_date": "15-06-2015", "end_date": "10-08-2015"}
        ],
        "7th_aspect": [],
        "9th_aspect": [],
        "stay": []
      },
      "rasi": {
        "5th_aspect": [],
        "7th_aspect": [],
        "9th_aspect": [],
        "stay": []
      }
    },
    "venus_aspects": {
      "houses": {
        "7th_aspect": [],
        "stay": []
      },
      "rasi": {
        "7th_aspect": [],
        "stay": []
      }
    },
    "generated_at": "2023-05-08T15:18:05.321496"
  }
  ```

#### Get marriage date prediction

- **URL**: `/api/marriage-date-prediction/{member_id}`
- **Method**: `GET`
- **Auth required**: Yes
- **URL Parameters**:
  - `member_id`: ID of the member profile
- **Query Parameters**:
  - `start_age`: Starting age for prediction (default: 21)
  - `end_age`: Ending age for prediction (default: 40)
- **Success Response**: Same as POST endpoint

#### Get marriage date prediction by user

- **URL**: `/api/marriage-date-prediction/user/{user_profile_id}/{member_profile_id}`
- **Method**: `GET`
- **Auth required**: Yes
- **URL Parameters**:
  - `user_profile_id`: ID of the user profile
  - `member_profile_id`: ID of the member profile
- **Query Parameters**:
  - `start_age`: Starting age for prediction (default: 21)
  - `end_age`: Ending age for prediction (default: 40)
- **Success Response**: Same as POST endpoint

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request

```json
{
  "message": "Validation error",
  "errors": {
    "field_name": ["Error message"]
  }
}
```

### 401 Unauthorized

```json
{
  "message": "Unauthorized access"
}
```

### 404 Not Found

```json
{
  "message": "Resource not found"
}
```

### 500 Internal Server Error

```json
{
  "message": "Internal server error"
}
```
