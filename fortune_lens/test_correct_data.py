#!/usr/bin/env python3
"""
Test with Correct Database Data
Tests using the actual planet positions from the database
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

def show_actual_data():
    """Show the actual database data"""
    print("=" * 80)
    print("📊 ACTUAL DATABASE DATA")
    print("=" * 80)
    
    actual_data = {
        1: ["lagnam", "rahu"],
        2: [],
        3: [],
        4: [],
        5: ["moon"],
        6: [],
        7: ["ketu"],
        8: ["mercury"],
        9: ["sun"],
        10: ["jupiter", "venus"],
        11: ["mars"],
        12: ["saturn"]
    }
    
    print("Real planet positions in the database:")
    
    for house_num, planets in actual_data.items():
        if planets:
            planet_list = ", ".join(planets).upper()
            print(f"✅ House {house_num:2d}: {planet_list}")
        else:
            print(f"❌ House {house_num:2d}: (empty)")

def show_correct_test_cases():
    """Show correct test cases based on actual data"""
    print("\n" + "=" * 80)
    print("✅ CORRECT TEST CASES")
    print("=" * 80)
    
    test_cases = [
        {
            "category": "Same House Together Tests",
            "tests": [
                {
                    "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
                    "house1_planets": ["LAGNAM", "RAHU"],
                    "house2_planets": ["LAGNAM", "RAHU"],
                    "expected_relationships": ["LAGNAM_TO_RAHU", "RAHU_TO_LAGNAM"],
                    "expected_together": True,
                    "together_type": "same_house"
                },
                {
                    "query": "10th_House_Planet IS RELATED TO 10th_House_Planet",
                    "house1_planets": ["JUPITER", "VENUS"],
                    "house2_planets": ["JUPITER", "VENUS"],
                    "expected_relationships": ["JUPITER_TO_VENUS", "VENUS_TO_JUPITER"],
                    "expected_together": True,
                    "together_type": "same_house"
                }
            ]
        },
        {
            "category": "Cross-House Tests",
            "tests": [
                {
                    "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
                    "house1_planets": ["LAGNAM", "RAHU"],
                    "house2_planets": ["JUPITER", "VENUS"],
                    "expected_relationships": ["LAGNAM_TO_JUPITER", "LAGNAM_TO_VENUS", "RAHU_TO_JUPITER", "RAHU_TO_VENUS"],
                    "expected_together": False,
                    "together_type": "none"
                },
                {
                    "query": "9th_House_Planet IS RELATED TO 11th_House_Planet",
                    "house1_planets": ["SUN"],
                    "house2_planets": ["MARS"],
                    "expected_relationships": ["SUN_TO_MARS", "MARS_TO_SUN"],
                    "expected_together": False,
                    "together_type": "none"
                },
                {
                    "query": "5th_House_Planet IS RELATED TO 7th_House_Planet",
                    "house1_planets": ["MOON"],
                    "house2_planets": ["KETU"],
                    "expected_relationships": ["MOON_TO_KETU", "KETU_TO_MOON"],
                    "expected_together": False,
                    "together_type": "none"
                }
            ]
        },
        {
            "category": "Empty House Tests",
            "tests": [
                {
                    "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
                    "house1_planets": [],
                    "house2_planets": ["JUPITER", "VENUS"],
                    "expected_relationships": [],
                    "expected_together": False,
                    "together_type": "none",
                    "note": "House 6 is empty, so no relationships possible"
                },
                {
                    "query": "2nd_House_Planet IS RELATED TO 3rd_House_Planet",
                    "house1_planets": [],
                    "house2_planets": [],
                    "expected_relationships": [],
                    "expected_together": False,
                    "together_type": "none",
                    "note": "Both houses are empty"
                }
            ]
        }
    ]
    
    for category in test_cases:
        print(f"\n{category['category']}:")
        
        for i, test in enumerate(category['tests'], 1):
            print(f"\n  {i}. {test['query']}")
            print(f"     House 1 Planets: {test['house1_planets'] if test['house1_planets'] else '(empty)'}")
            print(f"     House 2 Planets: {test['house2_planets'] if test['house2_planets'] else '(empty)'}")
            print(f"     Expected Relationships: {len(test['expected_relationships'])}")
            if test['expected_relationships']:
                for rel in test['expected_relationships']:
                    print(f"       • {rel}")
            print(f"     Expected Together: {test['expected_together']}")
            if 'note' in test:
                print(f"     Note: {test['note']}")

def show_your_original_query():
    """Show analysis of your original query with correct data"""
    print("\n" + "=" * 80)
    print("🎯 YOUR ORIGINAL QUERY WITH CORRECT DATA")
    print("=" * 80)
    
    print("Your Query: '6th_House_Planet IS RELATED TO 10th_House_Planet'")
    
    print("\n❌ Previous Wrong Analysis:")
    print("   • House 6: KETU")
    print("   • House 10: (empty)")
    print("   • Result: FALSE (no planets in House 10)")
    
    print("\n✅ Correct Analysis:")
    print("   • House 6: (empty)")
    print("   • House 10: JUPITER, VENUS")
    print("   • Result: FALSE (no planets in House 6)")
    print("   • Reason: House 6 is empty, so no relationships possible")

def show_curl_tests():
    """Show curl commands for testing with correct data"""
    print("\n" + "=" * 80)
    print("🌐 CURL TESTS WITH CORRECT DATA")
    print("=" * 80)
    
    tests = [
        {
            "name": "Test Same House Together (House 1)",
            "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
            "expected": "Should show LAGNAM_TO_RAHU relationship"
        },
        {
            "name": "Test Same House Together (House 10)",
            "query": "10th_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": "Should show JUPITER_TO_VENUS relationship"
        },
        {
            "name": "Test Cross-House (1st to 10th)",
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": "Should show 4 cross-house relationships"
        },
        {
            "name": "Test Your Original Query",
            "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": "Should return FALSE (House 6 is empty)"
        }
    ]
    
    for i, test in enumerate(tests, 1):
        print(f"\n{i}. {test['name']}:")
        print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-planet-relationship" \\')
        print('  -H "Content-Type: application/json" \\')
        print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
        print(f"  -d '{{\"user_profile_id\": \"1\", \"member_profile_id\": \"1\", \"query\": \"{test['query']}\", \"chart_type\": \"D1\"}}'")
        print(f"Expected: {test['expected']}")

def show_postman_updates():
    """Show how to update Postman tests with correct data"""
    print("\n" + "=" * 80)
    print("📋 POSTMAN TEST UPDATES")
    print("=" * 80)
    
    print("Update your Postman tests with these correct expectations:")
    
    postman_tests = [
        {
            "test_name": "Test Same House Together - House 1",
            "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
            "expectations": [
                "response.house1_planets should include ['LAGNAM', 'RAHU']",
                "response.house2_planets should include ['LAGNAM', 'RAHU']",
                "response.summary.together should be true",
                "response.summary.together_types.same_house should be true",
                "Should have LAGNAM_TO_RAHU or RAHU_TO_LAGNAM relationship"
            ]
        },
        {
            "test_name": "Test Cross-House - 1st to 10th",
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "expectations": [
                "response.house1_planets should include ['LAGNAM', 'RAHU']",
                "response.house2_planets should include ['JUPITER', 'VENUS']",
                "Should have 4 cross-house relationships",
                "Should NOT have same-house relationships",
                "All directions should show House 1 ↔ House 10"
            ]
        },
        {
            "test_name": "Test Your Original Query",
            "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
            "expectations": [
                "response.house1_planets should be empty []",
                "response.house2_planets should include ['JUPITER', 'VENUS']",
                "response.overall_result should be false",
                "response.planet_relationships should be empty {}",
                "Details should mention 'No planets found in House 6'"
            ]
        }
    ]
    
    for i, test in enumerate(postman_tests, 1):
        print(f"\n{i}. {test['test_name']}:")
        print(f"   Query: {test['query']}")
        print(f"   Expectations:")
        for expectation in test['expectations']:
            print(f"     • {expectation}")

def main():
    """Main testing function"""
    print("CORRECT DATABASE DATA TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Using actual database data instead of wrong assumptions")
    
    # Show all information
    show_actual_data()
    show_correct_test_cases()
    show_your_original_query()
    show_curl_tests()
    show_postman_updates()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ Actual database data verified")
    print("✅ Correct test cases provided")
    print("✅ Your original query analyzed with real data")
    print("✅ Updated curl commands and Postman tests")
    
    print("\n🎯 KEY FINDINGS:")
    print("✅ House 1: LAGNAM, RAHU (2 planets - good for same-house tests)")
    print("✅ House 10: JUPITER, VENUS (2 planets - good for same-house tests)")
    print("✅ Your query (6th→10th): House 6 empty, House 10 has planets")
    print("✅ Best cross-house test: 1st→10th (2×2 = 4 relationships)")
    
    print("\n📋 NEXT STEPS:")
    print("1. Use the corrected test cases")
    print("2. Update Postman collection with real data expectations")
    print("3. Test same-house relationships: House 1 and House 10")
    print("4. Test cross-house relationships: 1st↔10th, 9th↔11th")
    print("5. Verify your original query returns FALSE correctly")

if __name__ == "__main__":
    main()
