# Complete Logical Operations Guide (OR, AND, NOT)

## Overview

The Rule Engine now supports comprehensive logical operations including **OR**, **AND**, and **NOT** with proper operator precedence and complex combinations.

## Operator Precedence

```
NOT > AND > OR
```

**Example**: `NOT Jupiter IN house1 AND Ketu IN house6 OR Mars IN house11`
**Interpreted as**: `((NOT Jupiter IN house1) AND (Ketu IN house6)) OR (Mars IN house11)`

---

## 1. OR Operations

### **Syntax**: `Condition1 OR Condition2`

### **Truth Table**:
| Condition1 | Condition2 | Result |
|------------|------------|--------|
| TRUE       | TRUE       | TRUE   |
| TRUE       | FALSE      | TRUE   |
| FALSE      | TRUE       | TRUE   |
| FALSE      | FALSE      | FALSE  |

### **Examples**:
```
Jupiter IN house9 OR Mars IN house1          → TRUE (TRUE OR FALSE)
Jupiter IN house1 OR Ketu IN house6          → TRUE (FALSE OR TRUE)
Jupiter IN house1 OR Mars IN house1          → FALSE (FALSE OR FALSE)
```

### **API Test**:
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Jupiter IN house9 OR Ketu IN house6",
    "chart_type": "D1"
  }'
```

---

## 2. AND Operations

### **Syntax**: `Condition1 AND Condition2`

### **Truth Table**:
| Condition1 | Condition2 | Result |
|------------|------------|--------|
| TRUE       | TRUE       | TRUE   |
| TRUE       | FALSE      | FALSE  |
| FALSE      | TRUE       | FALSE  |
| FALSE      | FALSE      | FALSE  |

### **Examples**:
```
Sun IN house1 AND Mercury IN house1          → TRUE (TRUE AND TRUE)
Jupiter IN house9 AND Mars IN house1         → FALSE (TRUE AND FALSE)
Jupiter IN house1 AND Mars IN house1         → FALSE (FALSE AND FALSE)
```

### **API Test**:
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Sun IN house1 AND Mercury IN house1",
    "chart_type": "D1"
  }'
```

---

## 3. NOT Operations

### **Syntax**: `NOT Condition`

### **Truth Table**:
| Condition | Result |
|-----------|--------|
| TRUE      | FALSE  |
| FALSE     | TRUE   |

### **Examples**:
```
NOT Jupiter IN house1                         → TRUE (NOT FALSE)
NOT Jupiter IN house9                         → FALSE (NOT TRUE)
NOT Mars IN house11 WITH Ruling_Planet_house11 → TRUE (NOT FALSE)
```

### **API Test**:
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "NOT Jupiter IN house1",
    "chart_type": "D1"
  }'
```

---

## 4. Complex Combinations

### **Mixed Operations**:

#### **NOT + AND**:
```
NOT Jupiter IN house1 AND Ketu IN house6     → TRUE (TRUE AND TRUE)
```

#### **NOT + OR**:
```
NOT Jupiter IN house1 OR Mars IN house1      → TRUE (TRUE OR FALSE)
```

#### **OR with AND Precedence**:
```
Jupiter IN house9 OR Sun IN house1 AND Mercury IN house1
→ Jupiter IN house9 OR (Sun IN house1 AND Mercury IN house1)
→ TRUE OR TRUE = TRUE
```

#### **Multiple NOT Operations**:
```
NOT Jupiter IN house1 AND NOT Mars IN house1 → TRUE (TRUE AND TRUE)
```

---

## 5. Advanced Rules with Logic

### **WITH Ruling Planet + OR**:
```
Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6
→ TRUE OR TRUE = TRUE
```

### **NOT WITH Ruling Planet**:
```
NOT Mars IN house11 WITH Ruling_Planet_house11
→ NOT FALSE = TRUE
```

### **Complex Advanced Combinations**:
```
Jupiter IN house9 WITH Ruling_Planet_house9 AND NOT Mars IN house1 OR Venus IN house2
→ (TRUE AND TRUE) OR TRUE = TRUE
```

---

## 6. Supported Rule Types

### **Basic Rules**:
- `Planet IN house#`
- `Planet NOT IN house#`
- `NOT Planet IN house#`

### **Advanced Rules**:
- `Planet IN house# WITH Ruling_Planet_house#`
- `NOT Planet IN house# WITH Ruling_Planet_house#`
- `Planet IS RELATED TO House#_Ruling_Planet`
- `NOT Planet IS RELATED TO House#_Ruling_Planet`

### **Logical Combinations**:
- `Condition1 OR Condition2`
- `Condition1 AND Condition2`
- `NOT Condition1 AND Condition2`
- `Condition1 OR NOT Condition2`
- `NOT Condition1 OR NOT Condition2`

---

## 7. Real-World Examples

### **Example 1: Self-Ruling Planet Check**
```
Query: Jupiter IN house9 WITH Ruling_Planet_house9 OR Sun IN house5 WITH Ruling_Planet_house5

Analysis:
- Jupiter in House 9 (Sagittarius) WITH Jupiter (self-ruling) → TRUE
- Sun in House 5 (Leo) WITH Sun (self-ruling) → TRUE
- Result: TRUE OR TRUE = TRUE
```

### **Example 2: Complex Medical Profession Rule**
```
Query: Mars IN house6 WITH Ruling_Planet_house6 AND NOT Saturn IN house8 OR Jupiter IN house10

Analysis:
- Mars in House 6 WITH ruling planet → Check chart data
- NOT Saturn in House 8 → TRUE (Saturn in House 11)
- Jupiter in House 10 → FALSE (Jupiter in House 9)
- Result: (FALSE AND TRUE) OR FALSE = FALSE
```

### **Example 3: Marriage Compatibility**
```
Query: Venus IN house7 WITH Ruling_Planet_house7 OR Moon IN house4 AND NOT Mars IN house7

Analysis:
- Venus in House 7 WITH ruling planet → Check chart data
- Moon in House 4 → TRUE
- NOT Mars in House 7 → TRUE (Mars in House 11)
- Result: Depends on Venus condition OR (TRUE AND TRUE)
```

---

## 8. API Response Format

### **Successful Evaluation**:
```json
{
  "success": true,
  "query": "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6",
  "chart_type": "D1",
  "result": true,
  "planet_positions": {
    "JUPITER": 9,
    "KETU": 6,
    "SUN": 1,
    "MERCURY": 1,
    "VENUS": 2,
    "MOON": 4,
    "MARS": 11,
    "SATURN": 11,
    "RAHU": 12
  },
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

### **Error Response**:
```json
{
  "success": false,
  "message": "Invalid query format",
  "error_code": "INVALID_QUERY_FORMAT"
}
```

---

## 9. Postman Testing

### **Import Collection**:
1. Import `Logical_Operations_Postman.json`
2. Set `base_url` = `http://127.0.0.1:5003`
3. Run "Login to Get Token"

### **Test Categories**:
1. **OR Operations** - Test all OR combinations
2. **AND Operations** - Test all AND combinations  
3. **NOT Operations** - Test negation
4. **Complex Combinations** - Test mixed logic
5. **Advanced Rules** - Test with ruling planets
6. **Your Original Query** - Test specific query

---

## 10. Key Features

### ✅ **Complete Logical Support**:
- OR, AND, NOT operations
- Proper operator precedence
- Complex combinations
- Advanced rule integration

### ✅ **MongoDB Integration**:
- Dynamic ruling planet determination
- Actual chart data usage
- Multiple chart type support

### ✅ **Error Handling**:
- Invalid syntax detection
- Missing data handling
- Clear error messages

### ✅ **Backward Compatibility**:
- Legacy syntax support
- No breaking changes
- Enhanced functionality

---

## 11. Testing Your Query

**Your Original Query**:
```
Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6
```

**Expected Result**: `TRUE`

**Breakdown**:
- Condition 1: Jupiter in House 9 WITH Jupiter (self-ruling) → `TRUE`
- Condition 2: Ketu in House 6 → `TRUE`
- Final: `TRUE OR TRUE = TRUE`

**Test Command**:
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Jupiter IN house9 WITH Ruling_Planet_house9 OR Ketu IN house6",
    "chart_type": "D1"
  }'
```

The Rule Engine now provides complete logical operation support with proper precedence, making it powerful enough for complex astrological rule evaluation!
