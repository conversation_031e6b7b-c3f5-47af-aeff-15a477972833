# Nakshatra-based Query Guide

## Overview

The Rule Engine now supports advanced nakshatra (star) based queries to check if house ruling planets are placed in the stars of specific planets. This enables sophisticated astrological analysis based on nakshatra lordship.

## Your Query

**Target Query:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu
```

**Translation:** Check if the 6th house ruling planet is placed in the star of Ketu OR if the 10th house ruling planet is placed in the star of Ketu.

---

## Query Analysis

### **Chart Data Analysis**

**House Ruling Planets:**
- House 6: KUMBAM (Aquarius) → ruled by **SATURN**
- House 10: MAGARAM (Capricorn) → ruled by **SATURN**

**Planet Placements with Nakshatras:**
- **SATURN**: Located in House 11, placed in **MAGAM** nakshatra

**Nakshatra Lordship:**
- **MAGAM** nakshatra → ruled by **KETU**

### **Step-by-Step Evaluation**

#### **Condition 1: House6_Ruling_Planet IN_STAR_OF Ketu**
1. ✅ House 6 ruling planet → SATURN
2. ✅ SATURN's nakshatra → MAGAM
3. ✅ MAGAM's lord → KETU
4. ✅ Result: **TRUE** (Saturn is in Ketu's star)

#### **Condition 2: House10_Ruling_Planet IN_STAR_OF Ketu**
1. ✅ House 10 ruling planet → SATURN
2. ✅ SATURN's nakshatra → MAGAM
3. ✅ MAGAM's lord → KETU
4. ✅ Result: **TRUE** (Saturn is in Ketu's star)

#### **Final OR Logic:**
```
TRUE OR TRUE = TRUE ✅
```

**Expected API Result: TRUE**

---

## Nakshatra Concepts

### **What are Nakshatras?**
- Nakshatras are 27 lunar mansions (stars) in Vedic astrology
- Each planet is placed in a specific nakshatra based on its degree
- Each nakshatra has a ruling planet (lord)

### **Ketu's Nakshatras:**
1. **ASHWINI** (0°00' - 13°20' Aries)
2. **MAGAM** (0°00' - 13°20' Leo)
3. **MOOLAM** (0°00' - 13°20' Sagittarius)

### **Complete Nakshatra Lordship:**
```
ASHWINI → KETU        MAGAM → KETU         MOOLAM → KETU
BARANI → VENUS        POORAM → VENUS       POORADAM → VENUS
KARTHIKAI → SUN       UTHIRAM → SUN        UTHIRADAM → SUN
ROHINI → MOON         HASTHAM → MOON       THIRUVONAM → MOON
MIRIGASIRISHAM → MARS CHITHIRAI → MARS     AVITTAM → MARS
THIRUVADIRAI → RAHU   SWATHI → RAHU        SADAYAM → RAHU
PUNARPOOSAM → JUPITER VISAGAM → JUPITER    POORATADHI → JUPITER
POOSAM → SATURN       ANUSHAM → SATURN     UTHIRATTADHI → SATURN
AYILYAM → MERCURY     KETTAI → MERCURY     REVATHI → MERCURY
```

---

## New Query Syntax

### **Basic Nakshatra Query:**
```
House#_Ruling_Planet IN_STAR_OF Planet
```

### **Examples:**
```
House6_Ruling_Planet IN_STAR_OF Ketu
House10_Ruling_Planet IN_STAR_OF Venus
House1_Ruling_Planet IN_STAR_OF Jupiter
```

### **Complex Combinations:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu
House1_Ruling_Planet IN_STAR_OF Mars AND Ketu IN house6
NOT House7_Ruling_Planet IN_STAR_OF Saturn
```

---

## API Testing

### **Authentication:**
```bash
curl -X POST "http://127.0.0.1:5003/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### **Test Individual Conditions:**

#### **Test 1: House 6 Ruling Planet in Ketu's Star**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "House6_Ruling_Planet IN_STAR_OF Ketu",
    "chart_type": "D1"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "query": "House6_Ruling_Planet IN_STAR_OF Ketu",
  "result": true,
  "chart_type": "D1"
}
```

#### **Test 2: Complex OR Query**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu",
    "chart_type": "D1"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "query": "House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu",
  "result": true,
  "chart_type": "D1"
}
```

---

## MongoDB Integration

### **Chart Data Structure:**
```json
{
  "chart_data": {
    "D1": {
      "houses": [
        {
          "house_number": 11,
          "house_name": "KANNI",
          "planets": ["saturn"],
          "planet_degrees": {"saturn": "5°20'"},
          "planet_nakshatras": {"saturn": "MAGAM"}
        }
      ]
    }
  }
}
```

### **Data Flow:**
1. **Get House Ruling Planet**: House 6 (KUMBAM) → SATURN
2. **Find Planet's Nakshatra**: SATURN → MAGAM (from `planet_nakshatras`)
3. **Get Nakshatra Lord**: MAGAM → KETU
4. **Compare**: KETU == KETU → TRUE

---

## Real-World Applications

### **Medical Profession Analysis:**
```
House6_Ruling_Planet IN_STAR_OF Ketu AND Mars IN house6 OR Jupiter IN house10
```

### **Marriage Compatibility:**
```
House7_Ruling_Planet IN_STAR_OF Venus OR House7_Ruling_Planet IN_STAR_OF Jupiter
```

### **Career Analysis:**
```
House10_Ruling_Planet IN_STAR_OF Sun OR House10_Ruling_Planet IN_STAR_OF Mars
```

### **Health Analysis:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House8_Ruling_Planet IN_STAR_OF Saturn
```

---

## Advanced Features

### **Mixed Logic Support:**
```
House6_Ruling_Planet IN_STAR_OF Ketu AND Ketu IN house6
NOT House1_Ruling_Planet IN_STAR_OF Venus
House6_Ruling_Planet IN_STAR_OF Ketu OR Jupiter IN house9 WITH Ruling_Planet_house9
```

### **Multiple Chart Types:**
```
House6_Ruling_Planet IN_STAR_OF Ketu  # D1 chart
House9_Ruling_Planet IN_STAR_OF Jupiter  # D9 chart (Navamsa)
```

### **Error Handling:**
- Invalid nakshatra names
- Missing chart data
- Planets not found in chart
- Invalid house numbers

---

## Postman Testing

### **Import Collection:**
1. Import `Nakshatra_Query_Postman.json`
2. Set `base_url` = `http://127.0.0.1:5003`
3. Run "Login to Get Token"

### **Test Categories:**
1. **Individual Nakshatra Tests** - Test each condition separately
2. **Complex OR Query** - Test your target query
3. **Mixed Logic Tests** - Combine with other rule types
4. **Comprehensive Tests** - Multiple house combinations

---

## Key Benefits

### ✅ **Advanced Astrological Analysis:**
- Nakshatra-based rule evaluation
- House ruling planet star analysis
- Complex logical combinations

### ✅ **MongoDB Integration:**
- Uses actual `planet_nakshatras` data
- Dynamic nakshatra lord determination
- Multiple chart type support

### ✅ **Comprehensive Logic:**
- OR, AND, NOT operations
- Mixed rule type combinations
- Proper operator precedence

### ✅ **Real-World Applications:**
- Medical profession analysis
- Marriage compatibility
- Career predictions
- Health assessments

---

## Summary

**Your Query Result:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu → TRUE
```

**Explanation:**
- Both House 6 and House 10 are ruled by Saturn
- Saturn is placed in MAGAM nakshatra
- MAGAM is ruled by Ketu
- Therefore, both conditions are TRUE
- Final result: TRUE OR TRUE = TRUE

The Rule Engine now supports sophisticated nakshatra-based analysis, enabling advanced astrological rule evaluation based on star lordship patterns!
