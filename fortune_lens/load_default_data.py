"""
<PERSON><PERSON><PERSON> to load default astrological data from Excel and store it in MongoDB.
This script should be run once to initialize the database with default values.
"""

import os
import sys

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the Flask app to initialize the MongoDB connection
from app import create_app
from app.services.career_prediction.load_default_data import store_in_mongodb

if __name__ == "__main__":
    # Create the Flask app to initialize the MongoDB connection
    app = create_app()

    # Use the app context to access the MongoDB connection
    with app.app_context():
        # Store the data in MongoDB
        store_in_mongodb()
