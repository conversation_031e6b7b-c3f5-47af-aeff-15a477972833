# Rule Engine API Fixes and Improvements

## Summary

This document outlines the comprehensive fixes applied to the rule engine API to correctly reference MongoDB chart_data structure and improve overall functionality.

## Issues Fixed

### 1. MongoDB Chart Data Access Issues
- **Problem**: Inconsistent logic for finding astro data using different field combinations
- **Solution**: Enhanced `get_chart_data()` function with multiple fallback strategies
- **Changes**: Added support for different field name combinations and ObjectId lookups

### 2. Planet-House Mapping Issues
- **Problem**: Rule engine expected different data structure than what was stored in MongoDB
- **Solution**: Updated `get_planet_house_mapping()` to handle multiple chart data structures
- **Changes**: 
  - Support for both `houses` array structure and `planets_precise` structure
  - Added planet name normalization
  - Added sign-to-house mapping for planets_precise structure

### 3. Planet Name Standardization
- **Problem**: Inconsistent planet names between rule engine and MongoDB
- **Solution**: Added `normalize_planet_name()` function
- **Changes**: Standardized all planet names to uppercase format with proper mapping

### 4. Error Handling and Validation
- **Problem**: Poor error handling and unclear error messages
- **Solution**: Enhanced error handling throughout the system
- **Changes**:
  - Added comprehensive input validation
  - Added specific error codes for different failure scenarios
  - Improved error messages with actionable information

### 5. API Response Structure
- **Problem**: Inconsistent API response formats
- **Solution**: Standardized response structure with proper HTTP status codes
- **Changes**:
  - Added error codes to all error responses
  - Enhanced success responses with additional metadata
  - Proper HTTP status code mapping based on error types

## New Features Added

### 1. Debug Endpoint
- **Endpoint**: `/api/rule-engine/debug-chart`
- **Purpose**: Analyze chart data structure for troubleshooting
- **Features**: 
  - Shows available charts
  - Analyzes chart structure
  - Lists planets found
  - Identifies data format issues

### 2. Enhanced Chart Data Support
- **Multiple Structure Support**: Handles both new and legacy chart data formats
- **Fallback Mechanisms**: Tries multiple approaches to extract planet positions
- **Sign-based Mapping**: Can map planets to houses using sign positions and lagna

### 3. Advanced Rule Types
- **WITH_RULING_PLANET**: Check if planet is with its ruling planet
- **RELATED_TO_RULING_PLANET**: Check planet relationships with house ruling planets
- **ASPECTING_BIRTH_RULING_PLANET**: Check if planet aspects house ruling planets

## API Endpoints Updated

### 1. `/api/rule-engine/evaluate` (POST)
- Enhanced input validation
- Better error handling
- Improved response structure
- Support for all 23 divisional charts

### 2. `/api/rule-engine/suggestions` (GET)
- Updated with new rule types
- Enhanced example queries
- Added house ruling planets mapping

### 3. `/api/rule-engine/debug-chart` (POST) - NEW
- Debug chart data structure
- Analyze planet-house mappings
- Troubleshoot data format issues

## Database Structure Support

### Supported Chart Data Formats

#### Format 1: Houses Array Structure
```json
{
  "chart_data": {
    "D1": {
      "houses": [
        {
          "house_number": 1,
          "planets": ["sun", "moon"],
          "planet_degrees": [15.5, 20.3],
          "planet_nakshatras": {"sun": "ASHWINI", "moon": "BARANI"}
        }
      ]
    }
  }
}
```

#### Format 2: Planets Precise Structure
```json
{
  "chart_data": {
    "D1": {
      "lagna": {"sign": "MESHAM"},
      "planets_precise": {
        "sun": {"sign": "SIMMAM", "degree": 15.5},
        "moon": {"sign": "MESHAM", "degree": 20.3}
      }
    }
  }
}
```

## Testing Updates

### Updated Test Cases
- Fixed function signatures to match new parameter structure
- Added tests for new chart data formats
- Enhanced test coverage for error scenarios
- Added tests for planet name normalization

### New Test Scenarios
- Multiple chart data structure support
- Planet name normalization
- Sign-to-house mapping
- Error handling validation

## Usage Examples

### Basic Rule Evaluation
```json
POST /api/rule-engine/evaluate
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Moon IN House1 OR Moon IN House3",
  "chart_type": "D1"
}
```

### Advanced Rule with Ruling Planet
```json
POST /api/rule-engine/evaluate
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "query": "Ketu IN House6 WITH Ruling_Planet",
  "chart_type": "D1"
}
```

### Debug Chart Structure
```json
POST /api/rule-engine/debug-chart
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "chart_type": "D1"
}
```

## Error Codes

- `NO_DATA_PROVIDED`: Request body is empty
- `MISSING_REQUIRED_FIELDS`: Required fields are missing
- `INVALID_CHART_TYPE`: Invalid chart type specified
- `CHART_DATA_NOT_FOUND`: Chart data not found in database
- `INVALID_CHART_STRUCTURE`: Chart data structure is invalid
- `NO_PLANET_DATA`: No planet data found in chart
- `INVALID_QUERY_FORMAT`: Query syntax is invalid
- `EVALUATION_ERROR`: Error during rule evaluation
- `VALIDATION_ERROR`: Input validation failed
- `INTERNAL_SERVER_ERROR`: Unexpected server error

## Backward Compatibility

All changes maintain backward compatibility with existing API calls while adding enhanced functionality and better error handling.
