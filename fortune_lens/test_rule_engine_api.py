#!/usr/bin/env python3
"""
API Test Script for Rule Engine
Tests the rule engine API endpoints directly
"""

import requests
import json
import time
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

# Test data
TEST_RULES = [
    # Basic rules
    {
        "name": "Basic IN Rule",
        "query": "Moon IN House4",
        "chart_type": "D1"
    },
    {
        "name": "Basic NOT IN Rule", 
        "query": "Sun NOT IN House8",
        "chart_type": "D1"
    },
    # OR Logic
    {
        "name": "OR Logic Rule",
        "query": "Moon IN House1 OR Moon IN House4 OR Moon IN House7",
        "chart_type": "D1"
    },
    # AND Logic
    {
        "name": "AND Logic Rule",
        "query": "Sun IN House1 AND Mercury IN House1",
        "chart_type": "D1"
    },
    # Mixed Logic
    {
        "name": "Mixed AND/OR Logic",
        "query": "Sun IN House1 AND Mercury IN House1 OR Jupiter IN House9",
        "chart_type": "D1"
    },
    # Advanced rules
    {
        "name": "WITH Ruling Planet",
        "query": "Ketu IN House6 WITH Ruling_Planet",
        "chart_type": "D1"
    },
    {
        "name": "IS RELATED TO Ruling Planet",
        "query": "Ketu IS RELATED TO House6_Ruling_Planet",
        "chart_type": "D1"
    },
    {
        "name": "IS ASPECTING_BIRTH Ruling Planet",
        "query": "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet",
        "chart_type": "D1"
    }
]

ERROR_TEST_CASES = [
    {
        "name": "Missing member_profile_id",
        "data": {
            "user_profile_id": TEST_USER_ID,
            "query": "Moon IN House1",
            "chart_type": "D1"
        },
        "expected_status": 400,
        "expected_error": "MISSING_REQUIRED_FIELDS"
    },
    {
        "name": "Invalid chart type",
        "data": {
            "user_profile_id": TEST_USER_ID,
            "member_profile_id": TEST_MEMBER_ID,
            "query": "Moon IN House1",
            "chart_type": "INVALID"
        },
        "expected_status": 400,
        "expected_error": "INVALID_CHART_TYPE"
    },
    {
        "name": "Invalid query format",
        "data": {
            "user_profile_id": TEST_USER_ID,
            "member_profile_id": TEST_MEMBER_ID,
            "query": "Invalid Query Format",
            "chart_type": "D1"
        },
        "expected_status": 400,
        "expected_error": "INVALID_QUERY_FORMAT"
    },
    {
        "name": "Non-existent user",
        "data": {
            "user_profile_id": "999",
            "member_profile_id": "999",
            "query": "Moon IN House1",
            "chart_type": "D1"
        },
        "expected_status": 404,
        "expected_error": "CHART_DATA_NOT_FOUND"
    }
]

def get_auth_token():
    """Try to get authentication token"""
    try:
        # Try to register a test user
        register_data = {
            "email": "<EMAIL>",
            "password": "password123",
            "name": "Rule Test User",
            "mobile": "9876543210"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/register", json=register_data)
        if response.status_code == 201:
            print("✓ Test user registered successfully")
        elif "already registered" in response.text:
            print("✓ Test user already exists")
        
        # Try to login
        login_data = {
            "email": "<EMAIL>",
            "password": "password123"
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code == 200:
            token = response.json().get("access_token")
            print("✓ Authentication successful")
            return token
        else:
            print(f"✗ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"✗ Authentication error: {str(e)}")
        return None

def test_debug_chart(token):
    """Test debug chart endpoint"""
    print("\n" + "="*60)
    print("TESTING DEBUG CHART ENDPOINT")
    print("="*60)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    data = {
        "user_profile_id": TEST_USER_ID,
        "member_profile_id": TEST_MEMBER_ID,
        "chart_type": "D1"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rule-engine/debug-chart", 
                               json=data, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✓ Debug chart successful")
            print(f"  Chart data exists: {result.get('chart_data_exists')}")
            print(f"  Available charts: {result.get('available_charts', [])}")
            print(f"  Planets found: {result.get('planets_found', [])}")
            return True
        else:
            print(f"✗ Debug chart failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Debug chart error: {str(e)}")
        return False

def test_rule_evaluation(token):
    """Test rule evaluation endpoint"""
    print("\n" + "="*60)
    print("TESTING RULE EVALUATION")
    print("="*60)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    success_count = 0
    total_count = len(TEST_RULES)
    
    for test_case in TEST_RULES:
        print(f"\nTesting: {test_case['name']}")
        print(f"Query: {test_case['query']}")
        
        data = {
            "user_profile_id": TEST_USER_ID,
            "member_profile_id": TEST_MEMBER_ID,
            "query": test_case['query'],
            "chart_type": test_case['chart_type']
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/evaluate",
                                   json=data, headers=headers)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"  ✓ Result: {result['result']}")
                    print(f"  ✓ Planet positions available: {len(result.get('planet_positions', {}))}")
                    success_count += 1
                else:
                    print(f"  ✗ Evaluation failed: {result.get('message')}")
            else:
                print(f"  ✗ HTTP Error {response.status_code}: {response.text}")
                
        except Exception as e:
            print(f"  ✗ Exception: {str(e)}")
    
    print(f"\nRule Evaluation Summary: {success_count}/{total_count} tests passed")
    return success_count == total_count

def test_error_handling(token):
    """Test error handling scenarios"""
    print("\n" + "="*60)
    print("TESTING ERROR HANDLING")
    print("="*60)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    success_count = 0
    total_count = len(ERROR_TEST_CASES)
    
    for test_case in ERROR_TEST_CASES:
        print(f"\nTesting: {test_case['name']}")
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/evaluate",
                                   json=test_case['data'], headers=headers)
            
            if response.status_code == test_case['expected_status']:
                result = response.json()
                error_code = result.get('error_code')
                
                if error_code == test_case['expected_error']:
                    print(f"  ✓ Expected error: {error_code}")
                    success_count += 1
                else:
                    print(f"  ✗ Wrong error code. Expected: {test_case['expected_error']}, Got: {error_code}")
            else:
                print(f"  ✗ Wrong status code. Expected: {test_case['expected_status']}, Got: {response.status_code}")
                
        except Exception as e:
            print(f"  ✗ Exception: {str(e)}")
    
    print(f"\nError Handling Summary: {success_count}/{total_count} tests passed")
    return success_count == total_count

def test_suggestions(token):
    """Test suggestions endpoint"""
    print("\n" + "="*60)
    print("TESTING SUGGESTIONS ENDPOINT")
    print("="*60)
    
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    try:
        response = requests.get(f"{BASE_URL}/api/rule-engine/suggestions", headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ Suggestions retrieved successfully")
                print(f"  Planets: {len(result.get('planets', []))}")
                print(f"  Operators: {len(result.get('operators', []))}")
                print(f"  Houses: {len(result.get('houses', []))}")
                print(f"  Example queries: {len(result.get('example_queries', []))}")
                return True
            else:
                print(f"✗ Suggestions failed: {result.get('message')}")
        else:
            print(f"✗ HTTP Error {response.status_code}: {response.text}")
            
    except Exception as e:
        print(f"✗ Suggestions error: {str(e)}")
    
    return False

def main():
    """Main test function"""
    print("FORTUNE LENS RULE ENGINE API TESTING")
    print("="*60)
    print(f"Test started at: {datetime.now()}")
    print(f"Base URL: {BASE_URL}")
    
    # Check if server is running
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print("✓ Server is running")
    except:
        print("✗ Server is not responding. Please start the Flask application.")
        return
    
    # Get authentication token
    print("\n" + "="*60)
    print("AUTHENTICATION")
    print("="*60)
    token = get_auth_token()
    
    if not token:
        print("⚠ Proceeding without authentication token (may cause failures)")
    
    # Run tests
    test_results = []
    
    # Test debug chart
    test_results.append(("Debug Chart", test_debug_chart(token)))
    
    # Test rule evaluation
    test_results.append(("Rule Evaluation", test_rule_evaluation(token)))
    
    # Test error handling
    test_results.append(("Error Handling", test_error_handling(token)))
    
    # Test suggestions
    test_results.append(("Suggestions", test_suggestions(token)))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} test suites passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Rule Engine API is working correctly.")
    else:
        print("⚠ Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()
