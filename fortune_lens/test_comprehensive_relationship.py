#!/usr/bin/env python3
"""
Test Comprehensive Relationship Functionality
Tests the new comprehensive relationship checking that evaluates all 4 relationship types
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

def test_comprehensive_relationship():
    """Test the comprehensive relationship functionality"""
    print("=" * 80)
    print("🔍 COMPREHENSIVE RELATIONSHIP TESTING")
    print("=" * 80)
    
    print("Your Query: 'Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet'")
    print("\nThis will check ALL 4 relationship types:")
    print("1. Basic Position: Ketu IN house6 OR Ketu IN house10")
    print("2. WITH Ruling Planet: Ketu IN House6 WITH Ruling_Planet OR Ketu IN House10 WITH Ruling_Planet")
    print("3. Nakshatra: House6_Ruling_Planet IN_STAR_OF Ketu OR Ketu IN_STAR_OF House10_Ruling_Planet")
    print("4. Aspecting: House6_Ruling_Planet IS_ASPECTING Ketu OR Ketu IS_ASPECTING House6_Ruling_Planet")
    
    # Expected results based on our chart data
    print("\n" + "=" * 60)
    print("EXPECTED RESULTS ANALYSIS")
    print("=" * 60)
    
    print("Chart Data Summary:")
    print("• House 6: KUMBAM (Aquarius) → ruled by SATURN")
    print("• House 10: MAGARAM (Capricorn) → ruled by SATURN") 
    print("• KETU: Located in House 6")
    print("• SATURN: Located in House 11, in MAGAM nakshatra")
    
    print("\nFor 6th House Relationship (Ketu IS RELATED TO 6th House_Ruling_Planet):")
    print("1. Basic Position: Ketu IN house6 → TRUE ✅ (Ketu is in House 6)")
    print("2. WITH Ruling Planet: Ketu WITH Saturn → FALSE ❌ (Ketu in House 6, Saturn in House 11)")
    print("3. Nakshatra: Saturn IN_STAR_OF Ketu → TRUE ✅ (Saturn in MAGAM, ruled by Ketu)")
    print("4. Aspecting: Saturn IS_ASPECTING Ketu → TRUE ✅ (Saturn in House 11 aspects House 6)")
    print("   OVERALL 6th House: TRUE ✅ (3 out of 4 relationships satisfied)")
    
    print("\nFor 10th House Relationship (Ketu IS RELATED TO 10th House_Ruling_Planet):")
    print("1. Basic Position: Ketu IN house10 → FALSE ❌ (Ketu is in House 6, not 10)")
    print("2. WITH Ruling Planet: Ketu WITH Saturn → FALSE ❌ (Ketu in House 6, Saturn in House 11)")
    print("3. Nakshatra: Saturn IN_STAR_OF Ketu → TRUE ✅ (Saturn in MAGAM, ruled by Ketu)")
    print("4. Aspecting: Saturn IS_ASPECTING Ketu → TRUE ✅ (Saturn in House 11 aspects House 6)")
    print("   OVERALL 10th House: TRUE ✅ (2 out of 4 relationships satisfied)")
    
    print("\nFINAL RESULT: TRUE OR TRUE = TRUE ✅")

def show_api_usage():
    """Show how to use the comprehensive relationship API"""
    print("\n" + "=" * 80)
    print("📡 API USAGE INSTRUCTIONS")
    print("=" * 80)
    
    print("🔧 New Endpoint: /api/rule-engine/comprehensive-relationship")
    print("Method: POST")
    print("Authentication: Bearer token required")
    
    print("\n📝 Request Body:")
    request_body = {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet",
        "chart_type": "D1"
    }
    print(json.dumps(request_body, indent=2))
    
    print("\n🌐 Curl Command:")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/comprehensive-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print(f"  -d '{json.dumps(request_body)}'")
    
    print("\n📊 Expected Response Structure:")
    expected_response = {
        "success": True,
        "query": "Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet",
        "chart_type": "D1",
        "overall_result": True,
        "relationships": {
            "6th_house": {
                "house_ruling_planet": "SATURN",
                "overall_result": True,
                "relationships": {
                    "basic_position": True,
                    "with_ruling_planet": False,
                    "nakshatra": True,
                    "aspecting": True
                },
                "details": {
                    "basic_position": "KETU is in House 6",
                    "with_ruling_planet": "KETU in House 6, SATURN in House 11 - not together",
                    "nakshatra": "SATURN is in MAGAM nakshatra (ruled by KETU)",
                    "aspecting": "SATURN in House 11 aspects House 6 (where KETU is)"
                }
            },
            "10th_house": {
                "house_ruling_planet": "SATURN",
                "overall_result": True,
                "relationships": {
                    "basic_position": False,
                    "with_ruling_planet": False,
                    "nakshatra": True,
                    "aspecting": True
                },
                "details": {
                    "basic_position": "KETU is in House 6, not in House 10 or SATURN's house",
                    "with_ruling_planet": "KETU in House 6, SATURN in House 11 - not together",
                    "nakshatra": "SATURN is in MAGAM nakshatra (ruled by KETU)",
                    "aspecting": "SATURN in House 11 aspects House 6 (where KETU is)"
                }
            }
        },
        "user_profile_id": "1",
        "member_profile_id": "1"
    }
    print(json.dumps(expected_response, indent=2))

def show_individual_queries():
    """Show the individual queries that are checked"""
    print("\n" + "=" * 80)
    print("🔍 INDIVIDUAL QUERIES BREAKDOWN")
    print("=" * 80)
    
    individual_queries = [
        {
            "category": "1. Basic Position",
            "queries": [
                "Ketu IN house6",
                "Ketu IN house10"
            ],
            "expected": [True, False]
        },
        {
            "category": "2. WITH Ruling Planet",
            "queries": [
                "Ketu IN house6 WITH Ruling_Planet_house6",
                "Ketu IN house10 WITH Ruling_Planet_house10"
            ],
            "expected": [False, False]
        },
        {
            "category": "3. Nakshatra (Star)",
            "queries": [
                "House6_Ruling_Planet IN_STAR_OF Ketu",
                "House10_Ruling_Planet IN_STAR_OF Ketu"
            ],
            "expected": [True, True]
        },
        {
            "category": "4. Aspecting",
            "queries": [
                "House6_Ruling_Planet IS_ASPECTING Ketu",
                "House10_Ruling_Planet IS_ASPECTING Ketu"
            ],
            "expected": [True, True]
        }
    ]
    
    print("The comprehensive relationship check evaluates these individual queries:")
    
    for category_data in individual_queries:
        print(f"\n{category_data['category']}:")
        for i, (query, expected) in enumerate(zip(category_data['queries'], category_data['expected'])):
            status = "✅ TRUE" if expected else "❌ FALSE"
            print(f"  • {query} → {status}")

def show_postman_tests():
    """Show Postman test cases for comprehensive relationship"""
    print("\n" + "=" * 80)
    print("📋 POSTMAN TEST CASES")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "Test Your Main Query",
            "query": "Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet",
            "expected_overall": True,
            "expected_6th": True,
            "expected_10th": True
        },
        {
            "name": "Test Single House",
            "query": "Ketu IS RELATED TO 6th House_Ruling_Planet",
            "expected_overall": True,
            "expected_6th": True,
            "expected_10th": None
        },
        {
            "name": "Test Different Planet",
            "query": "Jupiter IS RELATED TO 9th House_Ruling_Planet",
            "expected_overall": True,
            "expected_9th": True,
            "expected_other": None
        },
        {
            "name": "Test False Case",
            "query": "Mars IS RELATED TO 1st House_Ruling_Planet",
            "expected_overall": False,
            "expected_1st": False,
            "expected_other": None
        }
    ]
    
    print("Recommended Postman Test Cases:")
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Query: {test['query']}")
        print(f"   Expected Overall: {'TRUE ✅' if test['expected_overall'] else 'FALSE ❌'}")
        
        # Show test script
        print(f"   Test Script:")
        print(f"   pm.test('{test['name']}', function () {{")
        print(f"       pm.response.to.have.status(200);")
        print(f"       const response = pm.response.json();")
        print(f"       pm.expect(response.success).to.be.true;")
        print(f"       pm.expect(response.overall_result).to.be.{'true' if test['expected_overall'] else 'false'};")
        print(f"   }});")

def main():
    """Main testing function"""
    print("COMPREHENSIVE RELATIONSHIP TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing the new comprehensive relationship functionality")
    
    # Run all demonstrations
    test_comprehensive_relationship()
    show_api_usage()
    show_individual_queries()
    show_postman_tests()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ New comprehensive relationship endpoint implemented")
    print("✅ Checks all 4 relationship types automatically")
    print("✅ Returns detailed results for each relationship")
    print("✅ Supports OR logic for multiple houses")
    print("✅ Your specific query format supported")
    
    print("\n🎯 YOUR QUERY SUPPORT:")
    print("✅ 'Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet'")
    print("✅ Returns all 4 relationship results for each house")
    print("✅ Overall result: TRUE (multiple relationships satisfied)")
    
    print("\n📋 NEXT STEPS:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test new endpoint: /api/rule-engine/comprehensive-relationship")
    print("4. Use your exact query format")
    print("5. Review detailed relationship breakdown")
    print("6. Add to Postman collection for regular testing")

if __name__ == "__main__":
    main()
