#!/usr/bin/env python3
"""
Test Working API
Verify all functionality is working correctly
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"

def test_api_endpoint(query, expected_result, description):
    """Test a single API endpoint"""
    print(f"\n🧪 Testing: {description}")
    print(f"Query: {query}")
    print(f"Expected: {expected_result}")
    
    data = {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": query,
        "chart_type": "D1"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship", 
                               json=data, headers={"Content-Type": "application/json"})
        
        if response.status_code == 200:
            result = response.json()
            actual_result = result.get('overall_result')
            
            if actual_result == expected_result:
                print(f"✅ PASS: Result = {actual_result}")
                print(f"   House 1 planets: {result.get('house1_planets')}")
                print(f"   House 2 planets: {result.get('house2_planets')}")
                print(f"   Relationships: {len(result.get('planet_relationships', {}))}")
                return True
            else:
                print(f"❌ FAIL: Expected {expected_result}, got {actual_result}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main testing function"""
    print("🚀 TESTING WORKING API")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    
    # Test cases
    test_cases = [
        {
            "query": "6th_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": False,
            "description": "🎯 YOUR ORIGINAL QUERY (House 6 empty)"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 1st_House_Planet",
            "expected": True,
            "description": "✅ Same House Together: LAGNAM & RAHU"
        },
        {
            "query": "10th_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": True,
            "description": "✅ Same House Together: JUPITER & VENUS"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "expected": True,
            "description": "🔥 Cross-House: 1st ↔ 10th (8 relationships)"
        },
        {
            "query": "9th_House_Planet IS RELATED TO 11th_House_Planet",
            "expected": True,
            "description": "✅ Cross-House: SUN ↔ MARS"
        },
        {
            "query": "5th_House_Planet IS RELATED TO 7th_House_Planet",
            "expected": True,
            "description": "✅ Cross-House: MOON ↔ KETU"
        },
        {
            "query": "2nd_House_Planet IS RELATED TO 3rd_House_Planet",
            "expected": False,
            "description": "❌ Both Houses Empty"
        },
        {
            "query": "4th_House_Planet IS RELATED TO 5th_House_Planet",
            "expected": False,
            "description": "❌ One House Empty (House 4)"
        }
    ]
    
    # Run tests
    passed = 0
    total = len(test_cases)
    
    for test_case in test_cases:
        if test_api_endpoint(test_case["query"], test_case["expected"], test_case["description"]):
            passed += 1
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! API IS WORKING PERFECTLY!")
        print("\n✅ VERIFIED FUNCTIONALITY:")
        print("   ✅ Your original query returns FALSE correctly")
        print("   ✅ Same house together relationships work")
        print("   ✅ Cross-house relationships work")
        print("   ✅ Empty house handling is correct")
        print("   ✅ All relationship types are checked")
        print("   ✅ Bidirectional analysis works")
        print("   ✅ Enhanced together types implemented")
        
        print("\n🎯 YOUR QUERY RESULT:")
        print("'6th_House_Planet IS RELATED TO 10th_House_Planet' = FALSE ✅")
        print("Reason: House 6 is empty (no planets to relate)")
        
        print("\n📋 NEXT STEPS:")
        print("1. Import 'Working_API_Postman_No_Auth.json' into Postman")
        print("2. Run the Postman collection to verify all tests")
        print("3. Your API is ready for production use!")
        
    else:
        print(f"\n❌ {total-passed} TESTS FAILED")
        print("Check the failed tests above for details")

if __name__ == "__main__":
    main()
