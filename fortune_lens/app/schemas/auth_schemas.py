"""
Authentication Schemas
"""

import re


def validate_login_input(data):
    """
    Validate login input

    Args:
        data (dict): Input data

    Returns:
        dict: Validation errors, empty if no errors
    """
    errors = {}

    # Check required fields
    if 'email' not in data or not data['email']:
        errors['email'] = 'Email is required'

    if 'password' not in data or not data['password']:
        errors['password'] = 'Password is required'

    # Validate email format
    if 'email' in data and data['email']:
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, data['email']):
            errors['email'] = 'Invalid email format'

    return errors


def validate_register_input(data):
    """
    Validate register input

    Args:
        data (dict): Input data

    Returns:
        dict: Validation errors, empty if no errors
    """
    errors = {}

    # Check required fields
    if 'email' not in data or not data['email']:
        errors['email'] = 'Email is required'

    if 'password' not in data or not data['password']:
        errors['password'] = 'Password is required'

    if 'name' not in data or not data['name']:
        errors['name'] = 'Name is required'

    if 'mobile' not in data or not data['mobile']:
        errors['mobile'] = 'Mobile number is required'

    # Validate email format
    if 'email' in data and data['email']:
        email_regex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_regex, data['email']):
            errors['email'] = 'Invalid email format'

    # Validate password strength
    if 'password' in data and data['password']:
        if len(data['password']) < 8:
            errors['password'] = 'Password must be at least 8 characters long'

    # Validate mobile number format if provided
    if 'mobile' in data and data['mobile']:
        mobile_regex = r'^[0-9]{10}$'
        if not re.match(mobile_regex, data['mobile']):
            errors['mobile'] = 'Invalid mobile number format. Must be exactly 10 digits'

    # Validate OTP if provided
    if 'otp' in data and data['otp']:
        if not data['otp'].strip():
            errors['otp'] = 'OTP cannot be empty'
        elif not data['otp'].isdigit() or len(data['otp']) != 6:
            errors['otp'] = 'OTP must be 6 digits'

    return errors
