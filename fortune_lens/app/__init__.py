"""
Fortune Lens Flask Application

This module initializes the Flask application, configures it with the appropriate
settings, registers blueprints, initializes extensions, and sets up error handlers.
"""

import os
from flask import Flask
from flask_cors import CORS

from .extensions import mongo, jwt, bcrypt
from .config import config_by_name
from .errors import register_error_handlers


def create_app(config_name="development"):
    """
    Create and configure the Flask application.

    This function creates a new Flask application instance, loads the appropriate
    configuration, initializes extensions, registers error handlers and blueprints,
    and sets up CORS.

    Args:
        config_name (str): Configuration environment name ('development', 'testing', or 'production')

    Returns:
        Flask: Configured Flask application instance
    """
    app = Flask(__name__)

    # Load configuration based on environment
    app.config.from_object(config_by_name[config_name])

    # Initialize extensions (MongoDB, JWT, Bcrypt)
    initialize_extensions(app)

    # Register error handlers for consistent error responses
    register_error_handlers(app)

    # Register API blueprints
    register_blueprints(app)

    # Enable CORS for API routes
    CORS(app, resources={r"/api/*": {"origins": "*"}})

    # Shell context for Flask CLI
    @app.shell_context_processor
    def shell_context():
        """
        Add objects to the Flask shell context.
        """
        return {
            "app": app,
            "mongo": mongo
        }

    return app


def initialize_extensions(app):
    """
    Initialize Flask extensions.

    Args:
        app (Flask): The Flask application instance
    """
    mongo.init_app(app)
    jwt.init_app(app)
    bcrypt.init_app(app)


def register_blueprints(app):
    """
    Register Flask blueprints for API routes.

    Args:
        app (Flask): The Flask application instance
    """
    from .api import api_bp

    app.register_blueprint(api_bp, url_prefix='/api')
