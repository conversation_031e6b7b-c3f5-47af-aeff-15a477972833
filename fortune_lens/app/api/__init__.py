"""
API Blueprint Package

This module defines the main API blueprint and registers all route modules.
It serves as the central point for organizing all API endpoints.
"""

from flask import Blueprint

# Create main API blueprint
api_bp = Blueprint('api', __name__)

# Import routes directly from existing files
from . import user_profile
from . import member_profiles
from . import charts
from . import marriage_matching
from . import marriage_date_prediction
from . import rule_engine

# Import and register daily_panchanga blueprint
from .daily_panchanga import daily_panchanga_bp
api_bp.register_blueprint(daily_panchanga_bp, url_prefix='/daily-panchanga')

# Import and register career_prediction blueprint
from .career_prediction import career_prediction_bp
api_bp.register_blueprint(career_prediction_bp)

# Note: Other routes are registered directly with the api_bp in their respective modules
