"""
Marriage Matching API

This module provides API endpoints for Lagna-based marriage compatibility analysis.
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import json
import numpy as np
from bson import ObjectId

from . import api_bp
from ..services.marriage_matching import analyze_lagna_marriage_compatibility, analyze_rasi_marriage_compatibility
from ..services.marriage_matching.lagna_matching import <PERSON><PERSON>yEncoder
from ..errors.exceptions import ValidationError, ResourceNotFoundError
from ..extensions import mongo
from ..config import BaseConfig


@api_bp.route('/marriage-matching/lagna', methods=['POST'])
@jwt_required()
def analyze_lagna_marriage_compatibility_api():
    """
    Analyze marriage compatibility between two members using Lagna (Ascendant) positions
    ---
    tags:
      - Marriage Matching
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - bride_id
            - groom_id
          properties:
            bride_id:
              type: string
              description: ID of the bride's member profile (can be ObjectId or member_profile_id)
            groom_id:
              type: string
              description: ID of the groom's member profile (can be ObjectId or member_profile_id)
    responses:
      200:
        description: Lagna-based compatibility analysis results
      400:
        description: Validation error
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    data = request.get_json() or {}

    # Validate input
    if 'bride_id' not in data:
        raise ValidationError({'bride_id': 'Bride ID is required'})
    if 'groom_id' not in data:
        raise ValidationError({'groom_id': 'Groom ID is required'})

    # Analyze compatibility using Lagna matching
    result = analyze_lagna_marriage_compatibility(
        data['bride_id'],
        data['groom_id']
    )

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return json.dumps(result, cls=NumpyEncoder), 200, {'Content-Type': 'application/json'}


@api_bp.route('/marriage-matching/lagna/<bride_id>/<groom_id>', methods=['GET'])
@jwt_required()
def get_lagna_marriage_compatibility(bride_id, groom_id):
    """
    Get Lagna-based marriage compatibility analysis between two members
    ---
    tags:
      - Marriage Matching
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: bride_id
        required: true
        schema:
          type: string
        description: ID of the bride's member profile (can be ObjectId or member_profile_id)
      - in: path
        name: groom_id
        required: true
        schema:
          type: string
        description: ID of the groom's member profile (can be ObjectId or member_profile_id)
    responses:
      200:
        description: Lagna-based compatibility analysis results
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    # Analyze compatibility using Lagna matching
    result = analyze_lagna_marriage_compatibility(bride_id, groom_id)

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return json.dumps(result, cls=NumpyEncoder), 200, {'Content-Type': 'application/json'}


@api_bp.route('/marriage-matching/rasi', methods=['POST'])
@jwt_required()
def analyze_rasi_marriage_compatibility_api():
    """
    Analyze marriage compatibility between two members using Rasi (Moon Sign) positions
    ---
    tags:
      - Marriage Matching
    security:
      - bearerAuth: []
    parameters:
      - in: body
        name: body
        schema:
          type: object
          required:
            - bride_id
            - groom_id
          properties:
            bride_id:
              type: string
              description: ID of the bride's member profile (can be ObjectId or member_profile_id)
            groom_id:
              type: string
              description: ID of the groom's member profile (can be ObjectId or member_profile_id)
            marriage_date:
              type: string
              description: Proposed marriage date (optional, format YYYY-MM-DD)
    responses:
      200:
        description: Rasi-based compatibility analysis results
      400:
        description: Validation error
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    data = request.get_json() or {}

    # Validate input
    if 'bride_id' not in data:
        raise ValidationError({'bride_id': 'Bride ID is required'})
    if 'groom_id' not in data:
        raise ValidationError({'groom_id': 'Groom ID is required'})

    # Extract marriage date if provided
    marriage_date = data.get('marriage_date')

    # Analyze compatibility using Rasi matching
    result = analyze_rasi_marriage_compatibility(
        data['bride_id'],
        data['groom_id'],
        marriage_date
    )

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return json.dumps(result, cls=NumpyEncoder), 200, {'Content-Type': 'application/json'}


@api_bp.route('/marriage-matching/rasi/<bride_id>/<groom_id>', methods=['GET'])
@jwt_required()
def get_rasi_marriage_compatibility(bride_id, groom_id):
    """
    Get Rasi-based marriage compatibility analysis between two members
    ---
    tags:
      - Marriage Matching
    security:
      - bearerAuth: []
    parameters:
      - in: path
        name: bride_id
        required: true
        schema:
          type: string
        description: ID of the bride's member profile (can be ObjectId or member_profile_id)
      - in: path
        name: groom_id
        required: true
        schema:
          type: string
        description: ID of the groom's member profile (can be ObjectId or member_profile_id)
    responses:
      200:
        description: Rasi-based compatibility analysis results
      401:
        description: Unauthorized
      404:
        description: Member profile not found
    """
    # Analyze compatibility using Rasi matching
    result = analyze_rasi_marriage_compatibility(bride_id, groom_id)

    if not result['success']:
        raise ResourceNotFoundError(result['message'])

    return json.dumps(result, cls=NumpyEncoder), 200, {'Content-Type': 'application/json'}
