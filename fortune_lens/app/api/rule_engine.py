"""
Rule Engine API

This module provides API endpoints for evaluating complex astrological rules and conditions.
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

from . import api_bp
from ..services.rule_engine import evaluate_rule, debug_chart_structure, get_chart_data
from ..services.rule_engine import check_comprehensive_relationship, check_house_ruling_planet_relationship, check_house_planet_relationship, check_planet_to_house_planet_relationship, get_success_rating
from ..errors.exceptions import ValidationError


@api_bp.route('/rule-engine/', methods=['GET', 'POST'])
# @jwt_required()  # Temporarily disabled for testing
def unified_rule_engine_api():
    """
    🚀 UNIFIED RULE ENGINE API - HANDLES ALL QUERY FORMATS

    This single endpoint intelligently detects and processes all supported query formats:

    1. 🔧 Basic Rule Evaluation:
       - Format: "Moon IN House5" or "Moon IN House1 OR Moon IN House3"
       - Example: "Ketu IN House7 WITH Ruling_Planet"

    2. 🏠 House to House Planet Relationships:
       - Format: "#th_House_Planet IS RELATED TO #th_House_Planet"
       - Example: "1st_House_Planet IS RELATED TO 10th_House_Planet"

    3. 🌟 Planet to House Planet Relationships:
       - Format: "Planet IS RELATED TO #th_House_Planet"
       - Example: "Ketu IS RELATED TO 10th_House_Planet"

    4. 👑 Planet to House Ruling Planet:
       - Format: "Planet IS RELATED TO #th House_Ruling_Planet"
       - Example: "Ketu IS RELATED TO 10th House_Ruling_Planet"

    5. 👑 House Ruling Planet Relationships:
       - Format: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
       - Format: "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
       - Example: "6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet"

    6. 🔍 Utility Functions:
       - GET request returns suggestions
       - Special queries for debugging

    All formats use the same 5-rule logic with ruling planet analysis.
    """
    try:
        # Handle GET request for suggestions
        if request.method == 'GET':
            try:
                suggestions = {
                    "success": True,
                    "message": "Rule engine suggestions",
                    "supported_query_formats": {
                        "1_basic_rule_evaluation": {
                            "format": "Planet IN House# [WITH Ruling_Planet] [OR/AND conditions]",
                            "examples": [
                                "Moon IN House5",
                                "Moon IN House1 OR Moon IN House3",
                                "Ketu IN House7 WITH Ruling_Planet",
                                "Mars IN House11 AND Jupiter IN House10"
                            ]
                        },
                        "2_house_to_house_relationships": {
                            "format": "#th_House_Planet IS RELATED TO #th_House_Planet",
                            "examples": [
                                "1st_House_Planet IS RELATED TO 10th_House_Planet",
                                "6th_House_Planet IS RELATED TO 10th_House_Planet"
                            ]
                        },
                        "3_planet_to_house_planets": {
                            "format": "Planet IS RELATED TO #th_House_Planet",
                            "examples": [
                                "Ketu IS RELATED TO 10th_House_Planet",
                                "Mars IS RELATED TO 1st_House_Planet"
                            ]
                        },
                        "4_planet_to_house_ruling_planet": {
                            "format": "Planet IS RELATED TO #th House_Ruling_Planet",
                            "examples": [
                                "Ketu IS RELATED TO 10th House_Ruling_Planet",
                                "Mars IS RELATED TO 1st House_Ruling_Planet"
                            ]
                        },
                        "5_house_ruling_planet_relationships": {
                            "format": "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet",
                            "examples": [
                                "6th_House_Ruling_Planet IS RELATED TO 10th_House_Ruling_Planet",
                                "House1_Ruling_Planet IS RELATED TO House10_Ruling_Planet"
                            ]
                        }
                    },
                    "relationship_types": [
                        "basic_position",
                        "with_ruling_planet",
                        "together",
                        "nakshatra",
                        "aspecting"
                    ],
                    "planets": ["Sun", "Moon", "Mars", "Mercury", "Jupiter", "Venus", "Saturn", "Rahu", "Ketu", "Lagnam"],
                    "houses": list(range(1, 13)),
                    "operators": ["IN", "WITH", "IS RELATED TO", "OR", "AND", "NOT"]
                }
                return jsonify(suggestions)
            except Exception as e:
                return jsonify({
                    'success': False,
                    'message': f'Error getting suggestions: {str(e)}'
                }), 500

        # Get request data
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': 'No JSON data provided',
                'error_code': 'NO_DATA'
            }), 400

        # Extract common parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query', '').strip()
        chart_type = data.get('chart_type', 'D1')

        # Validate required parameters
        if not user_profile_id or not member_profile_id:
            return jsonify({
                'success': False,
                'message': 'user_profile_id and member_profile_id are required',
                'error_code': 'MISSING_REQUIRED_FIELDS'
            }), 400

        if not query:
            return jsonify({
                'success': False,
                'message': 'query is required',
                'error_code': 'MISSING_QUERY'
            }), 400

        # 🔍 INTELLIGENT QUERY FORMAT DETECTION
        import re

        # Special debug query
        if query.lower() == 'debug_chart':
            return debug_chart_api()

        # Check for logical operators (OR, AND, NOT) - route to comprehensive relationship
        logical_operators = ['OR', 'AND', 'NOT']
        has_logical_operators = any(op in query.upper() for op in logical_operators)

        if has_logical_operators:
            return process_comprehensive_relationship(data)

        # 1. 🏠 House to House Planet Relationships
        # Format: "#th_House_Planet IS RELATED TO #th_House_Planet"
        house_to_house_pattern = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
        if re.search(house_to_house_pattern, query, re.IGNORECASE):
            return process_house_planet_relationship(data)

        # 2. 🌟 Planet to House Planet Relationships
        # Format: "Planet IS RELATED TO #th_House_Planet"
        planet_to_house_pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
        if re.search(planet_to_house_pattern, query, re.IGNORECASE):
            return process_comprehensive_relationship(data)

        # 3. 👑 Planet to House Ruling Planet
        # Format: "Planet IS RELATED TO #th House_Ruling_Planet"
        planet_to_ruling_pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House_Ruling_Planet'
        if re.search(planet_to_ruling_pattern, query, re.IGNORECASE):
            return process_comprehensive_relationship(data)

        # 4. 👑 House Ruling Planet Relationships
        # Format: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
        # Format: "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
        house_ruling_pattern1 = r'House(\d+)_Ruling_Planet\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
        house_ruling_pattern2 = r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
        if re.search(house_ruling_pattern1, query, re.IGNORECASE) or re.search(house_ruling_pattern2, query, re.IGNORECASE):
            return process_house_ruling_planet_relationship(data)

        # 5. 🔧 Basic Rule Evaluation (default)
        # Format: "Moon IN House5" or "Moon IN House1 OR Moon IN House3"
        # This handles all other query formats
        return process_basic_rule_evaluation(data)

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


# Helper functions for processing different query types
def process_basic_rule_evaluation(data):
    """Process basic rule evaluation queries"""
    try:
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1')

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': 'Chart data not found',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Evaluate the rule
        result = evaluate_rule(chart_data, query, chart_type)

        return jsonify({
            'success': True,
            'query': query,
            'result': result,
            'chart_type': chart_type,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'query_type': 'basic_rule_evaluation'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error in basic rule evaluation: {str(e)}',
            'error_code': 'BASIC_RULE_ERROR'
        }), 500


def process_house_planet_relationship(data):
    """Process house to house planet relationship queries"""
    try:
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1')

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': 'Chart data not found',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse the query to extract house numbers
        import re
        pattern = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
        match = re.match(pattern, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "#th_House_Planet IS RELATED TO #th_House_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1_num, house2_num = match.groups()
        house1_num = int(house1_num)
        house2_num = int(house2_num)

        # Check house planet relationship
        result = check_house_planet_relationship(chart_data, house1_num, house2_num, chart_type)

        return jsonify({
            'success': True,
            'query': query,
            'house1_number': house1_num,
            'house2_number': house2_num,
            'chart_type': chart_type,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'query_type': 'house_planet_relationship',
            **result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error in house planet relationship: {str(e)}',
            'error_code': 'HOUSE_PLANET_ERROR'
        }), 500


def process_comprehensive_relationship(data):
    """Process planet to house planet/ruling planet relationship queries with logical operators"""
    try:
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1')

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': 'Chart data not found',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse comprehensive relationship query with logical operators
        result = parse_and_evaluate_logical_query(chart_data, query, chart_type)

        if result is None:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Supported formats: "Planet IS RELATED TO #th_House_Planet", "Planet IS RELATED TO #th House_Ruling_Planet", "#th_House_Planet IS RELATED TO #th_House_Planet" with OR, AND, NOT operators',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'query_type': 'comprehensive_relationship_with_logic',
            **result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error in comprehensive relationship: {str(e)}',
            'error_code': 'COMPREHENSIVE_ERROR'
        }), 500


def parse_and_evaluate_logical_query(chart_data, query, chart_type):
    """Parse and evaluate queries with logical operators (OR, AND, NOT)"""
    import re

    # Remove extra spaces and normalize
    query = re.sub(r'\s+', ' ', query.strip())

    # Handle NOT at the beginning
    starts_with_not = query.upper().startswith('NOT ')
    if starts_with_not:
        # Remove NOT from beginning and process the rest
        remaining_query = query[4:].strip()
        result = parse_and_evaluate_logical_query(chart_data, remaining_query, chart_type)
        if result is None:
            return None

        # Apply NOT to the result
        if 'overall_result' in result:
            result['overall_result'] = not result['overall_result']
            result['logical_evaluation'] = {
                'total_sub_queries': 1,
                'sub_queries_true': 0 if result['overall_result'] else 1,
                'sub_queries_false': 1 if result['overall_result'] else 0,
                'operators_used': ['NOT'],
                'final_logical_result': result['overall_result'],
                'not_applied': True
            }
        return result

    # Split by logical operators while preserving them
    # This regex splits on OR, AND, NOT but keeps the operators
    parts = re.split(r'\s+(OR|AND|NOT)\s+', query, flags=re.IGNORECASE)

    if len(parts) == 1:
        # Single query without logical operators
        return evaluate_single_relationship_query(chart_data, query, chart_type)

    # Process logical expression
    results = []
    operators = []

    i = 0
    while i < len(parts):
        part = parts[i].strip()

        if part.upper() in ['OR', 'AND', 'NOT']:
            operators.append(part.upper())
        elif part:  # Non-empty relationship query
            # Handle NOT prefix for this part
            is_negated = False
            if len(operators) > 0 and operators[-1] == 'NOT':
                is_negated = True
                # Remove the NOT operator as we'll handle it here
                operators.pop()

            result = evaluate_single_relationship_query(chart_data, part, chart_type)
            if result is None:
                return None  # Invalid query format

            # Apply NOT if needed
            if is_negated:
                result['overall_result'] = not result['overall_result']
                result['negated'] = True

            results.append(result)

        i += 1

    # Evaluate logical expression
    return evaluate_logical_expression(results, operators, query)


def evaluate_single_relationship_query(chart_data, query, chart_type):
    """Evaluate a single relationship query"""
    import re

    # Pattern 1: "Planet IS RELATED TO #th_House_Planet"
    pattern1 = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
    match = re.match(pattern1, query, re.IGNORECASE)

    if match:
        planet, house_num = match.groups()
        house_num = int(house_num)

        result = check_planet_to_house_planet_relationship(
            chart_data, planet.upper(), house_num, chart_type
        )

        return {
            'query_part': query,
            'query_type': 'planet_to_house_planet',
            'planet': planet.upper(),
            'target_house': house_num,
            'result': result,
            'overall_result': result["overall_result"]
        }

    # Pattern 2: "Planet IS RELATED TO #th House_Ruling_Planet"
    pattern2 = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House_Ruling_Planet'
    match = re.match(pattern2, query, re.IGNORECASE)

    if match:
        planet, house_num = match.groups()
        house_num = int(house_num)

        result = check_comprehensive_relationship(
            chart_data, planet.upper(), house_num, chart_type
        )

        return {
            'query_part': query,
            'query_type': 'planet_to_house_ruling_planet',
            'planet': planet.upper(),
            'target_house': house_num,
            'result': result,
            'overall_result': result["overall_result"]
        }

    # Pattern 3: "#th_House_Planet IS RELATED TO #th_House_Planet"
    pattern3 = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
    match = re.match(pattern3, query, re.IGNORECASE)

    if match:
        house1_num, house2_num = match.groups()
        house1_num = int(house1_num)
        house2_num = int(house2_num)

        result = check_house_planet_relationship(
            chart_data, house1_num, house2_num, chart_type
        )

        return {
            'query_part': query,
            'query_type': 'house_to_house_planet',
            'house1': house1_num,
            'house2': house2_num,
            'result': result,
            'overall_result': result["overall_result"]
        }

    # Pattern 4: "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
    pattern4 = r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
    match = re.match(pattern4, query, re.IGNORECASE)

    if match:
        house1_num, house2_num = match.groups()
        house1_num = int(house1_num)
        house2_num = int(house2_num)

        result = check_house_ruling_planet_relationship(
            chart_data, house1_num, house2_num, chart_type
        )

        return {
            'query_part': query,
            'query_type': 'house_ruling_planet_to_house_ruling_planet',
            'house1': house1_num,
            'house2': house2_num,
            'result': result,
            'overall_result': result["overall_result"]
        }

    return None  # Invalid query format


def evaluate_logical_expression(results, operators, original_query):
    """Evaluate logical expression with results and operators"""
    if not results:
        return None

    # Handle case where we have fewer results than expected due to NOT processing
    if len(results) == 1 and len(operators) == 1 and operators[0] == 'NOT':
        # Simple NOT case
        final_result = not results[0]['overall_result']
    else:
        # Start with first result
        final_result = results[0]['overall_result']

        # Apply operators sequentially
        result_index = 1
        for i, operator in enumerate(operators):
            if operator == 'NOT':
                # NOT applies to the next result
                if result_index < len(results):
                    next_result = not results[result_index]['overall_result']
                    # Apply the previous operator if any
                    if i > 0:
                        prev_operator = operators[i-1] if i > 0 else None
                        if prev_operator == 'OR':
                            final_result = final_result or next_result
                        elif prev_operator == 'AND':
                            final_result = final_result and next_result
                    else:
                        final_result = next_result
                    result_index += 1
            else:
                # Regular operators (OR, AND)
                if result_index < len(results):
                    next_result = results[result_index]['overall_result']

                    if operator == 'OR':
                        final_result = final_result or next_result
                    elif operator == 'AND':
                        final_result = final_result and next_result

                    result_index += 1

    # Calculate summary statistics
    total_queries = len(results)
    true_count = sum(1 for r in results if r['overall_result'])
    false_count = total_queries - true_count

    # Collect all relationship marks
    total_marks = 0
    max_marks = 0

    for result in results:
        if 'result' in result and 'scoring' in result['result']:
            total_marks += result['result']['scoring']['total_marks_earned']
            max_marks += result['result']['scoring']['total_marks_possible']

    success_rate = (total_marks / max_marks * 100) if max_marks > 0 else 0

    return {
        'overall_result': final_result,
        'logical_evaluation': {
            'total_sub_queries': total_queries,
            'sub_queries_true': true_count,
            'sub_queries_false': false_count,
            'operators_used': operators,
            'final_logical_result': final_result
        },
        'individual_results': results,
        'combined_scoring': {
            'total_marks_earned': total_marks,
            'total_marks_possible': max_marks,
            'success_percentage': round(success_rate, 1),
            'rating': get_success_rating(success_rate)
        },
        'query_breakdown': {
            'original_query': original_query,
            'parsed_parts': [r['query_part'] for r in results],
            'logical_operators': operators
        }
    }


def process_house_ruling_planet_relationship(data):
    """Process house ruling planet to house ruling planet relationship queries"""
    try:
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1')

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': 'Chart data not found',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse house ruling planet relationship query
        import re

        # Try format 1: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
        pattern1 = r'House(\d+)_Ruling_Planet\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
        match = re.match(pattern1, query, re.IGNORECASE)

        # Try format 2: "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
        if not match:
            pattern2 = r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
            match = re.match(pattern2, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet" or "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1_num, house2_num = match.groups()
        house1_num = int(house1_num)
        house2_num = int(house2_num)

        # Check house ruling planet relationship
        result = check_house_ruling_planet_relationship(
            chart_data, house1_num, house2_num, chart_type
        )

        return jsonify({
            'success': True,
            'query': query,
            'house1_number': house1_num,
            'house2_number': house2_num,
            'chart_type': chart_type,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'query_type': 'house_ruling_planet_relationship',
            **result
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error in house ruling planet relationship: {str(e)}',
            'error_code': 'HOUSE_RULING_ERROR'
        }), 500


@api_bp.route('/rule-engine/evaluate', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_rule_api():
    """
    Evaluate a complex astrological rule.

    This endpoint accepts a rule query string and evaluates it against the chart data
    for a specific user and member profile.

    Request JSON:
    {
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "query": "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON (Success):
    {
        "success": true,
        "query": "original query string",
        "chart_type": "D1",
        "result": true/false,
        "planet_positions": {
            "SUN": 2,
            "MOON": 4,
            ...
        },
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id"
    }

    Response JSON (Error):
    {
        "success": false,
        "message": "Error description",
        "error_code": "ERROR_CODE",
        "missing_fields": ["field1", "field2"] (if applicable),
        "valid_chart_types": ["D1", "D2", ...] (if applicable)
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Validate chart_type
        valid_chart_types = ['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8', 'D9', 'D10', 'D11', 'D12',
                           'D16', 'D20', 'D24', 'D27', 'D30', 'D40', 'D45', 'D60', 'D81', 'D108', 'D144']
        if chart_type not in valid_chart_types:
            return jsonify({
                'success': False,
                'message': f'Invalid chart_type: {chart_type}. Must be one of: {", ".join(valid_chart_types)}',
                'error_code': 'INVALID_CHART_TYPE',
                'valid_chart_types': valid_chart_types
            }), 400

        # Evaluate rule
        result = evaluate_rule(query, user_profile_id, member_profile_id, chart_type)

        # Determine appropriate HTTP status code based on result
        if result.get('success', False):
            status_code = 200
        else:
            error_code = result.get('error_code', 'UNKNOWN_ERROR')
            if error_code in ['CHART_DATA_NOT_FOUND', 'NO_PLANET_DATA']:
                status_code = 404
            elif error_code in ['INVALID_QUERY_FORMAT', 'INVALID_CHART_STRUCTURE']:
                status_code = 400
            else:
                status_code = 500

        return jsonify(result), status_code

    except ValidationError as e:
        return jsonify({
            'success': False,
            'message': str(e),
            'error_code': 'VALIDATION_ERROR'
        }), 400

    except Exception as e:
        import traceback
        print(f"Error in rule engine API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/comprehensive-relationship', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_comprehensive_relationship_api():
    """
    Evaluate comprehensive relationship between planet and house ruling planet.

    This endpoint checks all 4 types of relationships:
    1. Basic position: Planet in house OR Planet in ruling planet's house
    2. WITH ruling planet: Planet with ruling planet OR Ruling planet with planet
    3. Nakshatra: Planet in ruling planet's star OR Ruling planet in planet's star
    4. Aspecting: Planet aspecting ruling planet OR Ruling planet aspecting planet

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "relationships": {
            "6th_house": {
                "house_ruling_planet": "SATURN",
                "overall_result": true,
                "relationships": {
                    "basic_position": true,
                    "with_ruling_planet": false,
                    "nakshatra": true,
                    "aspecting": true
                },
                "details": {...}
            },
            "10th_house": {...}
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_comprehensive_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse comprehensive relationship query
        # Expected formats:
        # 1. "Planet IS RELATED TO #th House_Ruling_Planet" (old format)
        # 2. "Planet IS RELATED TO #th_House_Planet" (new format)
        import re

        # Split by OR to handle multiple relationships
        or_parts = [part.strip() for part in query.split(' OR ')]

        relationships = {}
        overall_result = False

        for part in or_parts:
            # Try new format first: "Planet IS RELATED TO #th_House_Planet"
            pattern_new = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
            match = re.match(pattern_new, part, re.IGNORECASE)

            if match:
                planet, house_num = match.groups()
                house_num = int(house_num)

                # Use the enhanced planet-to-house-planet relationship function
                from ..services.rule_engine import check_planet_to_house_planet_relationship

                relationship_result = check_planet_to_house_planet_relationship(
                    chart_data, planet.upper(), house_num, chart_type
                )

                relationships[f"{house_num}th_house"] = relationship_result

                # Overall result is TRUE if any relationship is TRUE
                if relationship_result["overall_result"]:
                    overall_result = True
            else:
                # Try old format: "Planet IS RELATED TO #th House_Ruling_Planet"
                pattern_old = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House_Ruling_Planet'
                match = re.match(pattern_old, part, re.IGNORECASE)

                if match:
                    planet, house_num = match.groups()
                    house_num = int(house_num)

                    # Check comprehensive relationship (old function)
                    relationship_result = check_comprehensive_relationship(
                        chart_data, planet.upper(), house_num, chart_type
                    )

                    relationships[f"{house_num}th_house"] = relationship_result

                    # Overall result is TRUE if any relationship is TRUE
                    if relationship_result["overall_result"]:
                        overall_result = True

        if not relationships:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "Planet IS RELATED TO #th_House_Planet" or "Planet IS RELATED TO #th House_Ruling_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': overall_result,
            'relationships': relationships,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in comprehensive relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/house-ruling-planet-relationship', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_house_ruling_planet_relationship_api():
    """
    Evaluate comprehensive relationship between two house ruling planets.

    This endpoint checks all 5 types of relationships:
    1. Basic position: House1_ruling_planet in House2 OR House2_ruling_planet in House1
    2. WITH ruling planet: House1_ruling_planet in House2 WITH House2_ruling_planet OR House2_ruling_planet in House1 WITH House1_ruling_planet
    3. Together: House1_ruling_planet TOGETHER_WITH House2_ruling_planet
    4. Nakshatra: House1_ruling_planet in House2_ruling_planet's star OR House2_ruling_planet in House1_ruling_planet's star
    5. Aspecting: House1_ruling_planet aspecting House2_ruling_planet OR House2_ruling_planet aspecting House1_ruling_planet

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "house1_ruling_planet": "SATURN",
        "house2_ruling_planet": "SATURN",
        "relationships": {
            "basic_position": true,
            "with_ruling_planet": false,
            "together": true,
            "nakshatra": false,
            "aspecting": true
        },
        "details": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_house_ruling_planet_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse house ruling planet relationship query
        # Expected formats:
        # "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
        # "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
        import re

        # Try format 1: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
        pattern1 = r'House(\d+)_Ruling_Planet\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
        match = re.match(pattern1, query, re.IGNORECASE)

        # Try format 2: "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"
        if not match:
            pattern2 = r'(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Ruling_Planet'
            match = re.match(pattern2, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet" or "#th_House_Ruling_Planet IS RELATED TO #th_House_Ruling_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1, house2 = match.groups()
        house1, house2 = int(house1), int(house2)

        # Check comprehensive relationship
        relationship_result = check_house_ruling_planet_relationship(
            chart_data, house1, house2, chart_type
        )

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': relationship_result["overall_result"],
            'house1_ruling_planet': relationship_result["house1_ruling_planet"],
            'house2_ruling_planet': relationship_result["house2_ruling_planet"],
            'relationships': relationship_result["relationships"],
            'details': relationship_result["details"],
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in house ruling planet relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/house-planet-relationship', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_house_planet_relationship_api():
    """
    Evaluate comprehensive relationship between planets in two houses.

    This endpoint checks all 5 types of relationships between planets present in the specified houses:
    1. Basic position: Planet1 in House2 OR Planet2 in House1
    2. WITH ruling planet: Planet1 in House2 WITH House2_ruling_planet OR Planet2 in House1 WITH House1_ruling_planet
    3. Together: Planet1 TOGETHER_WITH Planet2 (in same house)
    4. Nakshatra: Planet1 in Planet2's star OR Planet2 in Planet1's star
    5. Aspecting: Planet1 aspecting Planet2 OR Planet2 aspecting Planet1

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "6th House Planet IS RELATED TO 10th House Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "house1_planets": ["KETU"],
        "house2_planets": [],
        "planet_relationships": {...},
        "summary": {
            "basic_position": true,
            "with_ruling_planet": false,
            "together": false,
            "nakshatra": false,
            "aspecting": true
        },
        "details": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_house_planet_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse house planet relationship query
        # Expected formats:
        # "#th House Planet IS RELATED TO #th House Planet"
        # "#th_House_Planet IS RELATED TO #th_House_Planet"
        import re

        # Try format 1: "#th House Planet IS RELATED TO #th House Planet"
        pattern1 = r'(\d+)(?:st|nd|rd|th)?\s+House\s+Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House\s+Planet'
        match = re.match(pattern1, query, re.IGNORECASE)

        # Try format 2: "#th_House_Planet IS RELATED TO #th_House_Planet"
        if not match:
            pattern2 = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
            match = re.match(pattern2, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "#th House Planet IS RELATED TO #th House Planet" or "#th_House_Planet IS RELATED TO #th_House_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1, house2 = match.groups()
        house1, house2 = int(house1), int(house2)

        # Check comprehensive relationship
        relationship_result = check_house_planet_relationship(
            chart_data, house1, house2, chart_type
        )

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': relationship_result["overall_result"],
            'house1_planets': relationship_result["house1_planets"],
            'house2_planets': relationship_result["house2_planets"],
            'planet_relationships': relationship_result["planet_relationships"],
            'summary': relationship_result["summary"],
            'details': relationship_result["details"],
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in house planet relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/suggestions', methods=['GET'])
# @jwt_required()  # Temporarily disabled for testing
def get_rule_suggestions():
    """
    Get suggestions for building rule queries.

    This endpoint returns lists of available planets, operators, and logical operators
    that can be used to build rule queries.

    Response JSON:
    {
        "success": true,
        "planets": ["SUN", "MOON", "MARS", ...],
        "operators": ["IN", "NOT IN"],
        "logical_operators": ["AND", "OR"],
        "houses": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        "example_queries": [
            "Moon IN House1 OR Moon IN House3",
            "Sun IN House5 AND Jupiter IN House9",
            ...
        ]
    }
    """
    try:
        # Define available options
        planets = ["SUN", "MOON", "MARS", "MERCURY", "JUPITER", "VENUS", "SATURN", "RAHU", "KETU"]
        operators = ["IN", "NOT IN", "IS RELATED TO", "IS ASPECTING_BIRTH"]
        logical_operators = ["AND", "OR"]
        houses = list(range(1, 13))  # Houses 1-12

        # House ruling planets
        house_ruling_planets = {
            1: "MARS", 2: "VENUS", 3: "MERCURY", 4: "MOON", 5: "SUN", 6: "MERCURY",
            7: "VENUS", 8: "MARS", 9: "JUPITER", 10: "SATURN", 11: "SATURN", 12: "JUPITER"
        }

        # Example queries
        example_queries = [
            "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
            "Sun IN House5 AND Jupiter IN House9",
            "Mars IN House10 OR Saturn IN House10",
            "Jupiter IN House1 AND Venus IN House7",
            "Rahu NOT IN House8 AND Ketu NOT IN House2",
            "Ketu IN House6 WITH Ruling_Planet",
            "Ketu IN House10 WITH Ruling_Planet",
            "Ketu IS RELATED TO House6_Ruling_Planet",
            "Ketu IS RELATED TO House10_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House10_Ruling_Planet",
            "Mars IN House6 WITH Ruling_Planet OR Venus IS RELATED TO House7_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet OR Ketu IS ASPECTING_BIRTH House10_Ruling_Planet"
        ]

        return jsonify({
            'success': True,
            'planets': planets,
            'operators': operators,
            'logical_operators': logical_operators,
            'houses': houses,
            'house_ruling_planets': house_ruling_planets,
            'example_queries': example_queries
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in rule suggestions API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error in rule suggestions API: {str(e)}'
        }), 500


@api_bp.route('/rule-engine/debug-chart', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def debug_chart_api():
    """
    Debug chart data structure for troubleshooting.

    This endpoint helps analyze the chart data structure to identify issues
    with planet-house mapping and data format.

    Request JSON:
    {
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "chart_data_exists": true/false,
        "chart_type_exists": true/false,
        "available_charts": ["D1", "D2", ...],
        "chart_structure": {...},
        "houses_structure": {...},
        "planets_found": ["sun", "moon", ...],
        "debug_info": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        if not user_profile_id or not member_profile_id:
            return jsonify({
                'success': False,
                'message': 'Both user_profile_id and member_profile_id are required',
                'error_code': 'MISSING_REQUIRED_FIELDS'
            }), 400

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Debug chart structure
        debug_info = debug_chart_structure(chart_data, chart_type)

        return jsonify({
            'success': True,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'chart_type': chart_type,
            **debug_info
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in debug chart API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500
