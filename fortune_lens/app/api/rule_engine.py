"""
Rule Engine API

This module provides API endpoints for evaluating complex astrological rules and conditions.
"""

from flask import request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId

from . import api_bp
from ..services.rule_engine import evaluate_rule, debug_chart_structure, get_chart_data
from ..errors.exceptions import ValidationError


@api_bp.route('/rule-engine/evaluate', methods=['POST'])
@jwt_required()
def evaluate_rule_api():
    """
    Evaluate a complex astrological rule.

    This endpoint accepts a rule query string and evaluates it against the chart data
    for a specific user and member profile.

    Request JSON:
    {
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "query": "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON (Success):
    {
        "success": true,
        "query": "original query string",
        "chart_type": "D1",
        "result": true/false,
        "planet_positions": {
            "SUN": 2,
            "MOON": 4,
            ...
        },
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id"
    }

    Response JSON (Error):
    {
        "success": false,
        "message": "Error description",
        "error_code": "ERROR_CODE",
        "missing_fields": ["field1", "field2"] (if applicable),
        "valid_chart_types": ["D1", "D2", ...] (if applicable)
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Validate chart_type
        valid_chart_types = ['D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7', 'D8', 'D9', 'D10', 'D11', 'D12',
                           'D16', 'D20', 'D24', 'D27', 'D30', 'D40', 'D45', 'D60', 'D81', 'D108', 'D144']
        if chart_type not in valid_chart_types:
            return jsonify({
                'success': False,
                'message': f'Invalid chart_type: {chart_type}. Must be one of: {", ".join(valid_chart_types)}',
                'error_code': 'INVALID_CHART_TYPE',
                'valid_chart_types': valid_chart_types
            }), 400

        # Evaluate rule
        result = evaluate_rule(query, user_profile_id, member_profile_id, chart_type)

        # Determine appropriate HTTP status code based on result
        if result.get('success', False):
            status_code = 200
        else:
            error_code = result.get('error_code', 'UNKNOWN_ERROR')
            if error_code in ['CHART_DATA_NOT_FOUND', 'NO_PLANET_DATA']:
                status_code = 404
            elif error_code in ['INVALID_QUERY_FORMAT', 'INVALID_CHART_STRUCTURE']:
                status_code = 400
            else:
                status_code = 500

        return jsonify(result), status_code

    except ValidationError as e:
        return jsonify({
            'success': False,
            'message': str(e),
            'error_code': 'VALIDATION_ERROR'
        }), 400

    except Exception as e:
        import traceback
        print(f"Error in rule engine API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/comprehensive-relationship', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_comprehensive_relationship_api():
    """
    Evaluate comprehensive relationship between planet and house ruling planet.

    This endpoint checks all 4 types of relationships:
    1. Basic position: Planet in house OR Planet in ruling planet's house
    2. WITH ruling planet: Planet with ruling planet OR Ruling planet with planet
    3. Nakshatra: Planet in ruling planet's star OR Ruling planet in planet's star
    4. Aspecting: Planet aspecting ruling planet OR Ruling planet aspecting planet

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "Ketu IS RELATED TO 6th House_Ruling_Planet OR Ketu IS RELATED TO 10th House_Ruling_Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "relationships": {
            "6th_house": {
                "house_ruling_planet": "SATURN",
                "overall_result": true,
                "relationships": {
                    "basic_position": true,
                    "with_ruling_planet": false,
                    "nakshatra": true,
                    "aspecting": true
                },
                "details": {...}
            },
            "10th_house": {...}
        }
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_comprehensive_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse comprehensive relationship query
        # Expected format: "Planet IS RELATED TO #th House_Ruling_Planet OR Planet IS RELATED TO #th House_Ruling_Planet"
        import re

        # Split by OR to handle multiple relationships
        or_parts = [part.strip() for part in query.split(' OR ')]

        relationships = {}
        overall_result = False

        for part in or_parts:
            # Parse each part: "Planet IS RELATED TO #th House_Ruling_Planet"
            pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House_Ruling_Planet'
            match = re.match(pattern, part, re.IGNORECASE)

            if match:
                planet, house_num = match.groups()
                house_num = int(house_num)

                # Check comprehensive relationship
                relationship_result = check_comprehensive_relationship(
                    chart_data, planet.upper(), house_num, chart_type
                )

                relationships[f"{house_num}th_house"] = relationship_result

                # Overall result is TRUE if any relationship is TRUE
                if relationship_result["overall_result"]:
                    overall_result = True

        if not relationships:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "Planet IS RELATED TO #th House_Ruling_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': overall_result,
            'relationships': relationships,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in comprehensive relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/house-ruling-planet-relationship', methods=['POST'])
@jwt_required()
def evaluate_house_ruling_planet_relationship_api():
    """
    Evaluate comprehensive relationship between two house ruling planets.

    This endpoint checks all 5 types of relationships:
    1. Basic position: House1_ruling_planet in House2 OR House2_ruling_planet in House1
    2. WITH ruling planet: House1_ruling_planet in House2 WITH House2_ruling_planet OR House2_ruling_planet in House1 WITH House1_ruling_planet
    3. Together: House1_ruling_planet TOGETHER_WITH House2_ruling_planet
    4. Nakshatra: House1_ruling_planet in House2_ruling_planet's star OR House2_ruling_planet in House1_ruling_planet's star
    5. Aspecting: House1_ruling_planet aspecting House2_ruling_planet OR House2_ruling_planet aspecting House1_ruling_planet

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "house1_ruling_planet": "SATURN",
        "house2_ruling_planet": "SATURN",
        "relationships": {
            "basic_position": true,
            "with_ruling_planet": false,
            "together": true,
            "nakshatra": false,
            "aspecting": true
        },
        "details": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_house_ruling_planet_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse house ruling planet relationship query
        # Expected format: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"
        import re

        pattern = r'House(\d+)_Ruling_Planet\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
        match = re.match(pattern, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "House#_Ruling_Planet IS RELATED TO House#_Ruling_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1, house2 = match.groups()
        house1, house2 = int(house1), int(house2)

        # Check comprehensive relationship
        relationship_result = check_house_ruling_planet_relationship(
            chart_data, house1, house2, chart_type
        )

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': relationship_result["overall_result"],
            'house1_ruling_planet': relationship_result["house1_ruling_planet"],
            'house2_ruling_planet': relationship_result["house2_ruling_planet"],
            'relationships': relationship_result["relationships"],
            'details': relationship_result["details"],
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in house ruling planet relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/house-planet-relationship', methods=['POST'])
# @jwt_required()  # Temporarily disabled for testing
def evaluate_house_planet_relationship_api():
    """
    Evaluate comprehensive relationship between planets in two houses.

    This endpoint checks all 5 types of relationships between planets present in the specified houses:
    1. Basic position: Planet1 in House2 OR Planet2 in House1
    2. WITH ruling planet: Planet1 in House2 WITH House2_ruling_planet OR Planet2 in House1 WITH House1_ruling_planet
    3. Together: Planet1 TOGETHER_WITH Planet2 (in same house)
    4. Nakshatra: Planet1 in Planet2's star OR Planet2 in Planet1's star
    5. Aspecting: Planet1 aspecting Planet2 OR Planet2 aspecting Planet1

    Request JSON:
    {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "6th House Planet IS RELATED TO 10th House Planet",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "query": "original query",
        "overall_result": true/false,
        "house1_planets": ["KETU"],
        "house2_planets": [],
        "planet_relationships": {...},
        "summary": {
            "basic_position": true,
            "with_ruling_planet": false,
            "together": false,
            "nakshatra": false,
            "aspecting": true
        },
        "details": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        query = data.get('query')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        missing_fields = []
        if not user_profile_id:
            missing_fields.append('user_profile_id')
        if not member_profile_id:
            missing_fields.append('member_profile_id')
        if not query:
            missing_fields.append('query')

        if missing_fields:
            return jsonify({
                'success': False,
                'message': f'Missing required fields: {", ".join(missing_fields)}',
                'error_code': 'MISSING_REQUIRED_FIELDS',
                'missing_fields': missing_fields
            }), 400

        # Get chart data
        from ..services.rule_engine import get_chart_data, check_house_planet_relationship

        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Parse house planet relationship query
        # Expected formats:
        # "#th House Planet IS RELATED TO #th House Planet"
        # "#th_House_Planet IS RELATED TO #th_House_Planet"
        import re

        # Try format 1: "#th House Planet IS RELATED TO #th House Planet"
        pattern1 = r'(\d+)(?:st|nd|rd|th)?\s+House\s+Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House\s+Planet'
        match = re.match(pattern1, query, re.IGNORECASE)

        # Try format 2: "#th_House_Planet IS RELATED TO #th_House_Planet"
        if not match:
            pattern2 = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
            match = re.match(pattern2, query, re.IGNORECASE)

        if not match:
            return jsonify({
                'success': False,
                'message': 'Invalid query format. Expected: "#th House Planet IS RELATED TO #th House Planet" or "#th_House_Planet IS RELATED TO #th_House_Planet"',
                'error_code': 'INVALID_QUERY_FORMAT'
            }), 400

        house1, house2 = match.groups()
        house1, house2 = int(house1), int(house2)

        # Check comprehensive relationship
        relationship_result = check_house_planet_relationship(
            chart_data, house1, house2, chart_type
        )

        return jsonify({
            'success': True,
            'query': query,
            'chart_type': chart_type,
            'overall_result': relationship_result["overall_result"],
            'house1_planets': relationship_result["house1_planets"],
            'house2_planets': relationship_result["house2_planets"],
            'planet_relationships': relationship_result["planet_relationships"],
            'summary': relationship_result["summary"],
            'details': relationship_result["details"],
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in house planet relationship API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500


@api_bp.route('/rule-engine/suggestions', methods=['GET'])
@jwt_required()
def get_rule_suggestions():
    """
    Get suggestions for building rule queries.

    This endpoint returns lists of available planets, operators, and logical operators
    that can be used to build rule queries.

    Response JSON:
    {
        "success": true,
        "planets": ["SUN", "MOON", "MARS", ...],
        "operators": ["IN", "NOT IN"],
        "logical_operators": ["AND", "OR"],
        "houses": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
        "example_queries": [
            "Moon IN House1 OR Moon IN House3",
            "Sun IN House5 AND Jupiter IN House9",
            ...
        ]
    }
    """
    try:
        # Define available options
        planets = ["SUN", "MOON", "MARS", "MERCURY", "JUPITER", "VENUS", "SATURN", "RAHU", "KETU"]
        operators = ["IN", "NOT IN", "IS RELATED TO", "IS ASPECTING_BIRTH"]
        logical_operators = ["AND", "OR"]
        houses = list(range(1, 13))  # Houses 1-12

        # House ruling planets
        house_ruling_planets = {
            1: "MARS", 2: "VENUS", 3: "MERCURY", 4: "MOON", 5: "SUN", 6: "MERCURY",
            7: "VENUS", 8: "MARS", 9: "JUPITER", 10: "SATURN", 11: "SATURN", 12: "JUPITER"
        }

        # Example queries
        example_queries = [
            "Moon IN House1 OR Moon IN House3 OR Moon IN House6 OR Moon IN House10 OR Moon IN House11",
            "Sun IN House5 AND Jupiter IN House9",
            "Mars IN House10 OR Saturn IN House10",
            "Jupiter IN House1 AND Venus IN House7",
            "Rahu NOT IN House8 AND Ketu NOT IN House2",
            "Ketu IN House6 WITH Ruling_Planet",
            "Ketu IN House10 WITH Ruling_Planet",
            "Ketu IS RELATED TO House6_Ruling_Planet",
            "Ketu IS RELATED TO House10_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House10_Ruling_Planet",
            "Mars IN House6 WITH Ruling_Planet OR Venus IS RELATED TO House7_Ruling_Planet",
            "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet OR Ketu IS ASPECTING_BIRTH House10_Ruling_Planet"
        ]

        return jsonify({
            'success': True,
            'planets': planets,
            'operators': operators,
            'logical_operators': logical_operators,
            'houses': houses,
            'house_ruling_planets': house_ruling_planets,
            'example_queries': example_queries
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in rule suggestions API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Error in rule suggestions API: {str(e)}'
        }), 500


@api_bp.route('/rule-engine/debug-chart', methods=['POST'])
@jwt_required()
def debug_chart_api():
    """
    Debug chart data structure for troubleshooting.

    This endpoint helps analyze the chart data structure to identify issues
    with planet-house mapping and data format.

    Request JSON:
    {
        "user_profile_id": "user_profile_id",
        "member_profile_id": "member_profile_id",
        "chart_type": "D1" (optional, defaults to "D1")
    }

    Response JSON:
    {
        "success": true,
        "chart_data_exists": true/false,
        "chart_type_exists": true/false,
        "available_charts": ["D1", "D2", ...],
        "chart_structure": {...},
        "houses_structure": {...},
        "planets_found": ["sun", "moon", ...],
        "debug_info": {...}
    }
    """
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'Request body must contain JSON data',
                'error_code': 'NO_DATA_PROVIDED'
            }), 400

        # Get parameters
        user_profile_id = data.get('user_profile_id')
        member_profile_id = data.get('member_profile_id')
        chart_type = data.get('chart_type', 'D1').upper()

        # Validate required fields
        if not user_profile_id or not member_profile_id:
            return jsonify({
                'success': False,
                'message': 'Both user_profile_id and member_profile_id are required',
                'error_code': 'MISSING_REQUIRED_FIELDS'
            }), 400

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return jsonify({
                'success': False,
                'message': f'Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}',
                'error_code': 'CHART_DATA_NOT_FOUND'
            }), 404

        # Debug chart structure
        debug_info = debug_chart_structure(chart_data, chart_type)

        return jsonify({
            'success': True,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'chart_type': chart_type,
            **debug_info
        }), 200

    except Exception as e:
        import traceback
        print(f"Error in debug chart API: {str(e)}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'message': f'Internal server error: {str(e)}',
            'error_code': 'INTERNAL_SERVER_ERROR'
        }), 500
