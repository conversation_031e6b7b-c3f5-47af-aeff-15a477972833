#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
API endpoints for Tamil Panchanga (Daily Panchanga) functionality.
"""

from flask import Blueprint, request, jsonify
from datetime import datetime
from ..services.tamil_panchanga import (
    get_daily_panchanga,
    get_monthly_panchanga,
    get_festival_dates,
    get_tamil_date
)

# Create a blueprint for the daily panchanga API
daily_panchanga_bp = Blueprint('daily_panchanga', __name__)

@daily_panchanga_bp.route('/daily', methods=['GET'])
def get_daily():
    """
    Get the daily panchanga for the given date.
    
    Query Parameters:
        date (str, optional): Date in the format "YYYY-MM-DD". Defaults to current date.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.
        
    Returns:
        JSON: Daily panchanga information
    """
    # Get query parameters
    date_str = request.args.get('date', None)
    lat = float(request.args.get('lat', 13.0878))
    lon = float(request.args.get('lon', 80.2785))
    tz = float(request.args.get('tz', 5.5))
    
    # Parse date
    if date_str:
        try:
            date_parts = date_str.split('-')
            date = datetime(int(date_parts[0]), int(date_parts[1]), int(date_parts[2]))
        except (ValueError, IndexError):
            return jsonify({
                'success': False,
                'error': 'Invalid date format. Use YYYY-MM-DD.'
            }), 400
    else:
        date = datetime.now()
    
    try:
        # Get daily panchanga
        panchanga_data = get_daily_panchanga(date, lat, lon, tz)
        
        return jsonify({
            'success': True,
            'data': panchanga_data
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@daily_panchanga_bp.route('/monthly', methods=['GET'])
def get_monthly():
    """
    Get the panchanga for an entire month.
    
    Query Parameters:
        year (int, optional): Year. Defaults to current year.
        month (int, optional): Month (1-12). Defaults to current month.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.
        
    Returns:
        JSON: Monthly panchanga information
    """
    # Get query parameters
    year = int(request.args.get('year', datetime.now().year))
    month = int(request.args.get('month', datetime.now().month))
    lat = float(request.args.get('lat', 13.0878))
    lon = float(request.args.get('lon', 80.2785))
    tz = float(request.args.get('tz', 5.5))
    
    # Validate month
    if month < 1 or month > 12:
        return jsonify({
            'success': False,
            'error': 'Invalid month. Month must be between 1 and 12.'
        }), 400
    
    try:
        # Get monthly panchanga
        monthly_panchanga = get_monthly_panchanga(year, month, lat, lon, tz)
        
        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'month': month,
                'days': monthly_panchanga
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@daily_panchanga_bp.route('/festivals', methods=['GET'])
def get_festivals():
    """
    Get the dates of major Tamil festivals for the given year.
    
    Query Parameters:
        year (int, optional): Year. Defaults to current year.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.
        
    Returns:
        JSON: Festival dates with details
    """
    # Get query parameters
    year = int(request.args.get('year', datetime.now().year))
    lat = float(request.args.get('lat', 13.0878))
    lon = float(request.args.get('lon', 80.2785))
    tz = float(request.args.get('tz', 5.5))
    
    try:
        # Get festival dates
        festival_dates = get_festival_dates(year, lat, lon, tz)
        
        return jsonify({
            'success': True,
            'data': {
                'year': year,
                'festivals': festival_dates
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@daily_panchanga_bp.route('/tamil-date', methods=['GET'])
def get_tamil_date_api():
    """
    Get the Tamil date for the given Gregorian date.
    
    Query Parameters:
        date (str, optional): Date in the format "YYYY-MM-DD". Defaults to current date.
        lat (float, optional): Latitude. Defaults to Chennai latitude.
        lon (float, optional): Longitude. Defaults to Chennai longitude.
        tz (float, optional): Timezone offset. Defaults to IST.
        
    Returns:
        JSON: Tamil date information
    """
    # Get query parameters
    date_str = request.args.get('date', None)
    lat = float(request.args.get('lat', 13.0878))
    lon = float(request.args.get('lon', 80.2785))
    tz = float(request.args.get('tz', 5.5))
    
    # Parse date
    if date_str:
        try:
            date_parts = date_str.split('-')
            date = datetime(int(date_parts[0]), int(date_parts[1]), int(date_parts[2]))
        except (ValueError, IndexError):
            return jsonify({
                'success': False,
                'error': 'Invalid date format. Use YYYY-MM-DD.'
            }), 400
    else:
        date = datetime.now()
    
    try:
        # Get Tamil date
        tamil_date = get_tamil_date(date, lat, lon, tz)
        
        return jsonify({
            'success': True,
            'data': tamil_date
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
