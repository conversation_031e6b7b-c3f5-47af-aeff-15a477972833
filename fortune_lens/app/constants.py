"""
Constants for the Fortune Lens application.

This file contains all the constants used throughout the application,
including HTTP methods, API routes, response messages, collection names,
field names, status codes, and other configuration values.

Organizing constants in this way makes the code more maintainable and
reduces the risk of typos or inconsistencies across the application.
"""

# HTTP Methods
class HttpMethod:
    """HTTP method constants used in API endpoints"""
    GET = 'GET'
    POST = 'POST'
    PUT = 'PUT'
    DELETE = 'DELETE'
    PATCH = 'PATCH'


# API Routes
class ApiRoute:
    """API route constants for all endpoints"""
    # Auth routes
    AUTH_REGISTER = '/auth/register'
    AUTH_LOGIN = '/auth/login'
    AUTH_REFRESH = '/auth/refresh'
    AUTH_ME = '/auth/me'

    # User routes
    USERS = '/users'
    USER_DETAIL = '/users/<user_id>'

    # Profile routes
    PROFILES = '/profiles'
    PROFILE_DETAIL = '/profiles/<profile_id>'

    # Member profile routes
    MEMBER_PROFILES = '/member-profiles'
    MEMBER_PROFILE_DETAIL = '/member-profiles/<profile_id>'

    # Chart routes
    CHARTS = '/charts'
    GENERATE_CHART = '/charts/generate'

    # Marriage matching routes
    MARRIAGE_MATCHING = '/marriage-matching'

    # Panchanga routes
    DAILY_PANCHANGA = '/panchanga/daily'
    TAMIL_PANCHANGA = '/panchanga/tamil'


# Response Messages
class ResponseMessage:
    """Standard response messages for API responses"""
    # Success messages
    USER_REGISTERED = 'User registered successfully'
    USER_UPDATED = 'User updated successfully'
    USER_DELETED = 'User deleted successfully'
    LOGIN_SUCCESS = 'Login successful'
    PROFILE_CREATED = 'Profile created successfully'
    PROFILE_UPDATED = 'Profile updated successfully'
    PROFILE_DELETED = 'Profile deleted successfully'
    MEMBER_PROFILE_CREATED = 'Member profile created successfully'
    MEMBER_PROFILE_UPDATED = 'Member profile updated successfully'
    MEMBER_PROFILE_DELETED = 'Member profile deleted successfully'
    OTP_SENT = 'OTP sent successfully'
    OTP_VERIFIED = 'OTP verified successfully'
    CHART_GENERATED = 'Chart generated successfully'
    MARRIAGE_MATCH_CALCULATED = 'Marriage compatibility calculated successfully'
    PANCHANGA_CALCULATED = 'Panchanga calculated successfully'

    # Error messages
    INVALID_CREDENTIALS = 'Invalid email or password'
    USER_NOT_FOUND = 'User not found'
    PROFILE_NOT_FOUND = 'Profile not found'
    MEMBER_PROFILE_NOT_FOUND = 'Member profile not found'
    EMAIL_ALREADY_REGISTERED = 'Email already registered'
    MOBILE_ALREADY_REGISTERED = 'Mobile number already registered'
    INVALID_OTP = 'Invalid or expired OTP'
    UNAUTHORIZED = 'Unauthorized access'
    VALIDATION_ERROR = 'Validation error'
    CHART_GENERATION_ERROR = 'Error generating chart'
    INVALID_BIRTH_DATA = 'Invalid birth date, time, or location data'
    MARRIAGE_MATCH_ERROR = 'Error calculating marriage compatibility'
    PANCHANGA_ERROR = 'Error calculating panchanga'


# OTP Types and Settings
class OtpConfig:
    """Configuration for One-Time Password functionality"""
    TYPE_REGISTRATION = 'registration'
    TYPE_LOGIN = 'login'
    TYPE_RESET_PASSWORD = 'reset_password'

    LENGTH = 6
    EXPIRY_MINUTES = 10

    COLLECTION = 'otps'

    # OTP Email Subject
    EMAIL_SUBJECT = 'Your Fortune Lens OTP'


# MongoDB Collections
class Collection:
    """MongoDB collection names used in the application"""
    USER_PROFILE = 'user_profile'
    MEMBER_PROFILE = 'member_profile'
    USER_MEMBER_ASTRO_PROFILE_DATA = 'user_member_astro_profile_data'
    OTP = 'otps'


# Field Names
class Field:
    """Database field names for MongoDB collections"""
    # Common fields
    ID = '_id'  # Legacy ID field, kept for backward compatibility
    USER_PROFILE_ID = 'user_profile_id'  # New user profile ID field
    MEMBER_PROFILE_ID = 'member_profile_id'  # New member profile ID field
    CREATED_AT = 'created_at'
    UPDATED_AT = 'updated_at'

    # User fields
    USER_ID = 'user_id'
    EMAIL = 'email'
    PASSWORD = 'password'
    NAME = 'name'
    MOBILE = 'mobile'

    # Profile fields
    PROFILE_ID = 'profile_id'
    BIRTHDATE = 'user_birthdate'
    BIRTHTIME = 'user_birthtime'
    BIRTHPLACE = 'user_birthplace'
    LATITUDE = 'latitude'
    LONGITUDE = 'longitude'
    STATE = 'user_state'
    COUNTRY = 'user_country'

    # OTP fields
    OTP = 'otp'
    TYPE = 'type'
    EXPIRES_AT = 'expires_at'
    VERIFIED = 'verified'

    # Chart fields
    CHART_TYPE = 'chart_type'
    CHART_DATA = 'chart_data'
    DIVISIONAL_CHARTS = 'divisional_charts'

    # Astrological fields
    PLANETS = 'planets'
    HOUSES = 'houses'
    NAKSHATRAS = 'nakshatras'
    DASHAS = 'dashas'


# HTTP Status Codes
class StatusCode:
    """HTTP status codes used in API responses"""
    OK = 200
    CREATED = 201
    ACCEPTED = 202
    NO_CONTENT = 204
    BAD_REQUEST = 400
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    CONFLICT = 409
    UNPROCESSABLE_ENTITY = 422
    INTERNAL_SERVER_ERROR = 500
    SERVICE_UNAVAILABLE = 503


# Pagination defaults
class Pagination:
    """Default pagination settings"""
    DEFAULT_PAGE = 1
    DEFAULT_PER_PAGE = 10
    MAX_PER_PAGE = 100
