"""
Application Configuration

This module defines configuration classes for different environments (development, testing, production).
It loads settings from environment variables with sensible defaults for local development.
"""

import os
from datetime import timed<PERSON><PERSON>
from .constants import Collection


class BaseConfig:
    """Base configuration with common settings for all environments"""
    # Flask settings
    SECRET_KEY = os.getenv('SECRET_KEY', 'my_precious_secret_key')  # Should be overridden in production
    DEBUG = False

    # MongoDB settings
    MONGO_URI = os.getenv('MONGO_URI', 'mongodb://localhost:27017/fortune_lens')

    # MongoDB Collections
    MONGO_USER_COLLECTION = Collection.USER_PROFILE
    MONGO_MEMBER_PROFILE_COLLECTION = Collection.MEMBER_PROFILE
    MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION = Collection.USER_MEMBER_ASTRO_PROFILE_DATA

    # Email settings (using Gmail SMTP)
    MAIL_SERVER = os.getenv('MAIL_SERVER', 'smtp.gmail.com')
    MAIL_PORT = int(os.getenv('MAIL_PORT', 587))
    MAIL_USE_TLS = os.getenv('MAIL_USE_TLS', 'True') == 'True'
    MAIL_USERNAME = os.getenv('MAIL_USERNAME', '<EMAIL>')
    MAIL_PASSWORD = os.getenv('MAIL_PASSWORD', 'your-app-password')
    MAIL_DEFAULT_SENDER = os.getenv('MAIL_DEFAULT_SENDER', 'Fortune Lens <<EMAIL>>')

    # Twilio settings for SMS
    TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID', 'your-account-sid')
    TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN', 'your-auth-token')
    TWILIO_PHONE_NUMBER = os.getenv('TWILIO_PHONE_NUMBER', 'your-twilio-phone-number')

    # JWT settings
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'jwt_secret_key')  # Should be overridden in production
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(days=30)
    JWT_REFRESH_TOKEN_EXPIRES = timedelta(days=30)

    # Logging configuration
    LOG_LEVEL = 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = 'logs/app.log'

    # API settings
    API_TITLE = 'Fortune Lens API'
    API_VERSION = 'v1'
    API_DESCRIPTION = 'API for Fortune Lens application'


class DevelopmentConfig(BaseConfig):
    """Development configuration with debugging enabled"""
    DEBUG = True
    LOG_LEVEL = 'DEBUG'


class TestingConfig(BaseConfig):
    """Testing configuration with test database and debugging enabled"""
    DEBUG = True
    TESTING = True
    MONGO_URI = os.getenv('TEST_MONGO_URI', 'mongodb://localhost:27017/fortune_lens_test')
    LOG_LEVEL = 'DEBUG'


class ProductionConfig(BaseConfig):
    """Production configuration with secure settings and minimal logging"""
    # In production, these must be set as environment variables
    SECRET_KEY = os.getenv('SECRET_KEY')
    JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY')

    # Disable debug mode in production
    DEBUG = False

    # Only log errors in production
    LOG_LEVEL = 'ERROR'


# Configuration dictionary mapping environment names to config classes
config_by_name = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig
}
