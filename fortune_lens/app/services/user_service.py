"""
User Service
"""

import random
import string
from datetime import datetime
from bson import ObjectId

from ..extensions import mongo, bcrypt
from ..config import BaseConfig
from .new_sequence_service import NewSequenceService
from ..constants import Field


class UserService:
    """User Service"""

    @staticmethod
    def generate_unique_key(length=10):
        """
        Generate a random unique key

        Args:
            length (int): Length of the key

        Returns:
            str: Random unique key
        """
        characters = string.ascii_letters + string.digits
        return ''.join(random.choice(characters) for _ in range(length))

    @staticmethod
    def create_user(email, password, name, mobile=None):
        """
        Create a new user

        Args:
            email (str): User email
            password (str): User password
            name (str): User name
            mobile (str, optional): User mobile number

        Returns:
            dict: Created user document
        """
        # Hash password
        hashed_password = bcrypt.generate_password_hash(password).decode('utf-8')

        # Generate a unique key for joining collections
        unique_key = UserService.generate_unique_key()

        # Get next user profile ID
        user_profile_id = NewSequenceService.get_next_user_profile_id()

        # Create user document
        user = {
            '_id': NewSequenceService.get_new_object_id(),  # Using ObjectId for _id field
            Field.USER_PROFILE_ID: user_profile_id,  # Sequential ID for user_profile_id
            'email': email,
            'password': hashed_password,
            'name': name,
            'mobile': mobile,
            'unique_key': unique_key,  # Add unique key for joining
            'created_at': datetime.utcnow(),
            'updated_at': datetime.utcnow()
        }

        # Insert user into database
        result = mongo.db[BaseConfig.MONGO_USER_COLLECTION].insert_one(user)
        user['_id'] = result.inserted_id

        return user

    @staticmethod
    def get_user_by_id(user_id):
        """
        Get user by ID

        Args:
            user_id (str): User ID

        Returns:
            dict: User document if found, None otherwise
        """
        try:
            # Try to find by integer ID first
            if isinstance(user_id, int):
                # Try both legacy and new field names
                user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({'_id': user_id})
                if not user:
                    user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({Field.USER_PROFILE_ID: user_id})
                return user
            else:
                # Try to convert to integer if it's a string representing an integer
                try:
                    int_id = int(user_id)
                    # Try both legacy and new field names
                    user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({'_id': int_id})
                    if not user:
                        user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({Field.USER_PROFILE_ID: int_id})
                    return user
                except (ValueError, TypeError):
                    # If not an integer, try ObjectId
                    try:
                        # Try both legacy and new field names
                        user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({'_id': ObjectId(user_id)})
                        if not user:
                            user = mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({Field.USER_PROFILE_ID: ObjectId(user_id)})
                        return user
                    except:
                        return None
        except Exception as e:
            print(f"Error getting user by ID: {str(e)}")
            return None

    @staticmethod
    def get_user_by_email(email):
        """
        Get user by email

        Args:
            email (str): User email

        Returns:
            dict: User document if found, None otherwise
        """
        return mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({'email': email})

    @staticmethod
    def get_user_by_mobile(mobile):
        """
        Get user by mobile number

        Args:
            mobile (str): User mobile number

        Returns:
            dict: User document if found, None otherwise
        """
        return mongo.db[BaseConfig.MONGO_USER_COLLECTION].find_one({'mobile': mobile})

    @staticmethod
    def get_users(page=1, per_page=10):
        """
        Get paginated list of users

        Args:
            page (int): Page number
            per_page (int): Number of users per page

        Returns:
            tuple: (list of users, total count)
        """
        # Calculate skip value for pagination
        skip = (page - 1) * per_page

        # Get users
        users = list(mongo.db[BaseConfig.MONGO_USER_COLLECTION].find({}, {
            'password': 0  # Exclude password field
        }).skip(skip).limit(per_page))

        # Get total count
        total = mongo.db[BaseConfig.MONGO_USER_COLLECTION].count_documents({})

        # Convert ObjectId to string and ensure user_profile_id is present
        for user in users:
            user['_id'] = str(user['_id'])
            # Add user_profile_id if not present
            if Field.USER_PROFILE_ID not in user:
                user[Field.USER_PROFILE_ID] = user['_id']

        return users, total

    @staticmethod
    def update_user(user_id, data):
        """
        Update user

        Args:
            user_id (str): User ID
            data (dict): User data to update

        Returns:
            dict: Updated user document
        """
        # Prepare update data
        update_data = {
            'updated_at': datetime.utcnow()
        }

        # Add fields to update
        if 'name' in data:
            update_data['name'] = data['name']
        if 'email' in data:
            update_data['email'] = data['email']
        if 'mobile' in data:
            update_data['mobile'] = data['mobile']

        # Update user
        try:
            # Try to update by integer ID first
            if isinstance(user_id, int):
                # Update both legacy and new field
                update_data[Field.USER_PROFILE_ID] = user_id  # Ensure new field is updated
                mongo.db[BaseConfig.MONGO_USER_COLLECTION].update_one(
                    {'_id': user_id},
                    {'$set': update_data}
                )
            else:
                # If not an integer, try ObjectId
                try:
                    obj_id = ObjectId(user_id)
                    # Update both legacy and new field
                    update_data[Field.USER_PROFILE_ID] = user_id  # Ensure new field is updated
                    mongo.db[BaseConfig.MONGO_USER_COLLECTION].update_one(
                        {'_id': obj_id},
                        {'$set': update_data}
                    )
                except:
                    # Try as integer string
                    try:
                        int_id = int(user_id)
                        update_data[Field.USER_PROFILE_ID] = int_id  # Ensure new field is updated
                        mongo.db[BaseConfig.MONGO_USER_COLLECTION].update_one(
                            {'_id': int_id},
                            {'$set': update_data}
                        )
                    except:
                        pass
        except Exception as e:
            print(f"Error updating user: {str(e)}")

        # Get updated user using the get_user_by_id method which already handles both field names
        user = UserService.get_user_by_id(user_id)

        if not user:
            return None

        # Remove password from response
        if 'password' in user:
            del user['password']

        # Convert ObjectId to string
        user['_id'] = str(user['_id'])

        # Ensure user_profile_id is present
        if Field.USER_PROFILE_ID not in user:
            user[Field.USER_PROFILE_ID] = user['_id']

        return user

    @staticmethod
    def delete_user(user_id):
        """
        Delete user

        Args:
            user_id (str): User ID

        Returns:
            bool: True if user deleted, False otherwise
        """
        try:
            # Try to delete by integer ID first
            if isinstance(user_id, int):
                result = mongo.db[BaseConfig.MONGO_USER_COLLECTION].delete_one({'_id': user_id})
            else:
                # If not an integer, try ObjectId
                result = mongo.db[BaseConfig.MONGO_USER_COLLECTION].delete_one({'_id': ObjectId(user_id)})
        except Exception as e:
            print(f"Error deleting user: {str(e)}")
            return False
        return result.deleted_count > 0
