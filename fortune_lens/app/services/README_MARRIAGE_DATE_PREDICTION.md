# Marriage Date Prediction Service

This service provides functionality to predict potential marriage dates based on Vedic astrology principles.

## Overview

The marriage date prediction service analyzes a member's birth chart to identify favorable periods for marriage based on planetary positions, aspects, and dasha periods. It focuses on the 2nd and 7th houses (which are associated with marriage in Vedic astrology) and the positions of <PERSON> and Venus, which are key planets for marriage timing.

## Prediction Factors

The service analyzes the following astrological factors:

1. **Jupiter's Aspects to Marriage Houses**
   - 5th, 7th, and 9th aspects of Jupiter to the 2nd and 7th houses
   - Jupiter's transit through marriage houses

2. **<PERSON>'s Aspects to Moon Sign (Rasi)**
   - 5th, 7th, and 9th aspects of Jupiter to the Moon sign
   - Jupiter's transit through the Moon sign

3. **Venus's Aspects to Marriage Houses**
   - 7th aspect of <PERSON> to the 2nd and 7th houses
   - Venus's transit through marriage houses

4. **Venus's Aspects to Moon Sign (Rasi)**
   - 7th aspect of Venus to the Moon sign
   - Venus's transit through the Moon sign

5. **Dasha Periods**
   - Favorable dasha periods based on the ruling planets of the 2nd and 7th houses

## Implementation Details

The implementation follows these steps:

1. **Extract Birth Chart Data**
   - Get the member's D1 chart (Rasi chart) from the database
   - Extract planet positions, house names, and dasha periods

2. **Identify Marriage Houses**
   - Find the 2nd and 7th houses in the birth chart
   - Determine the ruling planets of these houses

3. **Find Favorable Dasha Periods**
   - Identify dasha periods ruled by the planets associated with marriage houses
   - Filter periods to those occurring between ages 21 and 40 (customizable)

4. **Analyze Planetary Aspects**
   - Calculate Jupiter's aspects to marriage houses and Moon sign
   - Calculate Venus's aspects to marriage houses and Moon sign
   - Identify periods when these aspects are active

5. **Format Results**
   - Organize the prediction results into a structured format
   - Provide date ranges for potential marriage periods

## Usage

The service can be accessed through the following API endpoints:

- `POST /api/marriage-date-prediction`: Predict marriage dates for a member
- `GET /api/marriage-date-prediction/{member_id}`: Get marriage date prediction for a member
- `GET /api/marriage-date-prediction/user/{user_profile_id}/{member_profile_id}`: Get marriage date prediction for a member of a user

You can also use the service directly in your code:

```python
from fortune_lens.app.services.marriage_matching import predict_marriage_dates

# Predict marriage dates
result = predict_marriage_dates(member_id, start_age=21, end_age=40)
```

## References

- Traditional Vedic astrology texts on marriage timing
- Dasha system for timing of life events
- Planetary aspects and transits in Vedic astrology
