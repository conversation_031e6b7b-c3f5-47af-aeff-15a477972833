"""
Marriage Date Prediction

This module provides functionality to predict potential marriage dates
based on astrological factors in Vedic astrology.
"""

import random
import string
import sys
import os
from datetime import datetime, timedelta
import swisseph as swe
from bson import ObjectId

from ...extensions import mongo
from ...config import BaseConfig
from ...constants import Collection, Field
from .utils import get_member_astro_data, extract_planet_positions, extract_house_names, extract_nakshatra_data
from ...services.horoscope.dhasa.graha.vimsottari import get_vimsottari_dhasa_bhukthi
from ...services.utils.date_time import gregorian_to_jd
from ...services.utils.location import get_location

# Define norm360 function here to avoid import issues
def norm360(longitude):
    """Normalize longitude to 0-360 degrees."""
    return longitude % 360

# Define zodiac signs
RAASI_LIST = ['Mesham', 'Rishabam', 'Midunam', '<PERSON>dagam', 'Simmam', '<PERSON>nn<PERSON>',
              '<PERSON><PERSON><PERSON>', '<PERSON>iri<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>garam', '<PERSON><PERSON>m', '<PERSON><PERSON>m']

# Planet constants for swisseph
PLANET_CONSTANTS = {
    "SUN": swe.SUN,
    "MOON": swe.MOON,
    "MERCURY": swe.MERCURY,
    "VENUS": swe.VENUS,
    "MARS": swe.MARS,
    "JUPITER": swe.JUPITER,
    "SATURN": swe.SATURN,
    "URANUS": swe.URANUS,
    "NEPTUNE": swe.NEPTUNE,
    "PLUTO": swe.PLUTO,
}


def generate_random_string(length=5):
    """Generate a random string of fixed length."""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))


def parse_datetime_to_date(date_str):
    """
    Parses a datetime string and returns only the date in 'YYYY-MM-DD' format.
    Handles both AM/PM and 24-hour formats, and cases where the time part is missing.
    """
    # Ensure date_str is a string
    date_str = str(date_str).strip()

    # If no time part is provided, add a default time '00:00:00'
    if len(date_str) == 10:  # Only date part (YYYY-MM-DD)
        date_str += " 00:00:00"

    # Remove AM/PM suffix if present
    if "AM" in date_str or "PM" in date_str:
        date_str = date_str.replace(" AM", "").replace(" PM", "")

    try:
        # Parse the datetime string and return only the date part
        date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        return date_obj.date()  # Return the date only in 'YYYY-MM-DD' format
    except ValueError:
        try:
            # Try alternative format
            date_obj = datetime.strptime(date_str, "%d-%m-%Y %H:%M:%S")
            return date_obj.date()
        except ValueError:
            print(f"Error: Unable to parse the datetime string '{date_str}'")
            return None


def check_period_within_age_range(start_date_str, end_date_str, dob_date_str, start_age, end_age):
    """
    Checks if the period falls within the specified age range based on the date of birth.
    """
    # Parse the start and end dates
    start_date = parse_datetime_to_date(start_date_str)
    end_date = parse_datetime_to_date(end_date_str)

    # Parse the date of birth
    if isinstance(dob_date_str, str):
        dob_date = parse_datetime_to_date(dob_date_str)
    else:
        dob_date = dob_date_str

    # Validate parsed dates
    if not start_date or not end_date or not dob_date:
        print(f"Invalid date(s): start_date={start_date}, end_date={end_date}, dob_date={dob_date}")
        return False

    # Calculate the age at the start and end of the period
    start_age_at_period_start = (start_date - dob_date).days // 365
    end_age_at_period_end = (end_date - dob_date).days // 365

    # Check if the age is within the range at both the start and end of the period
    return start_age <= start_age_at_period_start <= end_age and start_age <= end_age_at_period_end <= end_age


def find_periods_by_ruling_planet(planetary_periods, house_data, dob, start_age=21, end_age=40):
    """
    Finds planetary periods based on the ruling planet of the house and filters by age range,
    then returns matching periods with only start and end dates, formatted as original tuples.
    The results are sorted by the start date.
    """
    # Convert date of birth to a date object
    try:
        dob_date = datetime.strptime(dob, "%Y-%m-%d").date()
    except ValueError:
        try:
            dob_date = datetime.strptime(dob, "%d-%m-%Y").date()
        except ValueError:
            print(f"Error: Unable to parse the date of birth '{dob}'")
            return [], None, None

    age_21_date = dob_date.replace(year=dob_date.year + 21)
    age_40_date = dob_date.replace(year=dob_date.year + 40)

    # List to store all matching periods
    all_matching_periods = []

    # Iterate through houses and their ruling planets
    for house in house_data:
        ruling_planet_name = house.get("ruling_planet_name", "")

        # Filter planetary periods matching the ruling planet
        matching_periods = [
            period for period in planetary_periods
            if ruling_planet_name in period[0]
               and check_period_within_age_range(
                parse_datetime_to_date(period[1]), parse_datetime_to_date(period[2]), dob_date, start_age, end_age
            )
        ]

        # Add matching periods to the final list
        all_matching_periods.extend(matching_periods)

    # Sort the matching periods by start date (period[1])
    sorted_matching_periods = sorted(
        all_matching_periods, key=lambda x: parse_datetime_to_date(x[1])
    )

    # Handle the case when the period starts before 'age_21_date' and ends after 'age_40_date'
    result = []
    for period in sorted_matching_periods:
        start_date = parse_datetime_to_date(period[1])
        end_date = parse_datetime_to_date(period[2])

        # Adjust the period to start at least from the 21st age date if the period starts before
        if start_date < age_21_date:
            start_date = age_21_date

        # Adjust the period to end at the 40th age date if the period ends after
        if end_date > age_40_date:
            end_date = age_40_date

        # Append the adjusted period to the result
        result.append((period[0], start_date.strftime('%d-%m-%Y'), end_date.strftime('%d-%m-%Y')))

    # Return the periods in the required format (name, start_date, end_date)
    return result, age_21_date, age_40_date


def get_aspect_sign(current_sign_index, aspect, total_signs=12):
    """Get the sign at a specific aspect from the current sign."""
    aspect_index = (current_sign_index + (aspect - 1)) % total_signs
    return RAASI_LIST[aspect_index]


def get_planet_sign(date, planet_name):
    """Get the sign where a planet is located on a specific date."""
    planet = PLANET_CONSTANTS.get(planet_name.upper())
    if not planet:
        raise ValueError(f"Invalid planet name: {planet_name}")

    jd = swe.julday(date.year, date.month, date.day, 0)
    swe.set_sid_mode(swe.SIDM_LAHIRI)
    planet_longitude = swe.calc_ut(jd, planet, swe.FLG_SIDEREAL)[0][0]
    planet_sign_index = int(planet_longitude // 30)
    return RAASI_LIST[planet_sign_index]


def get_planet_aspects(date, planet_name, aspects):
    """Get the signs that a planet aspects on a specific date."""
    planet = PLANET_CONSTANTS.get(planet_name.upper())
    if not planet:
        raise ValueError(f"Invalid planet name: {planet_name}")

    jd = swe.julday(date.year, date.month, date.day, 0)
    swe.set_sid_mode(swe.SIDM_LAHIRI)
    planet_longitude = swe.calc_ut(jd, planet, swe.FLG_SIDEREAL)[0][0]
    planet_sign_index = int(planet_longitude // 30)

    return {
        aspect: get_aspect_sign(planet_sign_index, aspect) for aspect in aspects
    }


def find_periods_grouped(dasha_periods, target_signs, planet_name, aspects):
    """Find grouped periods for aspects and stays."""
    grouped_results = {aspect: [] for aspect in aspects}
    grouped_results['stay'] = []  # Adding the 'stay' key for planet stays in target_signs

    for dasha, start_date_str, end_date_str in dasha_periods:
        start_date = datetime.strptime(start_date_str, '%d-%m-%Y')
        end_date = datetime.strptime(end_date_str, '%d-%m-%Y')
        current_date = start_date
        active_periods = {}
        stay_period = {"start_date": None, "end_date": None, "sign": None}

        while current_date <= end_date:
            aspects_result = get_planet_aspects(current_date, planet_name, aspects)
            planet_sign = get_planet_sign(current_date, planet_name)

            # Check for matching aspects
            for aspect, sign in aspects_result.items():
                if sign.upper() in [ts.upper() for ts in target_signs]:
                    if aspect not in active_periods:
                        active_periods[aspect] = {"start_date": current_date, "end_date": current_date}
                    else:
                        active_periods[aspect]["end_date"] = current_date
                elif aspect in active_periods:
                    grouped_results[aspect].append((
                        dasha,
                        active_periods[aspect]["start_date"].strftime('%d-%m-%Y'),
                        active_periods[aspect]["end_date"].strftime('%d-%m-%Y')
                    ))
                    del active_periods[aspect]

            # Check for stays
            if planet_sign.upper() in [ts.upper() for ts in target_signs]:
                if stay_period["start_date"] is None:
                    stay_period["start_date"] = current_date
                    stay_period["sign"] = planet_sign
                stay_period["end_date"] = current_date
            else:
                if stay_period["start_date"] is not None:
                    grouped_results['stay'].append((
                        dasha,
                        stay_period["start_date"].strftime('%d-%m-%Y'),
                        stay_period["end_date"].strftime('%d-%m-%Y'),
                        stay_period["sign"]
                    ))
                    stay_period = {"start_date": None, "end_date": None, "sign": None}

            current_date += timedelta(days=1)

        # Close active and stay periods at the end of the range
        for aspect, period in active_periods.items():
            grouped_results[aspect].append((
                dasha,
                period["start_date"].strftime('%d-%m-%Y'),
                period["end_date"].strftime('%d-%m-%Y')
            ))

        if stay_period["start_date"] is not None:
            grouped_results['stay'].append((
                dasha,
                stay_period["start_date"].strftime('%d-%m-%Y'),
                stay_period["end_date"].strftime('%d-%m-%Y'),
                stay_period["sign"]
            ))

    return grouped_results


def check_date_in_period(date_to_check, periods):
    """Check if a date falls within any of the periods."""
    try:
        date_obj = datetime.strptime(date_to_check, "%Y-%m-%d")
    except ValueError:
        try:
            date_obj = datetime.strptime(date_to_check, "%d-%m-%Y")
        except ValueError:
            print(f"Error: Unable to parse the date '{date_to_check}'")
            return False, 0

    total_count = sum(len(values) for values in periods.values())
    for key in periods.values():
        for period in key:
            start_date = datetime.strptime(period[1], "%d-%m-%Y")
            end_date = datetime.strptime(period[2], "%d-%m-%Y")
            if start_date <= date_obj <= end_date:
                return True, total_count
    return False, total_count


def handle_continuity_date(data):
    """Handle continuity in date ranges and format them for display."""
    if not data:
        return []

    # Parse data into datetime objects
    parsed_data = [
        (item[0], datetime.strptime(item[1], '%d-%m-%Y'), datetime.strptime(item[2], '%d-%m-%Y'))
        for item in data
    ]

    # Sort by start date
    parsed_data.sort(key=lambda x: x[1])

    # Generate ranges
    ranges = []
    current_start = parsed_data[0][1]
    current_end = parsed_data[0][2]

    for i in range(1, len(parsed_data)):
        _, next_start, next_end = parsed_data[i]

        # Check for continuity
        if next_start <= current_end:
            current_end = max(current_end, next_end)
        else:
            ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")
            current_start = next_start
            current_end = next_end

    # Add last period
    ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")

    return ranges


def find_houses_names(chart_data, house_numbers):
    """Find house names for specific house numbers."""
    house_names = []
    for house in chart_data.get('houses', []):
        if house.get('house_number') in house_numbers:
            house_names.append(house.get('house_name', ''))
    return house_names


def find_rasi(planet_house_data, house_name_data):
    """Find the Rasi (Moon sign) from the chart data."""
    moon_house = None
    for house_num, planets in planet_house_data.items():
        if 'MOON' in planets:
            moon_house = house_num
            break

    if moon_house and moon_house in house_name_data:
        return house_name_data[moon_house]
    return None


def remove_duplicates(data):
    """Remove duplicate entries based on ruling_planet_name."""
    seen = set()
    result = []

    for entry in data:
        ruling_planet = entry.get('ruling_planet_name')
        if ruling_planet and ruling_planet not in seen:
            seen.add(ruling_planet)
            result.append(entry)

    return result


def find_stars_by_houses(houses, house_to_planet_data, stars_data, default_planet="VENUS"):
    """Find stars associated with houses based on their ruling planets."""
    result = []
    planets_in_houses = set()

    # For each house, find its ruling planet and stars
    for house in houses:
        # Find the ruling planet for the house
        planet_row = house_to_planet_data.get(house.upper(), {})
        if planet_row:
            planet = planet_row.get("ruling_planet", "")
            # Add planet to the set to prevent duplication
            if planet not in planets_in_houses:
                planets_in_houses.add(planet)
            # Get the stars associated with the planet
            stars = stars_data.get(planet, {}).get("stars", [])
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": planet,
                "Stars": stars
            })
        else:
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": None,
                "Stars": []
            })

    # Add default planet if it's not in planets_in_houses
    if default_planet not in planets_in_houses:
        stars = stars_data.get(default_planet, {}).get("stars", [])
        result.append({
            "house_name": None,
            "ruling_planet_name": default_planet,
            "Stars": stars
        })

    return result


def get_all_stars_for_indices(stars_data, planet_dict, indices):
    """
    Retrieve stars for specified indices based on ruling planets.
    """
    result = []
    default_entry = {'house_name': None, 'ruling_planet_name': 'VENUS', 'Stars': ['BARANI', 'POORAM', 'POORADAM']}

    for idx in indices:
        planet_names = planet_dict.get(idx, None)

        if isinstance(planet_names, list):  # Check if valid list
            for planet_name in planet_names:
                # Get stars for the planet
                stars_for_planet = stars_data.get(planet_name.upper(), {}).get("stars", [])

                if stars_for_planet:  # Only process if there are matching stars
                    # Append the structured dictionary
                    result.append({
                        'house_name': f"{idx}_stay",
                        'ruling_planet_name': planet_name.upper(),
                        'Stars': stars_for_planet
                    })
                else:
                    # Append default entry if no stars are found for the planet
                    result.append(default_entry)
        else:
            # Append default entry if no valid planet names are found
            result.append(default_entry)

    # Ensure default 'VENUS' entry is present in the result
    if not any(entry.get('ruling_planet_name') == 'VENUS' for entry in result):
        result.append(default_entry)

    return result


def parse_to_tuples(input_string):
    """
    Converts a string of tuples into a list of tuples.
    """
    import re

    # Remove the surrounding brackets and split into individual entries
    input_cleaned = input_string.strip("[]")
    tuple_strings = re.findall(r"\(([^)]+)\)", input_cleaned)

    # Convert each tuple string into a Python tuple
    return [tuple(map(str.strip, entry.split(','))) for entry in tuple_strings]


def m_rule_one(placements, dob, marriage_date, user_id, chart_data=None):
    """
    Main rule function for marriage date prediction.
    This function exactly matches the original implementation in marriage_main_functions.py.

    Args:
        placements (dict): Planet placements data
        dob (str): Date of birth
        marriage_date (str): Marriage date
        user_id (int): User ID
        chart_data (dict): Chart data (optional)

    Returns:
        tuple: (rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods)
    """
    print("marriage_date: ", marriage_date)
    m_rule = {}

    # Find rasi (Moon sign)
    rasi = find_rasi(placements['planet_house'], placements['house_name'])
    print("The Rasi where the Moon is located:", rasi)

    # Get star data
    star = placements['star']

    # Always generate dasha data dynamically using vimsottari.py
    # Extract birth details
    birth_date = dob

    # Get member profile from MongoDB to get birth time and place
    member_profile = None
    if isinstance(user_id, int):
        member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"member_profile_id": user_id})
    else:
        try:
            member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"_id": ObjectId(user_id)})
        except:
            pass

    # Default values
    birth_time = '00:00:00'
    birth_place = 'Chennai, India'
    latitude = 13.0827
    longitude = 80.2707

    # Get values from member profile if available
    if member_profile:
        birth_time = member_profile.get('birth_time', birth_time)
        birth_place = member_profile.get('birth_place', birth_place)
        latitude = member_profile.get('latitude', latitude)
        longitude = member_profile.get('longitude', longitude)
    elif chart_data:
        # Use values from chart_data if member_profile is not available
        birth_time = chart_data.get('birth_time', birth_time)
        birth_place = chart_data.get('birth_place', birth_place)
        latitude = chart_data.get('latitude', latitude)
        longitude = chart_data.get('longitude', longitude)

    # Convert birth date and time to Julian day
    try:
        birth_date_obj = datetime.strptime(birth_date, '%Y-%m-%d')
    except ValueError:
        try:
            birth_date_obj = datetime.strptime(birth_date, '%d-%m-%Y')
        except ValueError:
            # Try other date formats
            try:
                birth_date_obj = datetime.strptime(birth_date, '%m/%d/%Y')
            except ValueError:
                # Default to current date if all parsing fails
                birth_date_obj = datetime.now()

    birth_time_parts = birth_time.split(':')
    hour = int(birth_time_parts[0]) if len(birth_time_parts) > 0 else 0
    minute = int(birth_time_parts[1]) if len(birth_time_parts) > 1 else 0
    second = int(birth_time_parts[2]) if len(birth_time_parts) > 2 else 0

    # Create a datetime object with the birth date and time
    birth_datetime = datetime(birth_date_obj.year, birth_date_obj.month, birth_date_obj.day, hour, minute, second)

    # Convert to Julian day
    jd = gregorian_to_jd(birth_datetime)

    # Get place details
    place_details = get_location(birth_place)
    if place_details:
        # Create place object in the format expected by vimsottari.py
        place = type('Place', (), {
            'place': birth_place,
            'latitude': place_details[1] if place_details[1] else latitude,
            'longitude': place_details[2] if place_details[2] else longitude,
            'timezone': place_details[3] if len(place_details) > 3 and place_details[3] else 5.5  # Default to IST
        })
    else:
        # Create place object in the format expected by vimsottari.py
        place = type('Place', (), {
            'place': birth_place,
            'latitude': latitude,
            'longitude': longitude,
            'timezone': 5.5  # Default to IST
        })

    # Generate dasha data
    print(f"Generating dasha data for birth date: {birth_date}, time: {birth_time}, place: {birth_place}")
    try:
        bhukti_dasha = get_vimsottari_dhasa_bhukthi(jd, place, include_antardhasa=False, include_sukshma_prana=False)

        # Format dasha data for use in prediction
        antara_dhasa_period = str(bhukti_dasha[1])  # Get only the bhukti layer
        print(f"Generated dasha data: {antara_dhasa_period}")

        # Parse the dasha data
        dh = parse_to_tuples(antara_dhasa_period)
    except Exception as e:
        print(f"Error generating dasha data: {str(e)}")
        # Use existing dasha data as fallback
        dhasa = placements.get('dhasa', {})
        antara_dhasa_period = dhasa.get('antara_dhasa_period', '[]')
        dh = parse_to_tuples(antara_dhasa_period)

    # Store user ID
    m_rule['user_id'] = user_id

    # Find houses 2 and 7 (marriage houses)
    house_names = [2, 7]  # Marriage houses
    house_names = find_houses_names(chart_data, house_names) if chart_data else []
    print("house_names: ", house_names)

    # Get house-planet mapping from MongoDB
    house_planet_mapping = {}
    try:
        house_planet_collection = mongo.db.get('house_planet_mapping', None)
        if house_planet_collection:
            mapping_doc = house_planet_collection.find_one({"_id": "mapping"})
            if mapping_doc and "mapping" in mapping_doc:
                house_planet_mapping = mapping_doc["mapping"]
            else:
                # Fallback to hardcoded data
                house_planet_mapping = {
                    'MESHAM': {'ruling_planet': 'MARS'},
                    'RISHABAM': {'ruling_planet': 'VENUS'},
                    'MIDUNAM': {'ruling_planet': 'MERCURY'},
                    'KADAGAM': {'ruling_planet': 'MOON'},
                    'SIMMAM': {'ruling_planet': 'SUN'},
                    'KANNI': {'ruling_planet': 'MERCURY'},
                    'THULAM': {'ruling_planet': 'VENUS'},
                    'VIRICHIGAM': {'ruling_planet': 'MARS'},
                    'DHANUSU': {'ruling_planet': 'JUPITER'},
                    'MAGARAM': {'ruling_planet': 'SATURN'},
                    'KUMBAM': {'ruling_planet': 'SATURN'},
                    'MEENAM': {'ruling_planet': 'JUPITER'}
                }
        else:
            # Fallback to hardcoded data
            house_planet_mapping = {
                'MESHAM': {'ruling_planet': 'MARS'},
                'RISHABAM': {'ruling_planet': 'VENUS'},
                'MIDUNAM': {'ruling_planet': 'MERCURY'},
                'KADAGAM': {'ruling_planet': 'MOON'},
                'SIMMAM': {'ruling_planet': 'SUN'},
                'KANNI': {'ruling_planet': 'MERCURY'},
                'THULAM': {'ruling_planet': 'VENUS'},
                'VIRICHIGAM': {'ruling_planet': 'MARS'},
                'DHANUSU': {'ruling_planet': 'JUPITER'},
                'MAGARAM': {'ruling_planet': 'SATURN'},
                'KUMBAM': {'ruling_planet': 'SATURN'},
                'MEENAM': {'ruling_planet': 'JUPITER'}
            }
    except Exception as e:
        print(f"Error getting house-planet mapping from MongoDB: {str(e)}")
        # Fallback to hardcoded data
        house_planet_mapping = {
            'MESHAM': {'ruling_planet': 'MARS'},
            'RISHABAM': {'ruling_planet': 'VENUS'},
            'MIDUNAM': {'ruling_planet': 'MERCURY'},
            'KADAGAM': {'ruling_planet': 'MOON'},
            'SIMMAM': {'ruling_planet': 'SUN'},
            'KANNI': {'ruling_planet': 'MERCURY'},
            'THULAM': {'ruling_planet': 'VENUS'},
            'VIRICHIGAM': {'ruling_planet': 'MARS'},
            'DHANUSU': {'ruling_planet': 'JUPITER'},
            'MAGARAM': {'ruling_planet': 'SATURN'},
            'KUMBAM': {'ruling_planet': 'SATURN'},
            'MEENAM': {'ruling_planet': 'JUPITER'}
        }

    # Get stars data from MongoDB
    stars_data = {}
    try:
        stars_collection = mongo.db.get('stars_data', None)
        if stars_collection:
            stars_doc = stars_collection.find_one({"_id": "stars"})
            if stars_doc and "data" in stars_doc:
                stars_data = stars_doc["data"]
            else:
                # Fallback to hardcoded data
                stars_data = {
                    'SUN': {'stars': ['KARTHIGAI', 'UTHIRAM', 'UTHIRADAM']},
                    'MOON': {'stars': ['ROHINI', 'HASTHAM', 'THIRUVONAM']},
                    'MARS': {'stars': ['MRIGASIRISHAM', 'CHITHIRAI', 'DHANISHTA']},
                    'MERCURY': {'stars': ['ASHLESHA', 'JYESHTA', 'REVATHI']},
                    'JUPITER': {'stars': ['PUNARVASU', 'VISAKA', 'POORATTATHI']},
                    'VENUS': {'stars': ['BHARANI', 'POORAM', 'POORADAM']},
                    'SATURN': {'stars': ['PUSHYAMI', 'ANURADHA', 'UTHIRATTATHI']},
                    'RAHU': {'stars': ['ARUDRA', 'SWATHI', 'SATHABISHAM']},
                    'KETU': {'stars': ['ASWINI', 'MAGAM', 'MOOLA']}
                }
        else:
            # Fallback to hardcoded data
            stars_data = {
                'SUN': {'stars': ['KARTHIGAI', 'UTHIRAM', 'UTHIRADAM']},
                'MOON': {'stars': ['ROHINI', 'HASTHAM', 'THIRUVONAM']},
                'MARS': {'stars': ['MRIGASIRISHAM', 'CHITHIRAI', 'DHANISHTA']},
                'MERCURY': {'stars': ['ASHLESHA', 'JYESHTA', 'REVATHI']},
                'JUPITER': {'stars': ['PUNARVASU', 'VISAKA', 'POORATTATHI']},
                'VENUS': {'stars': ['BHARANI', 'POORAM', 'POORADAM']},
                'SATURN': {'stars': ['PUSHYAMI', 'ANURADHA', 'UTHIRATTATHI']},
                'RAHU': {'stars': ['ARUDRA', 'SWATHI', 'SATHABISHAM']},
                'KETU': {'stars': ['ASWINI', 'MAGAM', 'MOOLA']}
            }
    except Exception as e:
        print(f"Error getting stars data from MongoDB: {str(e)}")
        # Fallback to hardcoded data
        stars_data = {
            'SUN': {'stars': ['KARTHIGAI', 'UTHIRAM', 'UTHIRADAM']},
            'MOON': {'stars': ['ROHINI', 'HASTHAM', 'THIRUVONAM']},
            'MARS': {'stars': ['MRIGASIRISHAM', 'CHITHIRAI', 'DHANISHTA']},
            'MERCURY': {'stars': ['ASHLESHA', 'JYESHTA', 'REVATHI']},
            'JUPITER': {'stars': ['PUNARVASU', 'VISAKA', 'POORATTATHI']},
            'VENUS': {'stars': ['BHARANI', 'POORAM', 'POORADAM']},
            'SATURN': {'stars': ['PUSHYAMI', 'ANURADHA', 'UTHIRATTATHI']},
            'RAHU': {'stars': ['ARUDRA', 'SWATHI', 'SATHABISHAM']},
            'KETU': {'stars': ['ASWINI', 'MAGAM', 'MOOLA']}
        }

    # Find stars by houses
    result_11 = find_stars_by_houses(house_names, house_planet_mapping, stars_data, "VENUS")
    result_11 = remove_duplicates(result_11)
    print("rule_1.1 data: ", result_11)

    # Find periods by ruling planet
    matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_11, dob)
    print(matching_periods_1)

    # Check if marriage date is in the predicted ranges
    is_in_range, count = is_date_in_ranges(marriage_date, matching_periods_1)
    m_rule['Rule 1.1'] = is_in_range
    m_rule['Rule 1.1_count'] = count
    print(count)

    # Find stars in data
    result_12 = find_star_in_data(star, result_11)
    print("result_12 data: ", result_12)
    result_12 = remove_duplicates(result_12)
    print("result_12 data: ", result_12)

    # Find periods for result_12
    matching_periods_2, _, _ = find_periods_by_ruling_planet(dh, result_12, dob)
    is_in_range, count = is_date_in_ranges(marriage_date, matching_periods_2)
    m_rule['Rule 1.2'] = is_in_range
    m_rule['Rule 1.2_count'] = count

    # Combine results
    result_12.extend(result_11)

    # Get planet positions for houses 2 and 7
    planet_house_mapping = {}
    for house_num in [2, 7]:
        if str(house_num) in placements['planet_house']:
            planet_house_mapping[house_num] = placements['planet_house'][str(house_num)].split('/')

    # Find stars for houses 2 and 7
    result_13 = get_all_stars_for_indices(stars_data, planet_house_mapping, [2, 7])
    result_13 = remove_duplicates(result_13)
    print(result_13)

    # Find periods for result_13
    matching_periods_3, _, _ = find_periods_by_ruling_planet(dh, result_13, dob)
    is_in_range, count = is_date_in_ranges(marriage_date, matching_periods_3)
    m_rule['Rule 1.3'] = is_in_range
    m_rule['Rule 1.3_count'] = count

    # Convert to DataFrame for easier handling
    import pandas as pd
    df = pd.DataFrame(matching_periods_1, columns=['Period', 'Start Date', 'End Date'])
    df_cleaned = df.drop_duplicates()
    cleaned_data = list(df_cleaned.itertuples(index=False, name=None))

    print(f"21st Age Date: {age_21_date.strftime('%d-%m-%Y')}")
    print(f"40th Age Date: {age_40_date.strftime('%d-%m-%Y')}")

    # Find grouped aspect periods
    grouped_aspect_periods_11 = find_periods_grouped(cleaned_data, house_names, "JUPITER", [5, 7, 9])
    grouped_aspect_periods_12 = find_periods_grouped(cleaned_data, rasi, "JUPITER", [5, 7, 9])
    grouped_aspect_periods_13 = find_periods_grouped(cleaned_data, house_names, "VENUS", [7])
    grouped_aspect_periods_14 = find_periods_grouped(cleaned_data, rasi, "VENUS", [7])

    # Check if marriage date is in the aspect periods
    m_rule['marriage_dasa_jup_asp_27'], m_rule['marriage_dasa_jup_asp_27_count'] = check_date_in_period(marriage_date, grouped_aspect_periods_11)
    m_rule['marriage_das_jup_asp_ras'], m_rule['marriage_das_jup_asp_ras_count'] = check_date_in_period(marriage_date, grouped_aspect_periods_12)
    m_rule['marriage_das_ven_in_7'], m_rule['marriage_das_ven_in_7_count'] = check_date_in_period(marriage_date, grouped_aspect_periods_13)
    m_rule['marriage_das_ven_in_ras'], m_rule['marriage_das_ven_in_ras_count'] = check_date_in_period(marriage_date, grouped_aspect_periods_14)

    # Check planets in periods
    mk = check_planets_in_periods(cleaned_data, result_13)
    print(mk)
    filter_results = check_date_in_periods(mk, marriage_date)

    # Output the result and filter check
    print("Filter Results:", filter_results)

    # Combine results
    combined = {**m_rule, **filter_results}

    # Print all matching periods
    for i in matching_periods_1:
        print(i)

    # Format the results
    result_data = handle_continuity_date(matching_periods_1)
    print(result_data)

    return combined, grouped_aspect_periods_11, grouped_aspect_periods_12, grouped_aspect_periods_13, grouped_aspect_periods_14, result_data


def find_star_in_data(star, result_11):
    """Find stars in data."""
    result = []
    for entry in result_11:
        stars = entry.get('Stars', [])
        for s in stars:
            for star_key, star_value in star.items():
                if s.upper() == star_value.upper():
                    result.append(entry)
                    break
    return result


def check_planets_in_periods(cleaned_data, result_13):
    """Check planets in periods."""
    mk = {}
    for period in cleaned_data:
        for entry in result_13:
            if entry.get('ruling_planet_name') in period[0]:
                if period[0] not in mk:
                    mk[period[0]] = []
                mk[period[0]].append((period[1], period[2]))
    return mk


def check_date_in_periods(mk, marriage_date):
    """Check if a date falls within any of the periods."""
    filter_results = {}
    try:
        date_obj = datetime.strptime(marriage_date, "%Y-%m-%d")
    except ValueError:
        try:
            date_obj = datetime.strptime(marriage_date, "%d-%m-%Y")
        except ValueError:
            print(f"Error: Unable to parse the date '{marriage_date}'")
            return filter_results

    for key, periods in mk.items():
        filter_results[f"marriage_dasa_{key.lower()}"] = False
        filter_results[f"marriage_dasa_{key.lower()}_count"] = len(periods)
        for start_date_str, end_date_str in periods:
            start_date = datetime.strptime(start_date_str, "%d-%m-%Y")
            end_date = datetime.strptime(end_date_str, "%d-%m-%Y")
            if start_date <= date_obj <= end_date:
                filter_results[f"marriage_dasa_{key.lower()}"] = True
                break

    return filter_results


def is_date_in_ranges(date_to_check, date_ranges):
    """
    Check if the given date is within any of the specified date ranges.

    Args:
        date_to_check (str): The date to check in 'YYYY-MM-DD' format
        date_ranges (list): A list of tuples, each containing a period name and start and end dates

    Returns:
        tuple: (is_in_range, count) - Boolean indicating if date is in range and count of ranges
    """
    try:
        # Convert date_to_check to datetime object
        date_obj = datetime.strptime(date_to_check, "%Y-%m-%d")
    except ValueError:
        try:
            date_obj = datetime.strptime(date_to_check, "%d-%m-%Y")
        except ValueError:
            print(f"Error: Unable to parse the date '{date_to_check}'")
            return False, 0

    for period_name, start_date, end_date in date_ranges:
        # Convert start and end dates to datetime objects
        start_date_obj = datetime.strptime(start_date, "%d-%m-%Y")
        end_date_obj = datetime.strptime(end_date, "%d-%m-%Y")

        # Check if the date is within the range
        if start_date_obj <= date_obj <= end_date_obj:
            return True, len(date_ranges)

    return False, len(date_ranges)


def print_detailed_output(rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods):
    """
    Print detailed output of marriage date prediction results in the same format as the original code.

    Args:
        rule_results (dict): Rule results
        jupiter_aspects_houses (dict): Jupiter aspects to houses
        jupiter_aspects_rasi (dict): Jupiter aspects to rasi
        venus_aspects_houses (dict): Venus aspects to houses
        venus_aspects_rasi (dict): Venus aspects to rasi
        potential_periods (list): Potential marriage periods
    """
    print(rule_results)
    print("Matching Dasha Periods:")
    print("\nrule 1.1\n")
    print("Jupiter 5th Aspect Periods:")
    for period in jupiter_aspects_houses[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in jupiter_aspects_houses[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in jupiter_aspects_houses[9]:
        print(period)

    print("\nJupiter Staying with 2th,7th house Periods:")
    for period in jupiter_aspects_houses['stay']:
        print(period)

    print("\nrule 1.2\n")
    print("Jupiter 5th Aspect Periods:")
    for period in jupiter_aspects_rasi[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in jupiter_aspects_rasi[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in jupiter_aspects_rasi[9]:
        print(period)

    print("\nJupiter Staying with rasi Periods:")
    for period in jupiter_aspects_rasi['stay']:
        print(period)

    print("\nrule 1.3\n")

    print("\nVenus 7th Aspect Periods:")
    for period in venus_aspects_houses[7]:
        print(period)
    print("\nVenus Staying with 7th house Periods:")
    for period in venus_aspects_houses['stay']:
        print(period)

    print("\nrule 1.4\n")

    print("\nVenus 7th Aspect Periods:")
    for period in venus_aspects_rasi[7]:
        print(period)

    print("\nVenus Staying with rasi Periods:")
    for period in venus_aspects_rasi['stay']:
        print(period)

    print("\nCustomer Output")
    for r in potential_periods:
        print(r)


def predict_marriage_dates(member_id, start_age=21, end_age=40, marriage_date=None, print_output=False):
    """
    Predict potential marriage dates for a member based on astrological factors.

    Args:
        member_id (str or int): Member's ID
        start_age (int): Starting age for prediction (default: 21)
        end_age (int): Ending age for prediction (default: 40)
        marriage_date (str): Optional marriage date for validation
        print_output (bool): Whether to print detailed output (default: False)

    Returns:
        dict: Marriage date prediction results
    """
    try:
        # Get member profile
        if isinstance(member_id, int):
            member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"member_profile_id": member_id})
        else:
            member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({"_id": ObjectId(member_id)})

        if not member_profile:
            return {
                'success': False,
                'message': f'Member profile with ID {member_id} not found'
            }

        # Get astrological data
        member_astro_data = get_member_astro_data(member_id)
        if not member_astro_data:
            return {
                'success': False,
                'message': 'Astrological data not found for the member'
            }

        # Extract D1 chart (Rasi chart)
        d1_chart = member_astro_data.get('chart_data', {}).get('D1', {})
        if not d1_chart:
            return {
                'success': False,
                'message': 'D1 chart data not found for the member'
            }

        # Extract planet positions and house names
        planet_positions = extract_planet_positions(d1_chart)
        house_names = extract_house_names(d1_chart)
        nakshatra_data = extract_nakshatra_data(d1_chart)
        dasha_data = member_astro_data.get('chart_data', {}).get('dashas', {})

        # Get birth date
        birth_date = member_profile.get('birth_date')
        if not birth_date:
            return {
                'success': False,
                'message': 'Birth date not found for the member'
            }

        # Use provided marriage date or set to None
        marriage_date = marriage_date or None

        # Create placements object to match the original code structure
        placements = {
            'planet_house': planet_positions,
            'house_name': house_names,
            'star': nakshatra_data,
            'dhasa': dasha_data
        }

        # Add birth time and place information to d1_chart for dasha calculation
        d1_chart['birth_time'] = member_profile.get('birth_time', '00:00:00')
        d1_chart['birth_place'] = member_profile.get('birth_place', 'Chennai, India')
        d1_chart['latitude'] = member_profile.get('latitude', 13.0827)
        d1_chart['longitude'] = member_profile.get('longitude', 80.2707)

        # Call the m_rule_one function to get the same output as the original code
        rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods = m_rule_one(
            placements, birth_date, marriage_date, member_id, d1_chart
        )

        # Print detailed output if requested
        if print_output:
            print_detailed_output(rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods)

        # Prepare the response
        response = {
            'success': True,
            'member': {
                'id': str(member_profile['_id']),
                'name': member_profile.get('name', ''),
                'birth_date': birth_date
            },
            'rule_results': rule_results,
            'potential_marriage_periods': potential_periods,
            'jupiter_aspects': {
                'houses': {
                    '5th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_houses[5]],
                    '7th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_houses[7]],
                    '9th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_houses[9]],
                    'stay': [{'period': p[0], 'start_date': p[1], 'end_date': p[2], 'sign': p[3]} for p in jupiter_aspects_houses['stay']]
                },
                'rasi': {
                    '5th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_rasi[5]],
                    '7th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_rasi[7]],
                    '9th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in jupiter_aspects_rasi[9]],
                    'stay': [{'period': p[0], 'start_date': p[1], 'end_date': p[2], 'sign': p[3]} for p in jupiter_aspects_rasi['stay']]
                }
            },
            'venus_aspects': {
                'houses': {
                    '7th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in venus_aspects_houses[7]],
                    'stay': [{'period': p[0], 'start_date': p[1], 'end_date': p[2], 'sign': p[3]} for p in venus_aspects_houses['stay']]
                },
                'rasi': {
                    '7th_aspect': [{'period': p[0], 'start_date': p[1], 'end_date': p[2]} for p in venus_aspects_rasi[7]],
                    'stay': [{'period': p[0], 'start_date': p[1], 'end_date': p[2], 'sign': p[3]} for p in venus_aspects_rasi['stay']]
                }
            },
            'raw_output': {
                'rule_results': rule_results,
                'potential_periods': potential_periods
            },
            'generated_at': datetime.utcnow().isoformat()
        }

        return response

    except Exception as e:
        import traceback
        print(f"Error predicting marriage dates: {str(e)}")
        print(traceback.format_exc())
        return {
            'success': False,
            'message': f'Error predicting marriage dates: {str(e)}'
        }


if __name__ == "__main__":
    # This code will run when the file is executed directly
    import sys
    import json
    import pymongo
    from bson import ObjectId
    from datetime import datetime

    # Connect directly to MongoDB
    client = pymongo.MongoClient("mongodb://localhost:27017/")
    db = client["fortune_lens"]

    # Find the member profile
    member_profile = db["member_profile"].find_one({"user_profile_id": 1, "member_profile_id": 1})

    if not member_profile:
        print("Member profile not found for user_profile_id=1 and member_profile_id=1")
        sys.exit(1)

    member_id = member_profile["_id"]
    print(f"Found member profile with ID: {member_id}")

    # Get astro data
    astro_data = db["user_member_astro_profile_data"].find_one({"member_profile_id": member_id})
    if not astro_data:
        print("Astro data not found for the member")
        sys.exit(1)

    print(f"Found astro data with ID: {astro_data['_id']}")

    # Extract D1 chart (Rasi chart)
    d1_chart = astro_data.get('chart_data', {}).get('D1', {})
    if not d1_chart:
        print("D1 chart data not found for the member")
        sys.exit(1)

    print("D1 chart data found")

    # Extract planet positions and house names
    planet_positions = {}
    house_names = {}
    nakshatra_data = {}

    # Extract from houses data
    houses = d1_chart.get('houses', [])
    for house in houses:
        house_number = house.get('house_number')
        house_name = house.get('house_name', '').upper()
        planets = house.get('planets', [])
        planet_nakshatras = house.get('planet_nakshatras', {})

        if house_number and planets:
            planet_positions[str(house_number)] = ', '.join(planets)

        if house_number and house_name:
            house_names[f'house_name_{house_number}'] = house_name

        for planet in planets:
            if planet in planet_nakshatras:
                nakshatra_data[f'{planet}_star'] = planet_nakshatras[planet]

    # Add lagna position
    lagna_house = d1_chart.get('lagna', {}).get('house_number')
    if lagna_house:
        if str(lagna_house) in planet_positions:
            planet_positions[str(lagna_house)] = f"lagnam, {planet_positions[str(lagna_house)]}"
        else:
            planet_positions[str(lagna_house)] = "lagnam"

    # Get dasha data
    dasha_data = astro_data.get('chart_data', {}).get('dashas', {})

    # Get birth date
    birth_date = member_profile.get('birth_date')
    if not birth_date:
        print("Birth date not found for the member")
        sys.exit(1)

    print(f"Birth date: {birth_date}")

    # Create placements object
    placements = {
        'planet_house': planet_positions,
        'house_name': house_names,
        'star': nakshatra_data,
        'dhasa': dasha_data
    }

    # Print planet positions
    print("\nPlanet positions:")
    for house_num, planets in planet_positions.items():
        print(f"House {house_num}: {planets}")

    # Print house names
    print("\nHouse names:")
    for key, name in house_names.items():
        print(f"{key}: {name}")

    # Print nakshatra data
    print("\nNakshatra data:")
    for planet, star in nakshatra_data.items():
        print(f"{planet}: {star}")

    # Call m_rule_one directly
    print("\nCalling m_rule_one function...")
    marriage_date = '2023-05-15'

    try:
        rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods = m_rule_one(
            placements, birth_date, marriage_date, str(member_id), d1_chart
        )

        # Print the detailed output
        print("\nDetailed Output:")
        print(rule_results)
        print("Matching Dasha Periods:")
        print("\nrule 1.1\n")
        print("Jupiter 5th Aspect Periods:")
        for period in jupiter_aspects_houses[5]:
            print(period)

        print("\nJupiter 7th Aspect Periods:")
        for period in jupiter_aspects_houses[7]:
            print(period)

        print("\nJupiter 9th Aspect Periods:")
        for period in jupiter_aspects_houses[9]:
            print(period)

        print("\nJupiter Staying with 2th,7th house Periods:")
        for period in jupiter_aspects_houses['stay']:
            print(period)

        print("\nrule 1.2\n")
        print("Jupiter 5th Aspect Periods:")
        for period in jupiter_aspects_rasi[5]:
            print(period)

        print("\nJupiter 7th Aspect Periods:")
        for period in jupiter_aspects_rasi[7]:
            print(period)

        print("\nJupiter 9th Aspect Periods:")
        for period in jupiter_aspects_rasi[9]:
            print(period)

        print("\nJupiter Staying with rasi Periods:")
        for period in jupiter_aspects_rasi['stay']:
            print(period)

        print("\nrule 1.3\n")

        print("\nVenus 7th Aspect Periods:")
        for period in venus_aspects_houses[7]:
            print(period)
        print("\nVenus Staying with 7th house Periods:")
        for period in venus_aspects_houses['stay']:
            print(period)

        print("\nrule 1.4\n")

        print("\nVenus 7th Aspect Periods:")
        for period in venus_aspects_rasi[7]:
            print(period)

        print("\nVenus Staying with rasi Periods:")
        for period in venus_aspects_rasi['stay']:
            print(period)

        print("\nCustomer Output")
        for r in potential_periods:
            print(r)
    except Exception as e:
        import traceback
        print(f"Error calling m_rule_one: {str(e)}")
        print(traceback.format_exc())
