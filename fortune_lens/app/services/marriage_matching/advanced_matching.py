"""
Advanced Marriage Matching

This module provides advanced marriage compatibility analysis based on Vedic astrology.
It combines both <PERSON><PERSON> (Ascendant) and <PERSON><PERSON> (Moon Sign) based matching models.
"""

from .lagna_matching import analyze_lagna_marriage_compatibility


def analyze_advanced_marriage_compatibility(bride_id, groom_id):
    """
    Analyze marriage compatibility between two members using advanced methods.
    
    This function combines multiple matching techniques for a comprehensive analysis.
    
    Args:
        bride_id (str): Bride's member ID
        groom_id (str): Groom's member ID
        
    Returns:
        dict: Compatibility analysis results
    """
    # For now, we'll just use the Lagna-based matching
    # In the future, this could combine multiple matching techniques
    return analyze_lagna_marriage_compatibility(bride_id, groom_id)
