"""
Planetary relationships in Vedic astrology.
This module contains constants and functions related to planetary relationships,
including exaltation, debilitation, friendship, enmity, and aspects.
"""

# Planetary Exaltation and Debilitation
EXALTATION = {
    'SUN': 'MESHAM',
    'MOON': 'RISHABAM',
    'MARS': 'MAGARA<PERSON>',
    'MERCURY': '<PERSON><PERSON><PERSON>',
    'JUPITER': 'KADAGAM',
    'VENUS': 'MEENAM',
    'SATURN': 'THULAM',
    'RAHU': 'DHANUSU',
    'KETU': 'MIDUNAM'
}

DEBILITATION = {
    'SUN': 'THULAM',
    'MOON': 'VIRICHIGAM',
    'MARS': 'KADAGAM',
    'MERCURY': 'MEENAM',
    'JUPITER': 'MAGARAM',
    'VENUS': 'KANNI',
    'SATURN': 'MESHAM',
    'RAHU': 'MIDUNAM',
    'KETU': 'DHANUSU'
}

# Planetary Relationships
FRIENDS = {
    'SUN': ['MOON', 'MARS', 'J<PERSON>ITER'],
    'MOON': ['SUN', 'MERCURY'],
    'MARS': ['SUN', 'MO<PERSON>', '<PERSON><PERSON><PERSON><PERSON>'],
    'MERCURY': ['SUN', 'VENUS'],
    'JUPITER': ['SUN', 'MOON', 'MARS'],
    'VENUS': ['SATURN', 'MERCURY'],
    'SATURN': ['MERCURY', 'VENUS'],
    'RAHU': ['VENUS', 'SATURN'],
    'KETU': ['VENUS', 'SATURN']
}

ENEMIES = {
    'SUN': ['SATURN', 'VENUS'],
    'MOON': [],
    'MARS': ['MERCURY'],
    'MERCURY': ['MOON'],
    'JUPITER': ['VENUS', 'MERCURY'],
    'VENUS': ['SUN', 'MOON', 'MARS', 'JUPITER', 'RAHU', 'KETU'],
    'SATURN': ['SUN', 'MOON', 'MARS', 'JUPITER', 'RAHU', 'KETU'],
    'RAHU': ['SUN', 'MOON', 'MARS'],
    'KETU': ['SUN', 'MOON', 'MARS']
}

NEUTRAL = {
    'SUN': ['MERCURY'],
    'MOON': ['MARS', 'JUPITER', 'VENUS', 'SATURN', 'RAHU', 'KETU'],
    'MARS': ['VENUS', 'SATURN', 'RAHU', 'KETU'],
    'MERCURY': ['VENUS', 'SATURN'],
    'JUPITER': ['SATURN'],
    'VENUS': ['JUPITER', 'MARS'],
    'SATURN': ['JUPITER'],
    'RAHU': ['JUPITER', 'MERCURY'],
    'KETU': ['JUPITER', 'MERCURY']
}

# Planetary Aspects
ASPECTS = {
    'SUN': [7],
    'MOON': [7],
    'MERCURY': [7],
    'VENUS': [7],
    'SATURN': [3, 7, 10],
    'MARS': [4, 7, 8],
    'JUPITER': [5, 7, 9],
    'RAHU': [5, 7, 9],  # Same as Jupiter
    'KETU': [5, 7, 9]   # Same as Jupiter
}

def is_planet_exalted(planet, sign):
    """
    Check if a planet is exalted in a given sign.
    
    Args:
        planet (str): The planet name
        sign (str): The sign name
        
    Returns:
        bool: True if the planet is exalted in the sign, False otherwise
    """
    return EXALTATION.get(planet.upper()) == sign.upper()

def is_planet_debilitated(planet, sign):
    """
    Check if a planet is debilitated in a given sign.
    
    Args:
        planet (str): The planet name
        sign (str): The sign name
        
    Returns:
        bool: True if the planet is debilitated in the sign, False otherwise
    """
    return DEBILITATION.get(planet.upper()) == sign.upper()

def get_planet_relationship(planet1, planet2):
    """
    Get the relationship between two planets.
    
    Args:
        planet1 (str): The first planet name
        planet2 (str): The second planet name
        
    Returns:
        str: 'FRIEND', 'ENEMY', or 'NEUTRAL'
    """
    planet1 = planet1.upper()
    planet2 = planet2.upper()
    
    if planet2 in FRIENDS.get(planet1, []):
        return 'FRIEND'
    elif planet2 in ENEMIES.get(planet1, []):
        return 'ENEMY'
    else:
        return 'NEUTRAL'

def get_planet_aspects(planet):
    """
    Get the houses that a planet aspects.
    
    Args:
        planet (str): The planet name
        
    Returns:
        list: List of houses that the planet aspects
    """
    return ASPECTS.get(planet.upper(), [])

def is_planet_aspecting_house(planet, planet_house, target_house):
    """
    Check if a planet is aspecting a specific house.
    
    Args:
        planet (str): The planet name
        planet_house (int): The house where the planet is placed
        target_house (int): The house to check if it's being aspected
        
    Returns:
        bool: True if the planet is aspecting the target house, False otherwise
    """
    aspects = get_planet_aspects(planet)
    for aspect in aspects:
        aspected_house = (planet_house + aspect - 1) % 12 + 1
        if aspected_house == target_house:
            return True
    return False

def is_planet_aspecting_planet(planet1, planet1_house, planet2, planet2_house):
    """
    Check if a planet is aspecting another planet.
    
    Args:
        planet1 (str): The first planet name
        planet1_house (int): The house where the first planet is placed
        planet2 (str): The second planet name
        planet2_house (int): The house where the second planet is placed
        
    Returns:
        bool: True if planet1 is aspecting planet2, False otherwise
    """
    return is_planet_aspecting_house(planet1, planet1_house, planet2_house)
