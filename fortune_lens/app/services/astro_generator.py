"""
Astrological Data Generator
"""

import random
from datetime import datetime


def generate_astro_data(profile):
    """
    Generate astrological data based on profile information
    
    Args:
        profile (dict): Member profile data
        
    Returns:
        dict: Generated astrological data
    """
    # Extract birth information
    birth_date = profile.get('birth_date')
    birth_time = profile.get('birth_time')
    birth_place = profile.get('birth_place')
    latitude = profile.get('latitude')
    longitude = profile.get('longitude')
    
    # Initialize astro data
    astro_data = {
        'generated_at': datetime.utcnow(),
        'birth_details': {
            'date': birth_date,
            'time': birth_time,
            'place': birth_place,
            'latitude': latitude,
            'longitude': longitude
        }
    }
    
    # Generate zodiac sign based on birth date
    if birth_date:
        try:
            if isinstance(birth_date, str):
                birth_date = datetime.strptime(birth_date, '%Y-%m-%d')
            
            month = birth_date.month
            day = birth_date.day
            
            # Determine zodiac sign
            if (month == 3 and day >= 21) or (month == 4 and day <= 19):
                zodiac = "Aries"
            elif (month == 4 and day >= 20) or (month == 5 and day <= 20):
                zodiac = "Taurus"
            elif (month == 5 and day >= 21) or (month == 6 and day <= 20):
                zodiac = "Gemini"
            elif (month == 6 and day >= 21) or (month == 7 and day <= 22):
                zodiac = "Cancer"
            elif (month == 7 and day >= 23) or (month == 8 and day <= 22):
                zodiac = "Leo"
            elif (month == 8 and day >= 23) or (month == 9 and day <= 22):
                zodiac = "Virgo"
            elif (month == 9 and day >= 23) or (month == 10 and day <= 22):
                zodiac = "Libra"
            elif (month == 10 and day >= 23) or (month == 11 and day <= 21):
                zodiac = "Scorpio"
            elif (month == 11 and day >= 22) or (month == 12 and day <= 21):
                zodiac = "Sagittarius"
            elif (month == 12 and day >= 22) or (month == 1 and day <= 19):
                zodiac = "Capricorn"
            elif (month == 1 and day >= 20) or (month == 2 and day <= 18):
                zodiac = "Aquarius"
            else:
                zodiac = "Pisces"
            
            astro_data['zodiac_sign'] = zodiac
        except:
            astro_data['zodiac_sign'] = None
    
    # Generate planetary positions
    planets = ["Sun", "Moon", "Mercury", "Venus", "Mars", "Jupiter", "Saturn", "Uranus", "Neptune", "Pluto"]
    houses = list(range(1, 13))  # 12 houses
    signs = ["Aries", "Taurus", "Gemini", "Cancer", "Leo", "Virgo", "Libra", "Scorpio", "Sagittarius", "Capricorn", "Aquarius", "Pisces"]
    
    planetary_positions = {}
    for planet in planets:
        planetary_positions[planet] = {
            'sign': random.choice(signs),
            'house': random.choice(houses),
            'degree': round(random.uniform(0, 29.99), 2)
        }
    
    astro_data['planetary_positions'] = planetary_positions
    
    # Generate ascendant (rising sign)
    astro_data['ascendant'] = {
        'sign': random.choice(signs),
        'degree': round(random.uniform(0, 29.99), 2)
    }
    
    # Generate aspects
    aspects = []
    aspect_types = ["Conjunction", "Opposition", "Trine", "Square", "Sextile"]
    
    # Generate some random aspects between planets
    for i in range(15):  # Generate 15 random aspects
        planet1 = random.choice(planets)
        planet2 = random.choice([p for p in planets if p != planet1])
        aspect_type = random.choice(aspect_types)
        orb = round(random.uniform(0, 8), 2)  # Orb in degrees
        
        aspects.append({
            'planet1': planet1,
            'planet2': planet2,
            'aspect': aspect_type,
            'orb': orb
        })
    
    astro_data['aspects'] = aspects
    
    # Generate houses
    house_data = {}
    for i in range(1, 13):
        house_data[str(i)] = {
            'sign': random.choice(signs),
            'degree_start': round(random.uniform(0, 29.99), 2)
        }
    
    astro_data['houses'] = house_data
    
    return astro_data
