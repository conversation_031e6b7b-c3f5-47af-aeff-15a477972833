"""
Authentication Service
"""

from ..extensions import mongo, bcrypt
from .user_service import UserService


class AuthService:
    """Authentication Service"""
    
    @staticmethod
    def authenticate_user(email, password):
        """
        Authenticate user with email and password
        
        Args:
            email (str): User email
            password (str): User password
            
        Returns:
            dict: User document if authentication successful, None otherwise
        """
        user = UserService.get_user_by_email(email)
        
        if user and bcrypt.check_password_hash(user['password'], password):
            return user
        
        return None
