"""
Sequence Generator Service
"""

from ..extensions import mongo
from ..config import BaseConfig
from bson import ObjectId


class SequenceService:
    """Service for generating sequential IDs"""

    @staticmethod
    def get_next_sequence(sequence_name):
        """
        Get the next value in a sequence

        Args:
            sequence_name (str): Name of the sequence

        Returns:
            int: Next value in the sequence
        """
        # Create sequences collection if it doesn't exist
        if 'sequences' not in mongo.db.list_collection_names():
            mongo.db.create_collection('sequences')

        # Get the next sequence value
        sequence = mongo.db.sequences.find_one_and_update(
            {'_id': sequence_name},
            {'$inc': {'seq': 1}},
            upsert=True,
            return_document=True
        )

        # If sequence is newly created, initialize it
        if 'seq' not in sequence:
            mongo.db.sequences.update_one(
                {'_id': sequence_name},
                {'$set': {'seq': 1}}
            )
            return 1

        return sequence['seq']

    @staticmethod
    def get_next_user_id():
        """
        Get the next user ID

        Returns:
            ObjectId: Next user ID as ObjectId
        """
        return ObjectId()

    @staticmethod
    def get_next_member_id():
        """
        Get the next member ID

        Returns:
            ObjectId: Next member ID as ObjectId
        """
        return ObjectId()

    @staticmethod
    def get_next_astro_id():
        """
        Get the next astro ID

        Returns:
            ObjectId: Next astro ID as ObjectId
        """
        return ObjectId()
