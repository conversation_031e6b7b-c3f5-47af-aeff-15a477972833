"""
Astronomy Utility Functions

This module provides functions for astronomical calculations,
including planet positions, house calculations, and other
astrological utilities.
"""

import swisseph as swe
import numpy as np

from app.services import const


def set_ephemeris_data_path(data_path=const._ephe_path):
    """
    Set the path to the ephemeris data files.
    
    Args:
        data_path (str): Path to ephemeris data files
    """
    swe.set_ephe_path(data_path)


def get_house_to_planet_dict_from_planet_to_house_dict(planet_to_house_dict):
    """
    Convert planet-to-house dictionary to house-to-planet list.
    
    Args:
        planet_to_house_dict (dict): Dictionary mapping planet IDs to house numbers
            Format: {planet_id: house_number, ...}
            Example: {0: 0, 1: 1, 2: 1, ...} (Sun in Aries, Moon in Taurus, Mars in Taurus, etc.)
            
    Returns:
        list: List mapping houses to planets
            Format: ['0', '1/2', ...] (<PERSON><PERSON> has Sun, Taurus has Moon/Mars, etc.)
    """
    h_to_p = ['' for h in range(12)]
    for p, h in planet_to_house_dict.items():
        h_to_p[h] += str(p) + '/'
    h_to_p = [p[:-1] for p in h_to_p]
    return h_to_p


def get_planet_to_house_dict_from_chart(house_to_planet_list):
    """
    Convert house-to-planet list to planet-to-house dictionary.
    
    Args:
        house_to_planet_list (list): List mapping houses to planets
            Format: ['0', '1/2', ...] (Aries has Sun, Taurus has Moon/Mars, etc.)
            'L' is used for Lagna (Ascendant)
            
    Returns:
        dict: Dictionary mapping planet IDs to house numbers
            Format: {planet_id: house_number, ...}
            Example: {0: 0, 1: 1, 2: 1, ...} (Sun in Aries, Moon in Taurus, Mars in Taurus, etc.)
            'L' is included for Lagna (Ascendant)
    """
    p_to_h = {p: h for p in [*range(9)] + [const._ascendant_symbol] for h, planets in enumerate(house_to_planet_list) if
              str(p) in planets}
    return p_to_h


def get_planet_house_dictionary_from_planet_positions(planet_positions):
    """
    Convert planet positions to planet-to-house dictionary.
    
    Args:
        planet_positions (list): List of planet positions
            Format: [(planet_index, (house_index, planet_longitude)), ...]
            
    Returns:
        dict: Dictionary mapping planet IDs to house numbers
            Format: {planet_id: house_number, ...}
    """
    p_to_h = {p: h for p, (h, _) in planet_positions}
    return p_to_h


def get_house_planet_list_from_planet_positions(planet_positions):
    """
    Convert planet positions to house-to-planet list.
    
    Args:
        planet_positions (list): List of planet positions
            Format: [(planet_index, (house_index, planet_longitude)), ...]
            
    Returns:
        list: List mapping houses to planets
            Format: ['0', '1/2', ...] (Aries has Sun, Taurus has Moon/Mars, etc.)
    """
    h_to_p = ['' for h in range(12)]
    for sublist in planet_positions:
        p = sublist[0]
        h = sublist[1][0]
        h_to_p[h] += str(p) + '/'
    h_to_p = [x[:-1] for x in h_to_p]
    return h_to_p


def deeptaamsa_range_of_planet(planet, planet_longitude_within_raasi):
    """
    Get deeptaamsa range of a planet.
    
    Args:
        planet (int): Planet index (0 for Sun, 1 for Moon, etc.)
        planet_longitude_within_raasi (float): Longitude of the planet within the raasi (0.0 to 30.0 degrees)
        
    Returns:
        tuple: (deeptaamsa_minimum, deeptaamsa_maximum)
    """
    return (planet_longitude_within_raasi - const.deeptaamsa_of_planets[planet],
            const.deeptaamsa_of_planets[planet] + planet_longitude_within_raasi)


def norm180(angle):
    """
    Normalize angle to range [-180, 180).
    
    Args:
        angle (float): Angle in degrees
        
    Returns:
        float: Normalized angle
    """
    return (angle - 360) if angle >= 180 else angle


def norm360(angle):
    """
    Normalize angle to range [0, 360).
    
    Args:
        angle (float): Angle in degrees
        
    Returns:
        float: Normalized angle
    """
    return angle % 360


def unwrap_angles(angles):
    """
    Add 360 to elements in the input list so that all elements are sorted in ascending order.
    
    Args:
        angles (list): List of angles
        
    Returns:
        list: Unwrapped angles
    """
    result = angles.copy()
    for i in range(1, len(angles)):
        if result[i] < result[i - 1]:
            result[i] += 360
    
    assert (result == sorted(result))
    return result


def count_stars(from_star, to_star, dir=1):
    """
    Count the number of stars between two stars.
    
    Args:
        from_star (int): Starting star index
        to_star (int): Ending star index
        dir (int): Direction (1 for forward, -1 for backward)
        
    Returns:
        int: Number of stars
    """
    if dir == 1:
        return ((to_star + 27 - from_star) % 27) + 1
    else:
        return ((from_star + 27 - to_star) % 27) + 1


def count_rasis(from_rasi, to_rasi, dir=1):
    """
    Count the number of rasis (zodiac signs) between two rasis.
    
    Args:
        from_rasi (int): Starting rasi index
        to_rasi (int): Ending rasi index
        dir (int): Direction (1 for forward, -1 for backward)
        
    Returns:
        int: Number of rasis
    """
    if dir == 1:
        return ((to_rasi + 12 - from_rasi) % 12) + 1
    else:
        return ((from_rasi + 12 - to_rasi) % 12) + 1


def closest_elements(arr1, arr2):
    """
    Find the closest elements between two arrays.
    
    Args:
        arr1 (list): First array
        arr2 (list): Second array
        
    Returns:
        list: [element1, element2] with the smallest difference
    """
    result = []
    for a1 in arr1:
        for a2 in arr2:
            if a1 != a2:
                if a1 > a2:
                    result.append([a1, a2, a1 - a2])
                else:
                    result.append([a1, a2, a2 - a1])
    return sorted(result, key=lambda i: i[-1])[0][:2]


def closest_element_from_list(list_array, value):
    """
    Find the closest element to a value in a list.
    
    Args:
        list_array (list): List of values
        value (float): Target value
        
    Returns:
        float: Closest element in the list
    """
    return list_array[min(range(len(list_array)), key=lambda i: abs(list_array[i] - value))]


def get_fraction(start_time_hrs, end_time_hrs, birth_time_hrs):
    """
    Calculate the fraction of time between start and end times.
    
    Args:
        start_time_hrs (float): Start time in hours
        end_time_hrs (float): End time in hours
        birth_time_hrs (float): Birth time in hours
        
    Returns:
        float: Fraction of time
    """
    tl = end_time_hrs - start_time_hrs
    if start_time_hrs < 0:
        tl = 24 + end_time_hrs - abs(start_time_hrs)
    tf = (end_time_hrs - birth_time_hrs) / tl
    return tf
