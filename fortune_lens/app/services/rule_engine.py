"""
Rule Engine Service

This module provides functionality to evaluate complex astrological rules and conditions.
It supports logical operators (AND, OR) and comparison operators (IN, NOT IN, etc.).
Enhanced to support ruling planet relationships and planetary relationships.
"""

from bson import ObjectId
import re
from ..extensions import mongo
from ..config import BaseConfig
from .astrology.planetary_relationships import get_planet_relationship, is_planet_aspecting_planet

# House ruling planets mapping (1-based house numbers to planet names)
HOUSE_RULING_PLANETS = {
    1: 'MARS',      # Aries
    2: 'VENUS',     # Taurus
    3: 'MERCURY',   # Gemini
    4: 'MOON',      # Cancer
    5: 'SUN',       # <PERSON>
    6: 'MERCURY',   # Virgo
    7: 'VENUS',     # Libra
    8: 'MARS',      # <PERSON><PERSON><PERSON>
    9: 'JUPITER',   # <PERSON>gittar<PERSON>
    10: 'SATURN',   # Capricorn
    11: 'SATURN',   # Aquarius
    12: 'JUPITER'   # Pisces
}

# Planet name mappings for consistency
PLANET_NAMES = {
    'SUN': 'SUN',
    'MOON': 'MOON',
    'MARS': 'MARS',
    'MERCURY': 'MERCURY',
    'JUPITER': 'JUPITER',
    'VENUS': 'VENUS',
    'SATURN': 'SATURN',
    'RAHU': 'RAHU',
    'KETU': 'KETU'
}


def get_house_ruling_planet(house_number):
    """
    Get the ruling planet for a specific house (static mapping).

    Args:
        house_number (int): House number (1-12)

    Returns:
        str: Ruling planet name
    """
    return HOUSE_RULING_PLANETS.get(house_number)


def get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type="D1"):
    """
    Get the actual house name (sign) and ruling planet for a specific house from chart data.

    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        tuple: (house_name, ruling_planet_name) or (None, None) if not found
    """
    if not chart_data or "chart_data" not in chart_data:
        return None, None

    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return None, None

    # House name (sign) to ruling planet mapping
    house_name_ruling_planets = {
        'MESHAM': 'MARS',       # Aries
        'RISHABAM': 'VENUS',    # Taurus
        'MIDUNAM': 'MERCURY',   # Gemini
        'KADAGAM': 'MOON',      # Cancer
        'SIMMAM': 'SUN',        # Leo
        'KANNI': 'MERCURY',     # Virgo
        'THULAM': 'VENUS',      # Libra
        'VIRICHIGAM': 'MARS',   # Scorpio
        'DHANUSU': 'JUPITER',   # Sagittarius
        'MAGARAM': 'SATURN',    # Capricorn
        'KUMBAM': 'SATURN',     # Aquarius
        'MEENAM': 'JUPITER'     # Pisces
    }

    # Method 1: Check houses array for house_name (primary method for MongoDB structure)
    if "houses" in chart:
        for house in chart["houses"]:
            if house.get("house_number") == house_number:
                house_name = house.get("house_name")
                if house_name:
                    ruling_planet = house_name_ruling_planets.get(house_name.upper())
                    return house_name.upper(), ruling_planet

    # Method 2: Check if houses have sign information (alternative structure)
    if "houses" in chart:
        for house in chart["houses"]:
            if house.get("house_number") == house_number:
                house_sign = house.get("sign")
                if house_sign:
                    ruling_planet = house_name_ruling_planets.get(house_sign.upper())
                    return house_sign.upper(), ruling_planet

    # Method 3: Calculate from lagna and house position
    if "lagna" in chart and "sign" in chart["lagna"]:
        lagna_sign = chart["lagna"]["sign"].upper()

        # Sign order for calculation
        sign_order = ['MESHAM', 'RISHABAM', 'MIDUNAM', 'KADAGAM', 'SIMMAM', 'KANNI',
                     'THULAM', 'VIRICHIGAM', 'DHANUSU', 'MAGARAM', 'KUMBAM', 'MEENAM']

        try:
            lagna_index = sign_order.index(lagna_sign)
            # Calculate the sign for the given house (house 1 = lagna sign)
            house_sign_index = (lagna_index + house_number - 1) % 12
            house_sign = sign_order[house_sign_index]
            ruling_planet = house_name_ruling_planets.get(house_sign)
            return house_sign, ruling_planet
        except ValueError:
            pass

    # Method 4: Check if there's direct house sign mapping in chart data
    house_signs = chart.get("house_signs", {})
    if house_signs:
        house_sign = house_signs.get(str(house_number)) or house_signs.get(house_number)
        if house_sign:
            ruling_planet = house_name_ruling_planets.get(house_sign.upper())
            return house_sign.upper(), ruling_planet

    return None, None


def get_planet_nakshatra_from_chart(chart_data, planet_name, chart_type="D1"):
    """
    Get the nakshatra (star) of a specific planet from chart data.

    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name (uppercase)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        str: Nakshatra name or None if not found
    """
    if not chart_data or "chart_data" not in chart_data:
        return None

    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return None

    # Search through houses for the planet's nakshatra
    if "houses" in chart:
        for house in chart["houses"]:
            planets = house.get("planets", [])
            planet_nakshatras = house.get("planet_nakshatras", {})

            # Check if planet is in this house
            planet_lower = planet_name.lower()
            if planet_lower in planets and planet_lower in planet_nakshatras:
                return planet_nakshatras[planet_lower].upper()

    return None


def get_nakshatra_lord(nakshatra_name):
    """
    Get the ruling planet (lord) of a nakshatra.

    Args:
        nakshatra_name (str): Nakshatra name

    Returns:
        str: Ruling planet name or None if not found
    """
    # Nakshatra to ruling planet mapping
    nakshatra_lords = {
        'ASHWINI': 'KETU',
        'BARANI': 'VENUS',
        'KARTHIKAI': 'SUN',
        'ROHINI': 'MOON',
        'MIRIGASIRISHAM': 'MARS',
        'THIRUVADIRAI': 'RAHU',
        'PUNARPOOSAM': 'JUPITER',
        'POOSAM': 'SATURN',
        'AYILYAM': 'MERCURY',
        'MAGAM': 'KETU',
        'POORAM': 'VENUS',
        'UTHIRAM': 'SUN',
        'HASTHAM': 'MOON',
        'CHITHIRAI': 'MARS',
        'SWATHI': 'RAHU',
        'VISAGAM': 'JUPITER',
        'ANUSHAM': 'SATURN',
        'KETTAI': 'MERCURY',
        'MOOLAM': 'KETU',
        'POORADAM': 'VENUS',
        'UTHIRADAM': 'SUN',
        'THIRUVONAM': 'MOON',
        'AVITTAM': 'MARS',
        'SADAYAM': 'RAHU',
        'POORATADHI': 'JUPITER',
        'UTHIRATTADHI': 'SATURN',
        'REVATHI': 'MERCURY'
    }

    return nakshatra_lords.get(nakshatra_name.upper())


def check_ruling_planet_in_star_of_planet(chart_data, house_number, star_planet, chart_type="D1"):
    """
    Check if the ruling planet of a house is placed in the star (nakshatra) of another planet.

    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number (1-12)
        star_planet (str): Planet whose star we're checking for (e.g., "KETU")
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        bool: True if ruling planet is in the star of the specified planet
    """
    # Get the ruling planet of the house
    house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type)

    if not house_ruling_planet:
        return False

    # Get the nakshatra of the ruling planet
    ruling_planet_nakshatra = get_planet_nakshatra_from_chart(chart_data, house_ruling_planet, chart_type)

    if not ruling_planet_nakshatra:
        return False

    # Get the lord of this nakshatra
    nakshatra_lord = get_nakshatra_lord(ruling_planet_nakshatra)

    # Check if the nakshatra lord is the specified star planet
    return nakshatra_lord == star_planet.upper()


def get_planet_house_from_chart(chart_data, planet_name, chart_type="D1"):
    """
    Get the house number where a specific planet is located.

    Args:
        chart_data (dict): Chart data from MongoDB
        planet_name (str): Planet name (uppercase)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        int: House number where planet is located, or None if not found
    """
    if not chart_data or "chart_data" not in chart_data:
        return None

    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return None

    # Search through houses for the planet
    if "houses" in chart:
        for house in chart["houses"]:
            planets = house.get("planets", [])

            # Check if planet is in this house
            planet_lower = planet_name.lower()
            if planet_lower in planets:
                return house.get("house_number")

    return None


def get_planetary_aspects(planet_name, planet_house):
    """
    Get the houses that a planet aspects (looks at) from its current position.

    Args:
        planet_name (str): Planet name (uppercase)
        planet_house (int): House number where planet is located

    Returns:
        list: List of house numbers that the planet aspects
    """
    if not planet_house or planet_house < 1 or planet_house > 12:
        return []

    aspects = []

    # 7th house aspect (opposition) - all planets aspect 7th house from their position
    seventh_house = planet_house + 6
    if seventh_house > 12:
        seventh_house = seventh_house - 12
    aspects.append(seventh_house)

    # Special aspects for specific planets
    if planet_name.upper() == "MARS":
        # Mars aspects 4th and 8th houses from its position
        fourth_house = planet_house + 3
        if fourth_house > 12:
            fourth_house = fourth_house - 12
        eighth_house = planet_house + 7
        if eighth_house > 12:
            eighth_house = eighth_house - 12
        aspects.extend([fourth_house, eighth_house])

    elif planet_name.upper() == "JUPITER":
        # Jupiter aspects 5th and 9th houses from its position
        fifth_house = planet_house + 4
        if fifth_house > 12:
            fifth_house = fifth_house - 12
        ninth_house = planet_house + 8
        if ninth_house > 12:
            ninth_house = ninth_house - 12
        aspects.extend([fifth_house, ninth_house])

    elif planet_name.upper() == "SATURN":
        # Saturn aspects 3rd and 10th houses from its position
        third_house = planet_house + 2
        if third_house > 12:
            third_house = third_house - 12
        tenth_house = planet_house + 9
        if tenth_house > 12:
            tenth_house = tenth_house - 12
        aspects.extend([third_house, tenth_house])

    # Remove duplicates and sort
    return sorted(list(set(aspects)))


def check_ruling_planet_aspecting_planet_house(chart_data, house_number, target_planet, chart_type="D1"):
    """
    Check if the ruling planet of a house is aspecting the house where a target planet is located.

    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number whose ruling planet we're checking
        target_planet (str): Planet whose house we want to check for aspects
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        bool: True if ruling planet is aspecting the target planet's house
    """
    # Get the ruling planet of the house
    house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type)

    if not house_ruling_planet:
        return False

    # Get the house where the ruling planet is located
    ruling_planet_house = get_planet_house_from_chart(chart_data, house_ruling_planet, chart_type)

    if not ruling_planet_house:
        return False

    # Get the house where the target planet is located
    target_planet_house = get_planet_house_from_chart(chart_data, target_planet, chart_type)

    if not target_planet_house:
        return False

    # Get the houses that the ruling planet aspects
    aspected_houses = get_planetary_aspects(house_ruling_planet, ruling_planet_house)

    # Check if the target planet's house is in the aspected houses
    return target_planet_house in aspected_houses


def get_chart_data(user_profile_id, member_profile_id):
    """
    Get chart data for a specific user and member profile.

    Args:
        user_profile_id (str or int): User profile ID
        member_profile_id (str or int): Member profile ID

    Returns:
        dict: Chart data or None if not found
    """
    # Convert to int if string and digit
    if isinstance(user_profile_id, str) and user_profile_id.isdigit():
        user_profile_id = int(user_profile_id)

    if isinstance(member_profile_id, str) and member_profile_id.isdigit():
        member_profile_id = int(member_profile_id)

    # First, try to find astro data by user_profile_id and member_profile_id directly
    astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
        "user_profile_id": user_profile_id,
        "member_profile_id": member_profile_id
    })

    if astro_data:
        return astro_data

    # If not found, try to find by member ObjectId
    member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({
        "user_profile_id": user_profile_id,
        "member_profile_id": member_profile_id
    })

    if member_profile:
        member_id = member_profile["_id"]
        # Try to find astro data using member ObjectId
        astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
            "member_profile_id": member_id
        })

        if astro_data:
            return astro_data

    # If still not found, try alternative field combinations
    # Some records might use different field names
    astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
        "$or": [
            {"user_profile_id": user_profile_id, "member_profile_id": member_profile_id},
            {"user_id": user_profile_id, "member_id": member_profile_id}
        ]
    })

    return astro_data


def get_planet_house_mapping(chart_data, chart_type="D1"):
    """
    Extract planet to house mapping from chart data.

    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Mapping of planets to their houses
    """
    if not chart_data or "chart_data" not in chart_data:
        return {}

    # Get the specified chart
    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return {}

    # Create planet to house mapping
    planet_house_mapping = {}

    # Handle different chart data structures
    if "houses" in chart:
        # New structure: houses array with planets list
        for house in chart["houses"]:
            house_number = house.get("house_number")
            planets = house.get("planets", [])

            if house_number and planets:
                for planet_name in planets:
                    if isinstance(planet_name, str):
                        # Normalize planet names to uppercase
                        normalized_planet = normalize_planet_name(planet_name)
                        if normalized_planet:
                            planet_house_mapping[normalized_planet] = house_number

    # If no planets found in houses structure, try alternative structures
    if not planet_house_mapping:
        # Try to extract from planets_precise or other structures
        planets_precise = chart.get("planets_precise", {})
        if planets_precise:
            # This structure doesn't directly give house numbers,
            # so we need to map signs to houses using lagna
            lagna_info = chart.get("lagna", {})
            if lagna_info and "sign" in lagna_info:
                lagna_sign = lagna_info["sign"]
                # Map planets based on their signs relative to lagna
                planet_house_mapping = map_planets_by_signs(planets_precise, lagna_sign)

    return planet_house_mapping


def normalize_planet_name(planet_name):
    """
    Normalize planet names to standard uppercase format.

    Args:
        planet_name (str): Planet name in any format

    Returns:
        str: Normalized planet name or None if invalid
    """
    if not isinstance(planet_name, str):
        return None

    # Convert to uppercase and strip whitespace
    planet = planet_name.upper().strip()

    # Handle common variations
    planet_mapping = {
        'SUN': 'SUN',
        'MOON': 'MOON',
        'MARS': 'MARS',
        'MERCURY': 'MERCURY',
        'JUPITER': 'JUPITER',
        'VENUS': 'VENUS',
        'SATURN': 'SATURN',
        'RAHU': 'RAHU',
        'KETU': 'KETU',
        'LAGNAM': 'LAGNA',
        'LAGNA': 'LAGNA'
    }

    return planet_mapping.get(planet, planet if planet in PLANET_NAMES else None)


def map_planets_by_signs(planets_precise, lagna_sign):
    """
    Map planets to houses based on their signs and lagna sign.

    Args:
        planets_precise (dict): Planet positions with signs
        lagna_sign (str): Lagna sign

    Returns:
        dict: Planet to house mapping
    """
    # Sign to number mapping
    sign_numbers = {
        'MESHAM': 1, 'RISHABAM': 2, 'MIDUNAM': 3, 'KADAGAM': 4,
        'SIMMAM': 5, 'KANNI': 6, 'THULAM': 7, 'VIRICHIGAM': 8,
        'DHANUSU': 9, 'MAGARAM': 10, 'KUMBAM': 11, 'MEENAM': 12
    }

    # Get lagna sign number
    lagna_number = sign_numbers.get(lagna_sign.upper(), 1)

    planet_house_mapping = {}

    for planet, planet_info in planets_precise.items():
        if isinstance(planet_info, dict) and "sign" in planet_info:
            planet_sign = planet_info["sign"].upper()
            planet_sign_number = sign_numbers.get(planet_sign, 1)

            # Calculate house number relative to lagna
            house_number = ((planet_sign_number - lagna_number) % 12) + 1

            normalized_planet = normalize_planet_name(planet)
            if normalized_planet:
                planet_house_mapping[normalized_planet] = house_number

    return planet_house_mapping


def debug_chart_structure(chart_data, chart_type="D1"):
    """
    Debug function to analyze chart data structure.

    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type to analyze

    Returns:
        dict: Debug information about the chart structure
    """
    debug_info = {
        "chart_data_exists": "chart_data" in chart_data if chart_data else False,
        "chart_type_exists": False,
        "chart_structure": {},
        "available_charts": [],
        "houses_structure": {},
        "planets_found": []
    }

    if not chart_data:
        debug_info["error"] = "No chart data provided"
        return debug_info

    if "chart_data" not in chart_data:
        debug_info["error"] = "Missing 'chart_data' field"
        return debug_info

    chart_data_obj = chart_data["chart_data"]
    debug_info["available_charts"] = list(chart_data_obj.keys())

    if chart_type in chart_data_obj:
        debug_info["chart_type_exists"] = True
        chart = chart_data_obj[chart_type]
        debug_info["chart_structure"] = {
            "keys": list(chart.keys()),
            "has_houses": "houses" in chart,
            "has_planets_precise": "planets_precise" in chart,
            "has_lagna": "lagna" in chart
        }

        if "houses" in chart:
            houses = chart["houses"]
            debug_info["houses_structure"] = {
                "houses_count": len(houses),
                "sample_house": houses[0] if houses else None,
                "house_keys": list(houses[0].keys()) if houses else []
            }

            # Extract all planets found
            for house in houses:
                planets = house.get("planets", [])
                debug_info["planets_found"].extend(planets)

            debug_info["planets_found"] = list(set(debug_info["planets_found"]))

    return debug_info


def parse_condition(condition):
    """
    Parse a single condition. Supports multiple formats:
    - "Moon IN House1"
    - "Ketu IN House6 WITH Ruling_Planet"
    - "Ketu IS RELATED TO House6_Ruling_Planet"
    - "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet"

    Args:
        condition (str): Condition string

    Returns:
        tuple: (planet, operator, value, condition_type)
        condition_type can be: 'BASIC', 'WITH_RULING_PLANET', 'RELATED_TO_RULING_PLANET', 'ASPECTING_BIRTH_RULING_PLANET'
    """
    condition = condition.strip()

    # Pattern 1: Basic house placement - "Planet IN/NOT IN House#"
    basic_pattern = r'([A-Za-z]+)\s+(IN|NOT IN)\s+House(\d+)$'
    match = re.match(basic_pattern, condition, re.IGNORECASE)
    if match:
        planet, operator, house_num = match.groups()
        return planet.upper(), operator.upper(), int(house_num), 'BASIC'

    # Pattern 2a: Planet with explicit ruling planet house - "Planet IN House# WITH Ruling_Planet_house#"
    with_explicit_ruling_pattern = r'([A-Za-z]+)\s+(IN|NOT IN)\s+House(\d+)\s+WITH\s+Ruling_Planet_house(\d+)'
    match = re.match(with_explicit_ruling_pattern, condition, re.IGNORECASE)
    if match:
        planet, operator, house_num, ruling_house_num = match.groups()
        # For now, we expect the house numbers to match (planet in house X with ruling planet of house X)
        if int(house_num) == int(ruling_house_num):
            return planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET'
        else:
            # Could support cross-house ruling planet checks in future
            return planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET_CROSS'

    # Pattern 2b: Planet with ruling planet (legacy) - "Planet IN House# WITH Ruling_Planet"
    with_ruling_pattern = r'([A-Za-z]+)\s+(IN|NOT IN)\s+House(\d+)\s+WITH\s+Ruling_Planet'
    match = re.match(with_ruling_pattern, condition, re.IGNORECASE)
    if match:
        planet, operator, house_num = match.groups()
        return planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET'

    # Pattern 3: Planet relationship to house ruling planet - "Planet IS RELATED TO House#_Ruling_Planet"
    related_pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
    match = re.match(related_pattern, condition, re.IGNORECASE)
    if match:
        planet, house_num = match.groups()
        return planet.upper(), 'IS_RELATED_TO', int(house_num), 'RELATED_TO_RULING_PLANET'

    # Pattern 4: Planet aspecting house ruling planet - "Planet IS ASPECTING_BIRTH House#_Ruling_Planet"
    aspecting_pattern = r'([A-Za-z]+)\s+IS\s+ASPECTING_BIRTH\s+House(\d+)_Ruling_Planet'
    match = re.match(aspecting_pattern, condition, re.IGNORECASE)
    if match:
        planet, house_num = match.groups()
        return planet.upper(), 'IS_ASPECTING_BIRTH', int(house_num), 'ASPECTING_BIRTH_RULING_PLANET'

    # Pattern 5: House ruling planet in star of planet - "House#_Ruling_Planet IN_STAR_OF Planet"
    ruling_planet_in_star_pattern = r'House(\d+)_Ruling_Planet\s+IN_STAR_OF\s+([A-Za-z]+)'
    match = re.match(ruling_planet_in_star_pattern, condition, re.IGNORECASE)
    if match:
        house_num, star_planet = match.groups()
        return star_planet.upper(), 'IN_STAR_OF', int(house_num), 'RULING_PLANET_IN_STAR'

    # Pattern 6: House ruling planet aspecting planet house - "House#_Ruling_Planet IS_ASPECTING Planet"
    ruling_planet_aspecting_pattern = r'House(\d+)_Ruling_Planet\s+IS_ASPECTING\s+([A-Za-z]+)'
    match = re.match(ruling_planet_aspecting_pattern, condition, re.IGNORECASE)
    if match:
        house_num, target_planet = match.groups()
        return target_planet.upper(), 'IS_ASPECTING', int(house_num), 'RULING_PLANET_ASPECTING'

    # Pattern 7: Comprehensive relationship - "Planet IS_RELATED_TO #th House_Ruling_Planet"
    comprehensive_relationship_pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House_Ruling_Planet'
    match = re.match(comprehensive_relationship_pattern, condition, re.IGNORECASE)
    if match:
        planet, house_num = match.groups()
        return planet.upper(), 'IS_RELATED_TO', int(house_num), 'COMPREHENSIVE_RELATIONSHIP'

    # Pattern 8: House ruling planet relationships - "House#_Ruling_Planet IS_RELATED_TO House#_Ruling_Planet"
    house_ruling_planet_relationship_pattern = r'House(\d+)_Ruling_Planet\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
    match = re.match(house_ruling_planet_relationship_pattern, condition, re.IGNORECASE)
    if match:
        house_num1, house_num2 = match.groups()
        return f"{house_num1}_{house_num2}", 'IS_RELATED_TO', None, 'HOUSE_RULING_PLANET_RELATIONSHIP'

    # Pattern 9: House planet relationships - "#th House Planet IS_RELATED_TO #th House Planet"
    house_planet_relationship_pattern = r'(\d+)(?:st|nd|rd|th)?\s+House\s+Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?\s+House\s+Planet'
    match = re.match(house_planet_relationship_pattern, condition, re.IGNORECASE)
    if match:
        house_num1, house_num2 = match.groups()
        return f"{house_num1}_{house_num2}", 'IS_RELATED_TO', None, 'HOUSE_PLANET_RELATIONSHIP'

    # Pattern 10: House planet relationships with underscore - "#th_House_Planet IS_RELATED_TO #th_House_Planet"
    house_planet_underscore_pattern = r'(\d+)(?:st|nd|rd|th)?_House_Planet\s+IS\s+RELATED\s+TO\s+(\d+)(?:st|nd|rd|th)?_House_Planet'
    match = re.match(house_planet_underscore_pattern, condition, re.IGNORECASE)
    if match:
        house_num1, house_num2 = match.groups()
        return f"{house_num1}_{house_num2}", 'IS_RELATED_TO', None, 'HOUSE_PLANET_RELATIONSHIP'

    return None, None, None, None


def parse_complex_query(query):
    """
    Parse a complex query with logical operators including OR, AND, and NOT.

    Args:
        query (str): Complex query string

    Returns:
        list: List of parsed conditions with logical operators
    """
    # Handle parentheses for complex grouping (future enhancement)
    # For now, handle standard precedence: NOT > AND > OR

    # Split by OR (lowest precedence)
    or_parts = [part.strip() for part in query.split(" OR ")]

    parsed_query = []
    for or_part in or_parts:
        # Split by AND (higher precedence than OR)
        and_parts = [part.strip() for part in or_part.split(" AND ")]

        if len(and_parts) > 1:
            # Multiple AND conditions
            and_conditions = []
            for and_part in and_parts:
                # Handle NOT operator (highest precedence)
                is_negated = False
                if and_part.startswith("NOT "):
                    is_negated = True
                    and_part = and_part[4:].strip()  # Remove "NOT " prefix

                result = parse_condition(and_part)
                if len(result) == 4 and result[0] and result[1] and result[2] is not None:
                    planet, operator, value, condition_type = result

                    # Apply NOT operation by inverting the operator
                    if is_negated:
                        if operator == "IN":
                            operator = "NOT IN"
                        elif operator == "NOT IN":
                            operator = "IN"
                        # For advanced operators, we'll handle negation in evaluation
                        elif operator in ["IS_RELATED_TO", "IS_ASPECTING_BIRTH"]:
                            condition_type = f"NOT_{condition_type}"

                    and_conditions.append((planet, operator, value, condition_type))

            if and_conditions:
                parsed_query.append(("AND", and_conditions))
        else:
            # Single condition
            single_part = or_part

            # Handle NOT operator for single conditions
            is_negated = False
            if single_part.startswith("NOT "):
                is_negated = True
                single_part = single_part[4:].strip()  # Remove "NOT " prefix

            result = parse_condition(single_part)
            if len(result) == 4 and result[0] and result[1] and result[2] is not None:
                planet, operator, value, condition_type = result

                # Apply NOT operation by inverting the operator
                if is_negated:
                    if operator == "IN":
                        operator = "NOT IN"
                    elif operator == "NOT IN":
                        operator = "IN"
                    # For advanced operators, we'll handle negation in evaluation
                    elif operator in ["IS_RELATED_TO", "IS_ASPECTING_BIRTH"]:
                        condition_type = f"NOT_{condition_type}"

                parsed_query.append(("SINGLE", (planet, operator, value, condition_type)))

    return parsed_query


def evaluate_condition(planet, operator, value, condition_type, planet_house_mapping, chart_data=None, chart_type="D1"):
    """
    Evaluate a single condition with enhanced support for ruling planets, relationships, and aspects.

    Args:
        planet (str): Planet name
        operator (str): Operator (IN, NOT IN, IS_RELATED_TO, IS_ASPECTING_BIRTH)
        value (int): House number
        condition_type (str): Type of condition (BASIC, WITH_RULING_PLANET, RELATED_TO_RULING_PLANET, ASPECTING_BIRTH_RULING_PLANET)
        planet_house_mapping (dict): Mapping of planets to their houses
        chart_data (dict): Chart data from MongoDB (required for advanced rules)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        bool: Result of the condition evaluation
    """
    if planet not in planet_house_mapping:
        return False

    planet_house = planet_house_mapping[planet]

    if condition_type == 'BASIC':
        # Basic house placement check
        if operator == "IN":
            return planet_house == value
        elif operator == "NOT IN":
            return planet_house != value

    elif condition_type == 'WITH_RULING_PLANET':
        # Check if planet is in the house AND with its ruling planet
        if operator == "IN":
            # First check if planet is in the specified house
            if planet_house != value:
                return False

            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)
                house_sign = None

            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                return ruling_planet_house == value
            return False

        elif operator == "NOT IN":
            # Planet is not in the house OR not with its ruling planet
            if planet_house != value:
                return True

            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                return ruling_planet_house != value
            return True

    elif condition_type == 'RELATED_TO_RULING_PLANET':
        # Check if planet has a relationship (friend/enemy/neutral) with the ruling planet of the specified house
        if operator == "IS_RELATED_TO":
            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet:
                relationship = get_planet_relationship(planet, house_ruling_planet)
                # Consider any relationship (friend, enemy, or neutral) as "related"
                return relationship in ['FRIEND', 'ENEMY', 'NEUTRAL']
            return False

    elif condition_type == 'ASPECTING_BIRTH_RULING_PLANET':
        # Check if planet is aspecting the ruling planet of the specified house
        if operator == "IS_ASPECTING_BIRTH":
            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                # Check if the planet is aspecting the ruling planet
                return is_planet_aspecting_planet(planet, planet_house, house_ruling_planet, ruling_planet_house)
            return False

    # Handle NOT operations for advanced condition types
    elif condition_type == 'NOT_RELATED_TO_RULING_PLANET':
        # Check if planet is NOT related to the ruling planet of the specified house
        if operator == "IS_RELATED_TO":
            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet:
                relationship = get_planet_relationship(planet, house_ruling_planet)
                # Return opposite of normal relationship check
                return relationship not in ['FRIEND', 'ENEMY', 'NEUTRAL']
            return True  # If no ruling planet found, consider as "not related"

    elif condition_type == 'NOT_ASPECTING_BIRTH_RULING_PLANET':
        # Check if planet is NOT aspecting the ruling planet of the specified house
        if operator == "IS_ASPECTING_BIRTH":
            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                # Return opposite of normal aspecting check
                return not is_planet_aspecting_planet(planet, planet_house, house_ruling_planet, ruling_planet_house)
            return True  # If no ruling planet found, consider as "not aspecting"

    elif condition_type == 'NOT_WITH_RULING_PLANET':
        # Check if planet is NOT with the ruling planet of the specified house
        if operator == "IN":
            # First check if planet is in the specified house
            if planet_house != value:
                return True  # Planet not in house, so definitely not with ruling planet

            # Get the actual ruling planet from chart data
            if chart_data:
                house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, value, chart_type)
            else:
                # Fallback to static mapping if chart_data not available
                house_ruling_planet = get_house_ruling_planet(value)

            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                # Return opposite of normal WITH ruling planet check
                return ruling_planet_house != value
            return True  # If no ruling planet found, consider as "not with"

    elif condition_type == 'RULING_PLANET_IN_STAR':
        # Check if the ruling planet of a house is in the star (nakshatra) of the specified planet
        if operator == "IN_STAR_OF":
            return check_ruling_planet_in_star_of_planet(chart_data, value, planet, chart_type)

    elif condition_type == 'RULING_PLANET_ASPECTING':
        # Check if the ruling planet of a house is aspecting the house where the specified planet is located
        if operator == "IS_ASPECTING":
            return check_ruling_planet_aspecting_planet_house(chart_data, value, planet, chart_type)

    elif condition_type == 'COMPREHENSIVE_RELATIONSHIP':
        # Check comprehensive relationship between planet and house ruling planet
        if operator == "IS_RELATED_TO":
            comprehensive_result = check_comprehensive_relationship(chart_data, planet, value, chart_type)
            # Store comprehensive results for later retrieval
            if not hasattr(evaluate_condition, '_comprehensive_results'):
                evaluate_condition._comprehensive_results = {}
            evaluate_condition._comprehensive_results[f"{planet}_TO_{value}th_House_Ruling_Planet"] = comprehensive_result
            return comprehensive_result["overall_result"]

    elif condition_type == 'HOUSE_RULING_PLANET_RELATIONSHIP':
        # Check comprehensive relationship between two house ruling planets
        if operator == "IS_RELATED_TO":
            house_nums = planet.split('_')  # planet contains "house1_house2"
            house1, house2 = int(house_nums[0]), int(house_nums[1])
            comprehensive_result = check_house_ruling_planet_relationship(chart_data, house1, house2, chart_type)
            # Store comprehensive results for later retrieval
            if not hasattr(evaluate_condition, '_comprehensive_results'):
                evaluate_condition._comprehensive_results = {}
            evaluate_condition._comprehensive_results[f"House{house1}_Ruling_Planet_TO_House{house2}_Ruling_Planet"] = comprehensive_result
            return comprehensive_result["overall_result"]

    elif condition_type == 'HOUSE_PLANET_RELATIONSHIP':
        # Check comprehensive relationship between planets in two houses
        if operator == "IS_RELATED_TO":
            house_nums = planet.split('_')  # planet contains "house1_house2"
            house1, house2 = int(house_nums[0]), int(house_nums[1])
            comprehensive_result = check_house_planet_relationship(chart_data, house1, house2, chart_type)
            # Store comprehensive results for later retrieval
            if not hasattr(evaluate_condition, '_comprehensive_results'):
                evaluate_condition._comprehensive_results = {}
            evaluate_condition._comprehensive_results[f"House{house1}_Planet_TO_House{house2}_Planet"] = comprehensive_result
            return comprehensive_result["overall_result"]

    return False


def check_comprehensive_relationship(chart_data, planet, house_number, chart_type="D1"):
    """
    Check comprehensive relationship between a planet and house ruling planet.
    This evaluates 4 different types of relationships:
    1. Basic position: Planet in house OR Planet in ruling planet's house
    2. WITH ruling planet: Planet with ruling planet OR Ruling planet with planet
    3. Nakshatra: Planet in ruling planet's star OR Ruling planet in planet's star
    4. Aspecting: Planet aspecting ruling planet OR Ruling planet aspecting planet

    Args:
        chart_data (dict): Chart data from MongoDB
        planet (str): Planet name (e.g., "KETU")
        house_number (int): House number (e.g., 6 for 6th house)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Comprehensive relationship results
    """
    # Get house ruling planet
    house_sign, house_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house_number, chart_type)

    if not house_ruling_planet:
        return {
            "overall_result": False,
            "house_ruling_planet": None,
            "relationships": {
                "basic_position": False,
                "with_ruling_planet": False,
                "nakshatra": False,
                "aspecting": False
            },
            "details": {
                "basic_position": "House ruling planet not found",
                "with_ruling_planet": "House ruling planet not found",
                "nakshatra": "House ruling planet not found",
                "aspecting": "House ruling planet not found"
            }
        }

    # Get planet positions
    planet_house = get_planet_house_from_chart(chart_data, planet, chart_type)
    ruling_planet_house = get_planet_house_from_chart(chart_data, house_ruling_planet, chart_type)

    results = {
        "overall_result": False,
        "house_ruling_planet": house_ruling_planet,
        "relationships": {
            "basic_position": False,
            "with_ruling_planet": False,
            "nakshatra": False,
            "aspecting": False
        },
        "details": {
            "basic_position": "",
            "with_ruling_planet": "",
            "nakshatra": "",
            "aspecting": ""
        }
    }

    # 1. Basic Position Check: Planet in house OR Planet in ruling planet's house
    basic_position_result = False
    if planet_house == house_number:
        basic_position_result = True
        results["details"]["basic_position"] = f"{planet} is in House {house_number}"
    elif ruling_planet_house and planet_house == ruling_planet_house:
        basic_position_result = True
        results["details"]["basic_position"] = f"{planet} is in House {planet_house} (same as {house_ruling_planet})"
    else:
        results["details"]["basic_position"] = f"{planet} is in House {planet_house}, not in House {house_number} or {house_ruling_planet}'s house"

    results["relationships"]["basic_position"] = basic_position_result

    # 2. WITH Ruling Planet Check: Planet with ruling planet OR Ruling planet with planet
    with_ruling_planet_result = False
    if planet_house and ruling_planet_house and planet_house == ruling_planet_house:
        with_ruling_planet_result = True
        results["details"]["with_ruling_planet"] = f"{planet} and {house_ruling_planet} are both in House {planet_house}"
    else:
        results["details"]["with_ruling_planet"] = f"{planet} in House {planet_house}, {house_ruling_planet} in House {ruling_planet_house} - not together"

    results["relationships"]["with_ruling_planet"] = with_ruling_planet_result

    # 3. Nakshatra Check: Planet in ruling planet's star OR Ruling planet in planet's star
    nakshatra_result = False

    # Check if planet is in ruling planet's star
    planet_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet, chart_type)
    if planet_nakshatra:
        planet_nakshatra_lord = get_nakshatra_lord(planet_nakshatra)
        if planet_nakshatra_lord == house_ruling_planet.upper():
            nakshatra_result = True
            results["details"]["nakshatra"] = f"{planet} is in {planet_nakshatra} nakshatra (ruled by {house_ruling_planet})"

    # Check if ruling planet is in planet's star
    if not nakshatra_result:
        ruling_planet_nakshatra = get_planet_nakshatra_from_chart(chart_data, house_ruling_planet, chart_type)
        if ruling_planet_nakshatra:
            ruling_planet_nakshatra_lord = get_nakshatra_lord(ruling_planet_nakshatra)
            if ruling_planet_nakshatra_lord == planet.upper():
                nakshatra_result = True
                results["details"]["nakshatra"] = f"{house_ruling_planet} is in {ruling_planet_nakshatra} nakshatra (ruled by {planet})"

    if not nakshatra_result:
        results["details"]["nakshatra"] = f"No nakshatra relationship between {planet} and {house_ruling_planet}"

    results["relationships"]["nakshatra"] = nakshatra_result

    # 4. Aspecting Check: Planet aspecting ruling planet OR Ruling planet aspecting planet
    aspecting_result = False

    # Check if planet is aspecting ruling planet
    if planet_house and ruling_planet_house:
        planet_aspects = get_planetary_aspects(planet, planet_house)
        if ruling_planet_house in planet_aspects:
            aspecting_result = True
            results["details"]["aspecting"] = f"{planet} in House {planet_house} aspects House {ruling_planet_house} (where {house_ruling_planet} is)"

    # Check if ruling planet is aspecting planet
    if not aspecting_result and planet_house and ruling_planet_house:
        ruling_planet_aspects = get_planetary_aspects(house_ruling_planet, ruling_planet_house)
        if planet_house in ruling_planet_aspects:
            aspecting_result = True
            results["details"]["aspecting"] = f"{house_ruling_planet} in House {ruling_planet_house} aspects House {planet_house} (where {planet} is)"

    if not aspecting_result:
        results["details"]["aspecting"] = f"No aspecting relationship between {planet} and {house_ruling_planet}"

    results["relationships"]["aspecting"] = aspecting_result

    # Overall result: TRUE if ANY relationship exists
    results["overall_result"] = any(results["relationships"].values())

    # Add bidirectional analysis for detailed counting
    forward_relationships = results["relationships"].copy()

    # Reverse analysis: Check ruling planet to planet relationships
    reverse_relationships = {
        "basic_position": False,
        "with_ruling_planet": False,
        "together": False,
        "nakshatra": False,
        "aspecting": False
    }

    # For reverse analysis, check if ruling planet has relationships with the original planet
    try:
        # Get ruling planet location
        ruling_planet_house = None
        for house in chart_data.get(chart_type, {}).get("houses", []):
            for planet_data in house.get("planets", []):
                if planet_data.get("planet") == house_ruling_planet:
                    ruling_planet_house = house.get("house_number")
                    break

        if ruling_planet_house:
            # Check reverse relationships
            reverse_relationships["basic_position"] = check_basic_position_relationship(
                chart_data, house_ruling_planet, planet, chart_type
            )
            reverse_relationships["with_ruling_planet"] = check_with_ruling_planet_relationship(
                chart_data, house_ruling_planet, planet, chart_type
            )
            reverse_relationships["together"] = check_together_relationship(
                chart_data, house_ruling_planet, planet, chart_type
            )
            reverse_relationships["nakshatra"] = check_nakshatra_relationship(
                chart_data, house_ruling_planet, planet, chart_type
            )
            reverse_relationships["aspecting"] = check_aspecting_relationship(
                chart_data, house_ruling_planet, planet, chart_type
            )
    except Exception as e:
        # If reverse analysis fails, keep all as False
        pass

    # Add bidirectional analysis to results
    results["bidirectional_analysis"] = {
        "forward_relationships": forward_relationships,
        "reverse_relationships": reverse_relationships,
        "forward_count": sum(1 for rel in forward_relationships.values() if rel),
        "reverse_count": sum(1 for rel in reverse_relationships.values() if rel),
        "total_count": sum(1 for rel in forward_relationships.values() if rel) + sum(1 for rel in reverse_relationships.values() if rel)
    }

    return results


def check_house_ruling_planet_relationship(chart_data, house1, house2, chart_type="D1"):
    """
    Check comprehensive relationship between two house ruling planets.
    This evaluates 5 different types of relationships:
    1. Basic position: House1_ruling_planet in House2 OR House2_ruling_planet in House1
    2. WITH ruling planet: House1_ruling_planet in House2 WITH House2_ruling_planet OR House2_ruling_planet in House1 WITH House1_ruling_planet
    3. Together: House1_ruling_planet TOGETHER_WITH House2_ruling_planet
    4. Nakshatra: House1_ruling_planet in House2_ruling_planet's star OR House2_ruling_planet in House1_ruling_planet's star
    5. Aspecting: House1_ruling_planet aspecting House2_ruling_planet OR House2_ruling_planet aspecting House1_ruling_planet

    Args:
        chart_data (dict): Chart data from MongoDB
        house1 (int): First house number (e.g., 6 for 6th house)
        house2 (int): Second house number (e.g., 10 for 10th house)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Comprehensive relationship results between two house ruling planets
    """
    # Get house ruling planets
    house1_sign, house1_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house1, chart_type)
    house2_sign, house2_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house2, chart_type)

    if not house1_ruling_planet or not house2_ruling_planet:
        return {
            "overall_result": False,
            "house1_ruling_planet": house1_ruling_planet,
            "house2_ruling_planet": house2_ruling_planet,
            "relationships": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": False,
                "aspecting": False
            },
            "details": {
                "basic_position": "One or both house ruling planets not found",
                "with_ruling_planet": "One or both house ruling planets not found",
                "together": "One or both house ruling planets not found",
                "nakshatra": "One or both house ruling planets not found",
                "aspecting": "One or both house ruling planets not found"
            }
        }

    # Get planet positions
    house1_ruling_planet_house = get_planet_house_from_chart(chart_data, house1_ruling_planet, chart_type)
    house2_ruling_planet_house = get_planet_house_from_chart(chart_data, house2_ruling_planet, chart_type)

    results = {
        "overall_result": False,
        "house1_ruling_planet": house1_ruling_planet,
        "house2_ruling_planet": house2_ruling_planet,
        "relationships": {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": False,
            "nakshatra": False,
            "aspecting": False
        },
        "details": {
            "basic_position": "",
            "with_ruling_planet": "",
            "together": "",
            "nakshatra": "",
            "aspecting": ""
        }
    }

    # 1. Basic Position Check: House1_ruling_planet in House2 OR House2_ruling_planet in House1
    basic_position_result = False
    if house1_ruling_planet_house == house2:
        basic_position_result = True
        results["details"]["basic_position"] = f"{house1_ruling_planet} (House {house1} ruler) is in House {house2}"
    elif house2_ruling_planet_house == house1:
        basic_position_result = True
        results["details"]["basic_position"] = f"{house2_ruling_planet} (House {house2} ruler) is in House {house1}"
    else:
        results["details"]["basic_position"] = f"{house1_ruling_planet} in House {house1_ruling_planet_house}, {house2_ruling_planet} in House {house2_ruling_planet_house} - no cross-house placement"

    results["relationships"]["basic_position"] = basic_position_result

    # 2. WITH Ruling Planet Check: House1_ruling_planet in House2 WITH House2_ruling_planet OR House2_ruling_planet in House1 WITH House1_ruling_planet
    with_ruling_planet_result = False
    if house1_ruling_planet_house == house2 and house2_ruling_planet_house == house2:
        with_ruling_planet_result = True
        results["details"]["with_ruling_planet"] = f"{house1_ruling_planet} is in House {house2} WITH {house2_ruling_planet}"
    elif house2_ruling_planet_house == house1 and house1_ruling_planet_house == house1:
        with_ruling_planet_result = True
        results["details"]["with_ruling_planet"] = f"{house2_ruling_planet} is in House {house1} WITH {house1_ruling_planet}"
    else:
        results["details"]["with_ruling_planet"] = f"No WITH ruling planet relationship found"

    results["relationships"]["with_ruling_planet"] = with_ruling_planet_result

    # 3. Together Check: House1_ruling_planet TOGETHER_WITH House2_ruling_planet
    together_result = False
    if house1_ruling_planet_house and house2_ruling_planet_house and house1_ruling_planet_house == house2_ruling_planet_house:
        together_result = True
        results["details"]["together"] = f"{house1_ruling_planet} and {house2_ruling_planet} are both in House {house1_ruling_planet_house}"
    else:
        results["details"]["together"] = f"{house1_ruling_planet} in House {house1_ruling_planet_house}, {house2_ruling_planet} in House {house2_ruling_planet_house} - not together"

    results["relationships"]["together"] = together_result

    # 4. Nakshatra Check: House1_ruling_planet in House2_ruling_planet's star OR House2_ruling_planet in House1_ruling_planet's star
    nakshatra_result = False

    # Check if house1_ruling_planet is in house2_ruling_planet's star
    house1_ruling_planet_nakshatra = get_planet_nakshatra_from_chart(chart_data, house1_ruling_planet, chart_type)
    if house1_ruling_planet_nakshatra:
        house1_ruling_planet_nakshatra_lord = get_nakshatra_lord(house1_ruling_planet_nakshatra)
        if house1_ruling_planet_nakshatra_lord == house2_ruling_planet.upper():
            nakshatra_result = True
            results["details"]["nakshatra"] = f"{house1_ruling_planet} is in {house1_ruling_planet_nakshatra} nakshatra (ruled by {house2_ruling_planet})"

    # Check if house2_ruling_planet is in house1_ruling_planet's star
    if not nakshatra_result:
        house2_ruling_planet_nakshatra = get_planet_nakshatra_from_chart(chart_data, house2_ruling_planet, chart_type)
        if house2_ruling_planet_nakshatra:
            house2_ruling_planet_nakshatra_lord = get_nakshatra_lord(house2_ruling_planet_nakshatra)
            if house2_ruling_planet_nakshatra_lord == house1_ruling_planet.upper():
                nakshatra_result = True
                results["details"]["nakshatra"] = f"{house2_ruling_planet} is in {house2_ruling_planet_nakshatra} nakshatra (ruled by {house1_ruling_planet})"

    if not nakshatra_result:
        results["details"]["nakshatra"] = f"No nakshatra relationship between {house1_ruling_planet} and {house2_ruling_planet}"

    results["relationships"]["nakshatra"] = nakshatra_result

    # 5. Aspecting Check: House1_ruling_planet aspecting House2_ruling_planet OR House2_ruling_planet aspecting House1_ruling_planet
    aspecting_result = False

    # Check if house1_ruling_planet is aspecting house2_ruling_planet
    if house1_ruling_planet_house and house2_ruling_planet_house:
        house1_ruling_planet_aspects = get_planetary_aspects(house1_ruling_planet, house1_ruling_planet_house)
        if house2_ruling_planet_house in house1_ruling_planet_aspects:
            aspecting_result = True
            results["details"]["aspecting"] = f"{house1_ruling_planet} in House {house1_ruling_planet_house} aspects House {house2_ruling_planet_house} (where {house2_ruling_planet} is)"

    # Check if house2_ruling_planet is aspecting house1_ruling_planet
    if not aspecting_result and house1_ruling_planet_house and house2_ruling_planet_house:
        house2_ruling_planet_aspects = get_planetary_aspects(house2_ruling_planet, house2_ruling_planet_house)
        if house1_ruling_planet_house in house2_ruling_planet_aspects:
            aspecting_result = True
            results["details"]["aspecting"] = f"{house2_ruling_planet} in House {house2_ruling_planet_house} aspects House {house1_ruling_planet_house} (where {house1_ruling_planet} is)"

    if not aspecting_result:
        results["details"]["aspecting"] = f"No aspecting relationship between {house1_ruling_planet} and {house2_ruling_planet}"

    results["relationships"]["aspecting"] = aspecting_result

    # Overall result: TRUE if ANY relationship exists
    results["overall_result"] = any(results["relationships"].values())

    # Add comprehensive bidirectional analysis for house ruling planet relationships
    forward_relationships = results["relationships"].copy()
    forward_explanations = results["details"].copy()

    # Reverse analysis: Check house2 ruling planet to house1 ruling planet
    reverse_relationships = {
        "basic_position": False,
        "with_ruling_planet": False,
        "together": False,
        "nakshatra": False,
        "aspecting": False
    }

    reverse_explanations = {
        "basic_position": "",
        "with_ruling_planet": "",
        "together": "",
        "nakshatra": "",
        "aspecting": ""
    }

    # Perform reverse analysis (house2 ruling planet → house1 ruling planet)
    try:
        # Reverse basic position: Check if house2 ruling planet is in house1 ruling planet's house
        reverse_basic = False
        house2_ruling_location = get_planet_house_location(chart_data, house2_ruling_planet, chart_type)
        house1_ruling_location = get_planet_house_location(chart_data, house1_ruling_planet, chart_type)

        if house2_ruling_location == house1_num:
            reverse_basic = True
            reverse_explanations["basic_position"] = f"TRUE: {house2_ruling_planet} (House {house1_num} ruling planet) is in House {house1_num}"
        elif house1_ruling_location == house2_num:
            reverse_basic = True
            reverse_explanations["basic_position"] = f"TRUE: {house1_ruling_planet} (House {house2_num} ruling planet) is in House {house2_num}"
        else:
            reverse_explanations["basic_position"] = f"FALSE: {house2_ruling_planet} (House {house2_ruling_location}) not in House {house1_num}, {house1_ruling_planet} (House {house1_ruling_location}) not in House {house2_num}"

        reverse_relationships["basic_position"] = reverse_basic

        # Reverse WITH ruling planet: Same as forward (bidirectional)
        reverse_relationships["with_ruling_planet"] = forward_relationships["with_ruling_planet"]
        reverse_explanations["with_ruling_planet"] = forward_explanations["with_ruling_planet"] + " (bidirectional)"

        # Reverse together: Same as forward (bidirectional by nature)
        reverse_relationships["together"] = forward_relationships["together"]
        reverse_explanations["together"] = forward_explanations["together"] + " (bidirectional)"

        # Reverse nakshatra: Check if house1 ruling planet is in house2 ruling planet's nakshatra
        reverse_nakshatra = check_nakshatra_relationship(chart_data, house1_ruling_planet, house2_ruling_planet, chart_type)
        reverse_relationships["nakshatra"] = reverse_nakshatra
        if reverse_nakshatra:
            reverse_explanations["nakshatra"] = f"TRUE: {house1_ruling_planet} is in {house2_ruling_planet}'s nakshatra"
        else:
            reverse_explanations["nakshatra"] = f"FALSE: {house1_ruling_planet} is not in {house2_ruling_planet}'s nakshatra"

        # Reverse aspecting: Check if house1 ruling planet is aspecting house2 ruling planet
        reverse_aspecting = check_aspecting_relationship(chart_data, house1_ruling_planet, house2_ruling_planet, chart_type)
        reverse_relationships["aspecting"] = reverse_aspecting
        if reverse_aspecting:
            reverse_explanations["aspecting"] = f"TRUE: {house1_ruling_planet} is aspecting {house2_ruling_planet}"
        else:
            reverse_explanations["aspecting"] = f"FALSE: {house1_ruling_planet} is not aspecting {house2_ruling_planet}"

    except Exception as e:
        # If reverse analysis fails, keep all as False with error explanation
        for key in reverse_relationships:
            reverse_relationships[key] = False
            reverse_explanations[key] = f"FALSE: Reverse analysis error - {str(e)}"

    # Calculate counts and scoring
    forward_count = sum(1 for val in forward_relationships.values() if val)
    reverse_count = sum(1 for val in reverse_relationships.values() if val)
    total_true_count = forward_count + reverse_count
    max_marks = len(forward_relationships) * 2  # Forward + Reverse = 2x marks possible
    success_rate = (total_true_count / max_marks * 100) if max_marks > 0 else 0

    results["summary"] = {
        "total_relationship_types": len(forward_relationships),
        "forward_count": forward_count,
        "reverse_count": reverse_count,
        "total_true_count": total_true_count,
        "total_marks": total_true_count,
        "max_marks": max_marks,
        "success_rate": round(success_rate, 1),
        "relationship_breakdown": {
            "forward": forward_relationships,
            "reverse": reverse_relationships
        }
    }

    # Add comprehensive bidirectional analysis
    results["bidirectional_analysis"] = {
        "forward_analysis": {
            "relationships": forward_relationships,
            "explanations": forward_explanations,
            "count": forward_count,
            "direction": f"{house1_ruling_planet} (House {house1_num} ruling) → {house2_ruling_planet} (House {house2_num} ruling)"
        },
        "reverse_analysis": {
            "relationships": reverse_relationships,
            "explanations": reverse_explanations,
            "count": reverse_count,
            "direction": f"{house2_ruling_planet} (House {house2_num} ruling) → {house1_ruling_planet} (House {house1_num} ruling)"
        },
        "combined_scoring": {
            "forward_points": forward_count,
            "reverse_points": reverse_count,
            "total_points": total_true_count,
            "max_possible_points": max_marks,
            "success_percentage": round(success_rate, 1),
            "calculation": f"Forward ({forward_count}) + Reverse ({reverse_count}) = Total ({total_true_count}) out of {max_marks} possible"
        }
    }

    # Add detailed scoring
    results["scoring"] = {
        "forward_marks": {
            "basic_position": 1 if forward_relationships["basic_position"] else 0,
            "with_ruling_planet": 1 if forward_relationships["with_ruling_planet"] else 0,
            "together": 1 if forward_relationships["together"] else 0,
            "nakshatra": 1 if forward_relationships["nakshatra"] else 0,
            "aspecting": 1 if forward_relationships["aspecting"] else 0
        },
        "reverse_marks": {
            "basic_position": 1 if reverse_relationships["basic_position"] else 0,
            "with_ruling_planet": 1 if reverse_relationships["with_ruling_planet"] else 0,
            "together": 1 if reverse_relationships["together"] else 0,
            "nakshatra": 1 if reverse_relationships["nakshatra"] else 0,
            "aspecting": 1 if reverse_relationships["aspecting"] else 0
        },
        "total_marks_earned": total_true_count,
        "total_marks_possible": max_marks,
        "success_percentage": round(success_rate, 1),
        "rating": get_success_rating(success_rate)
    }

    return results


def get_success_rating(success_rate):
    """Get success rating based on percentage"""
    if success_rate >= 80:
        return "🌟 EXCELLENT - Strong astrological relationships!"
    elif success_rate >= 60:
        return "⭐ GOOD - Moderate astrological relationships!"
    elif success_rate >= 40:
        return "🔸 FAIR - Some astrological relationships!"
    elif success_rate >= 20:
        return "🔹 MINIMAL - Limited astrological relationships!"
    else:
        return "❌ NONE - No astrological relationships found!"


def check_planet_to_house_planet_relationship(chart_data, planet, house_number, chart_type="D1"):
    """
    Check comprehensive relationship between a specific planet and planets in a specific house.
    This uses the same 5-rule bidirectional logic as house-planet-relationship endpoint.

    This evaluates 5 different types of relationships in BOTH DIRECTIONS:
    1. Basic position: Planet in house OR House_planet in planet's house
    2. WITH ruling planet: Planet in house WITH house_ruling_planet OR House_planet with planet
    3. Together: Planet TOGETHER_WITH House_planet (ruling planet logic)
    4. Nakshatra: Planet in house_planet's star OR House_planet in planet's star (ruling planet logic)
    5. Aspecting: Planet aspecting house_planet OR House_planet aspecting planet (ruling planet logic)

    Args:
        chart_data (dict): Chart data from MongoDB
        planet (str): Planet name (e.g., "KETU")
        house_number (int): House number (e.g., 10 for 10th house)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Comprehensive relationship results with same structure as house-planet-relationship
    """
    # Get planets in the specified house
    house_planets = get_planets_in_house(chart_data, house_number, chart_type)

    # Get the specific planet's house
    planet_house = get_planet_house_from_chart(chart_data, planet.upper(), chart_type)

    if not house_planets:
        return {
            "overall_result": False,
            "planet": planet.upper(),
            "planet_house": planet_house,
            "target_house": house_number,
            "target_house_planets": [],
            "forward_direction": f"{planet.upper()} → House {house_number} Planets",
            "reverse_direction": f"House {house_number} Planets → {planet.upper()}",
            "planet_relationships": {},
            "summary": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "together_types": {
                    "ruling_planets_same_house": False,
                    "ruling_planets_different_house": False
                },
                "nakshatra": False,
                "aspecting": False
            },
            "details": {
                "basic_position": f"No planets found in House {house_number}",
                "with_ruling_planet": f"No planets found in House {house_number}",
                "together": f"No planets found in House {house_number}",
                "nakshatra": f"No planets found in House {house_number}",
                "aspecting": f"No planets found in House {house_number}"
            }
        }

    if not planet_house:
        return {
            "overall_result": False,
            "planet": planet.upper(),
            "planet_house": None,
            "target_house": house_number,
            "target_house_planets": [p.upper() for p in house_planets],
            "forward_direction": f"{planet.upper()} → House {house_number} Planets",
            "reverse_direction": f"House {house_number} Planets → {planet.upper()}",
            "planet_relationships": {},
            "summary": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "together_types": {
                    "ruling_planets_same_house": False,
                    "ruling_planets_different_house": False
                },
                "nakshatra": False,
                "aspecting": False
            },
            "details": {
                "basic_position": f"{planet.upper()} not found in chart",
                "with_ruling_planet": f"{planet.upper()} not found in chart",
                "together": f"{planet.upper()} not found in chart",
                "nakshatra": f"{planet.upper()} not found in chart",
                "aspecting": f"{planet.upper()} not found in chart"
            }
        }

    # Initialize results structure (same as house-planet-relationship)
    results = {
        "overall_result": False,
        "planet": planet.upper(),
        "planet_house": planet_house,
        "target_house": house_number,
        "target_house_planets": [p.upper() for p in house_planets],
        "forward_direction": f"{planet.upper()} → House {house_number} Planets",
        "reverse_direction": f"House {house_number} Planets → {planet.upper()}",
        "planet_relationships": {},
        "summary": {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": False,
            "together_types": {
                "ruling_planets_same_house": False,
                "ruling_planets_different_house": False
            },
            "nakshatra": False,
            "aspecting": False
        },
        "details": {
            "basic_position": [],
            "with_ruling_planet": [],
            "together": [],
            "nakshatra": [],
            "aspecting": []
        }
    }

    # Get house ruling planets for WITH checks
    planet_house_ruling_planet = get_house_ruling_planet(planet_house)
    target_house_ruling_planet = get_house_ruling_planet(house_number)

    # Check relationships between the specific planet and each planet in the target house
    relationship_found = {
        "basic_position": False,
        "with_ruling_planet": False,
        "together": False,
        "nakshatra": False,
        "aspecting": False
    }

    together_types_found = {
        "ruling_planets_same_house": False,
        "ruling_planets_different_house": False
    }

    relationship_details = {
        "basic_position": [],
        "with_ruling_planet": [],
        "together": [],
        "nakshatra": [],
        "aspecting": []
    }

    # Check relationships with each planet in the target house
    for house_planet in house_planets:
        house_planet_upper = house_planet.upper()

        # Skip if same planet
        if planet.upper() == house_planet_upper:
            continue

        pair_key = f"{planet.upper()}_TO_{house_planet_upper}"

        # Get house planet's position
        house_planet_house = get_planet_house_from_chart(chart_data, house_planet_upper, chart_type)

        pair_results = {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": False,
            "nakshatra": False,
            "aspecting": False,
            "direction": f"{planet.upper()} (House {planet_house}) → {house_planet_upper} (House {house_number})"
        }

        # 1. Basic Position Check
        if house_planet_house == planet_house:
            pair_results["basic_position"] = True
            relationship_found["basic_position"] = True
            relationship_details["basic_position"].append(f"{planet.upper()} and {house_planet_upper} are both in House {planet_house}")

        # 2. WITH Ruling Planet Check
        # Check if planet is WITH target house ruling planet
        if target_house_ruling_planet:
            target_house_ruling_planet_house = get_planet_house_from_chart(chart_data, target_house_ruling_planet, chart_type)
            if target_house_ruling_planet_house == planet_house:
                pair_results["with_ruling_planet"] = True
                relationship_found["with_ruling_planet"] = True
                relationship_details["with_ruling_planet"].append(f"{planet.upper()} in House {planet_house} WITH {target_house_ruling_planet} (House {house_number} ruling planet)")

        # Check if house planet is WITH planet's house ruling planet
        if planet_house_ruling_planet:
            planet_house_ruling_planet_house = get_planet_house_from_chart(chart_data, planet_house_ruling_planet, chart_type)
            if planet_house_ruling_planet_house == house_number:
                pair_results["with_ruling_planet"] = True
                relationship_found["with_ruling_planet"] = True
                relationship_details["with_ruling_planet"].append(f"{house_planet_upper} in House {house_number} WITH {planet_house_ruling_planet} (House {planet_house} ruling planet)")

        # 3. Together Check (Ruling Planet Logic Only)
        together_result = False
        together_type = ""

        # Get ruling planets of the house numbers where the planets are located
        planet_house_ruling_planet = get_house_ruling_planet(planet_house)
        house_planet_house_ruling_planet = get_house_ruling_planet(house_number)

        if planet_house_ruling_planet and house_planet_house_ruling_planet:
            # Get where these ruling planets are located in the chart
            ruling_planet1_house = get_planet_house_from_chart(chart_data, planet_house_ruling_planet, chart_type)
            ruling_planet2_house = get_planet_house_from_chart(chart_data, house_planet_house_ruling_planet, chart_type)

            if ruling_planet1_house and ruling_planet2_house:
                # Check if ruling planets are in the same house (together)
                if ruling_planet1_house == ruling_planet2_house:
                    together_result = True
                    together_type = "ruling_planets_same_house"
                    relationship_found["together"] = True
                    together_types_found["ruling_planets_same_house"] = True
                    relationship_details["together"].append(f"{planet.upper()} (House {planet_house}) and {house_planet_upper} (House {house_number}) are together because their ruling planets {planet_house_ruling_planet} and {house_planet_house_ruling_planet} are both in House {ruling_planet1_house}")
                else:
                    # Ruling planets are in different houses
                    together_result = False
                    together_type = "ruling_planets_different_house"
                    together_types_found["ruling_planets_different_house"] = True
                    relationship_details["together"].append(f"{planet.upper()} (House {planet_house}) and {house_planet_upper} (House {house_number}) are NOT together because their ruling planets {planet_house_ruling_planet} (House {ruling_planet1_house}) and {house_planet_house_ruling_planet} (House {ruling_planet2_house}) are in different houses")

        pair_results["together"] = together_result
        if together_result:
            pair_results["together_type"] = together_type

        # 4. Nakshatra Check (Ruling Planet Logic Only)
        # Check if ruling planet of planet's house is in nakshatra ruled by ruling planet of target house
        if planet_house_ruling_planet and house_planet_house_ruling_planet:
            # Get nakshatra of planet's house ruling planet
            ruling_planet1_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet_house_ruling_planet, chart_type)
            if ruling_planet1_nakshatra:
                ruling_planet1_nakshatra_lord = get_nakshatra_lord(ruling_planet1_nakshatra)
                if ruling_planet1_nakshatra_lord == house_planet_house_ruling_planet:
                    pair_results["nakshatra"] = True
                    relationship_found["nakshatra"] = True
                    relationship_details["nakshatra"].append(f"House {planet_house} ruling planet {planet_house_ruling_planet} is in {ruling_planet1_nakshatra} nakshatra (ruled by House {house_number} ruling planet {house_planet_house_ruling_planet})")

            # Get nakshatra of target house ruling planet
            ruling_planet2_nakshatra = get_planet_nakshatra_from_chart(chart_data, house_planet_house_ruling_planet, chart_type)
            if ruling_planet2_nakshatra:
                ruling_planet2_nakshatra_lord = get_nakshatra_lord(ruling_planet2_nakshatra)
                if ruling_planet2_nakshatra_lord == planet_house_ruling_planet:
                    pair_results["nakshatra"] = True
                    relationship_found["nakshatra"] = True
                    relationship_details["nakshatra"].append(f"House {house_number} ruling planet {house_planet_house_ruling_planet} is in {ruling_planet2_nakshatra} nakshatra (ruled by House {planet_house} ruling planet {planet_house_ruling_planet})")

        # 5. Aspecting Check (Ruling Planet Logic Only)
        # Check if ruling planet of planet's house aspects ruling planet of target house
        if planet_house_ruling_planet and house_planet_house_ruling_planet:
            # Get where ruling planets are located
            ruling_planet1_house = get_planet_house_from_chart(chart_data, planet_house_ruling_planet, chart_type)
            ruling_planet2_house = get_planet_house_from_chart(chart_data, house_planet_house_ruling_planet, chart_type)

            if ruling_planet1_house and ruling_planet2_house:
                # Check if ruling planet1 aspects ruling planet2's house
                ruling_planet1_aspects = get_planetary_aspects(planet_house_ruling_planet, ruling_planet1_house)
                if ruling_planet2_house in ruling_planet1_aspects:
                    pair_results["aspecting"] = True
                    relationship_found["aspecting"] = True
                    relationship_details["aspecting"].append(f"House {planet_house} ruling planet {planet_house_ruling_planet} (in House {ruling_planet1_house}) aspects House {ruling_planet2_house} (where House {house_number} ruling planet {house_planet_house_ruling_planet} is)")

                # Check if ruling planet2 aspects ruling planet1's house
                ruling_planet2_aspects = get_planetary_aspects(house_planet_house_ruling_planet, ruling_planet2_house)
                if ruling_planet1_house in ruling_planet2_aspects:
                    pair_results["aspecting"] = True
                    relationship_found["aspecting"] = True
                    relationship_details["aspecting"].append(f"House {house_number} ruling planet {house_planet_house_ruling_planet} (in House {ruling_planet2_house}) aspects House {ruling_planet1_house} (where House {planet_house} ruling planet {planet_house_ruling_planet} is)")

        results["planet_relationships"][pair_key] = pair_results

        # REVERSE DIRECTION: Check house_planet → planet relationships
        reverse_pair_key = f"{house_planet_upper}_TO_{planet.upper()}"

        reverse_pair_results = {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": False,
            "nakshatra": False,
            "aspecting": False,
            "direction": f"{house_planet_upper} (House {house_number}) → {planet.upper()} (House {planet_house})"
        }

        # Same 5-rule logic but in reverse direction
        # 1. Basic Position Check (already covered above)
        reverse_pair_results["basic_position"] = pair_results["basic_position"]

        # 2. WITH Ruling Planet Check (already covered above)
        reverse_pair_results["with_ruling_planet"] = pair_results["with_ruling_planet"]

        # 3. Together Check (already covered above)
        reverse_pair_results["together"] = pair_results["together"]
        if pair_results.get("together_type"):
            reverse_pair_results["together_type"] = pair_results["together_type"]

        # 4. Nakshatra Check (already covered above)
        reverse_pair_results["nakshatra"] = pair_results["nakshatra"]

        # 5. Aspecting Check (already covered above)
        reverse_pair_results["aspecting"] = pair_results["aspecting"]

        results["planet_relationships"][reverse_pair_key] = reverse_pair_results

    # Set summary and overall result
    results["summary"]["basic_position"] = relationship_found["basic_position"]
    results["summary"]["with_ruling_planet"] = relationship_found["with_ruling_planet"]
    results["summary"]["together"] = relationship_found["together"]
    results["summary"]["together_types"]["ruling_planets_same_house"] = together_types_found["ruling_planets_same_house"]
    results["summary"]["together_types"]["ruling_planets_different_house"] = together_types_found["ruling_planets_different_house"]
    results["summary"]["nakshatra"] = relationship_found["nakshatra"]
    results["summary"]["aspecting"] = relationship_found["aspecting"]

    results["overall_result"] = any(relationship_found.values())

    # Convert details lists to strings
    for key in results["details"]:
        if isinstance(results["details"][key], list):
            results["details"][key] = "; ".join(results["details"][key]) if results["details"][key] else f"No {key} relationships found"

    # Add summary with counts and scoring
    relationship_values = list(relationship_found.values())
    true_count = sum(1 for val in relationship_values if val)
    false_count = sum(1 for val in relationship_values if not val)
    total_marks = true_count  # 1 mark per TRUE relationship
    max_marks = len(relationship_values)  # 5 relationship types
    success_rate = (total_marks / max_marks * 100) if max_marks > 0 else 0

    # Update summary with counts
    results["summary"]["total_relationship_types"] = max_marks
    results["summary"]["true_count"] = true_count
    results["summary"]["false_count"] = false_count
    results["summary"]["total_marks"] = total_marks
    results["summary"]["max_marks"] = max_marks
    results["summary"]["success_rate"] = round(success_rate, 1)

    # Add scoring details
    results["scoring"] = {
        "marks_per_relationship": {
            "basic_position": 1 if relationship_found["basic_position"] else 0,
            "with_ruling_planet": 1 if relationship_found["with_ruling_planet"] else 0,
            "together": 1 if relationship_found["together"] else 0,
            "nakshatra": 1 if relationship_found["nakshatra"] else 0,
            "aspecting": 1 if relationship_found["aspecting"] else 0
        },
        "total_marks_earned": total_marks,
        "total_marks_possible": max_marks,
        "success_percentage": round(success_rate, 1),
        "rating": get_success_rating(success_rate)
    }

    return results


def get_planets_in_house(chart_data, house_number, chart_type="D1"):
    """
    Get all planets present in a specific house.

    Args:
        chart_data (dict): Chart data from MongoDB
        house_number (int): House number
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        list: List of planets in the house
    """
    if not chart_data or "chart_data" not in chart_data:
        return []

    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return []

    # Search for the house
    if "houses" in chart:
        for house in chart["houses"]:
            if house.get("house_number") == house_number:
                return house.get("planets", [])

    return []


def check_house_planet_relationship(chart_data, house1, house2, chart_type="D1"):
    """
    Check comprehensive relationship between planets in two houses.
    This evaluates 5 different types of relationships for each planet pair in BOTH DIRECTIONS:

    Forward Direction (House1 → House2):
    1. Basic position: Planet1 in House2 OR Planet2 in House1
    2. WITH ruling planet: Planet1 in House2 WITH House2_ruling_planet OR Planet2 in House1 WITH House1_ruling_planet
    3. Together: Planet1 TOGETHER_WITH Planet2 (in same house)
    4. Nakshatra: Planet1 in Planet2's star OR Planet2 in Planet1's star
    5. Aspecting: Planet1 aspecting Planet2 OR Planet2 aspecting Planet1

    Reverse Direction (House2 → House1):
    Same 5 relationship types checked in reverse order for comprehensive analysis.

    Args:
        chart_data (dict): Chart data from MongoDB
        house1 (int): First house number (e.g., 6 for 6th house)
        house2 (int): Second house number (e.g., 10 for 10th house)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Comprehensive relationship results between planets in two houses (both directions)
    """
    # Get planets in both houses
    house1_planets = get_planets_in_house(chart_data, house1, chart_type)
    house2_planets = get_planets_in_house(chart_data, house2, chart_type)

    if not house1_planets and not house2_planets:
        return {
            "overall_result": False,
            "house1_planets": [],
            "house2_planets": [],
            "forward_direction": f"House {house1} → House {house2}",
            "reverse_direction": f"House {house2} → House {house1}",
            "planet_relationships": {},
            "summary": {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": False,
                "aspecting": False
            },
            "details": {
                "basic_position": f"No planets found in House {house1} or House {house2}",
                "with_ruling_planet": f"No planets found in House {house1} or House {house2}",
                "together": f"No planets found in House {house1} or House {house2}",
                "nakshatra": f"No planets found in House {house1} or House {house2}",
                "aspecting": f"No planets found in House {house1} or House {house2}"
            }
        }

    results = {
        "overall_result": False,
        "house1_planets": [p.upper() for p in house1_planets],
        "house2_planets": [p.upper() for p in house2_planets],
        "forward_direction": f"House {house1} → House {house2}",
        "reverse_direction": f"House {house2} → House {house1}",
        "planet_relationships": {},
        "summary": {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": False,
            "together_types": {
                "ruling_planets_same_house": False,
                "ruling_planets_different_house": False
            },
            "nakshatra": False,
            "aspecting": False
        },
        "details": {
            "basic_position": "",
            "with_ruling_planet": "",
            "together": "",
            "nakshatra": "",
            "aspecting": ""
        }
    }

    # Get house ruling planets for WITH checks
    house1_sign, house1_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house1, chart_type)
    house2_sign, house2_ruling_planet = get_house_sign_and_ruling_planet_from_chart(chart_data, house2, chart_type)

    # Check relationships between all planet pairs
    relationship_found = {
        "basic_position": False,
        "with_ruling_planet": False,
        "together": False,
        "nakshatra": False,
        "aspecting": False
    }

    together_types_found = {
        "ruling_planets_same_house": False,
        "ruling_planets_different_house": False
    }

    relationship_details = {
        "basic_position": [],
        "with_ruling_planet": [],
        "together": [],
        "nakshatra": [],
        "aspecting": []
    }

    # Check relationships between planets in house1 and planets in house2 (both directions)

    # Forward direction: house1 planets → house2 planets
    for planet1 in house1_planets:
        planet1_upper = planet1.upper()

        for planet2 in house2_planets:
            planet2_upper = planet2.upper()

            # Skip if same planet
            if planet1_upper == planet2_upper:
                continue

            pair_key = f"{planet1_upper}_TO_{planet2_upper}"

            # Skip if this pair was already processed (avoid duplicates)
            reverse_pair_key = f"{planet2_upper}_TO_{planet1_upper}"
            if reverse_pair_key in results["planet_relationships"]:
                continue

            # Get planet positions
            planet1_house = get_planet_house_from_chart(chart_data, planet1_upper, chart_type)
            planet2_house = get_planet_house_from_chart(chart_data, planet2_upper, chart_type)

            pair_results = {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": False,
                "aspecting": False,
                "direction": f"{planet1_upper} (House {house1}) → {planet2_upper} (House {house2})"
            }

            # 1. Basic Position Check
            # Check if planet1 (from house1) is in house2, or planet2 (from house2) is in house1
            if planet1_house == house2:
                pair_results["basic_position"] = True
                relationship_found["basic_position"] = True
                relationship_details["basic_position"].append(f"{planet1_upper} (from House {house1}) is in House {house2}")
            elif planet2_house == house1:
                pair_results["basic_position"] = True
                relationship_found["basic_position"] = True
                relationship_details["basic_position"].append(f"{planet2_upper} (from House {house2}) is in House {house1}")

            # 2. WITH Ruling Planet Check
            # Check if planet1 is in house2 WITH house2's ruling planet
            if planet1_house == house2 and house2_ruling_planet:
                house2_ruling_house = get_planet_house_from_chart(chart_data, house2_ruling_planet, chart_type)
                if house2_ruling_house == house2:
                    pair_results["with_ruling_planet"] = True
                    relationship_found["with_ruling_planet"] = True
                    relationship_details["with_ruling_planet"].append(f"{planet1_upper} in House {house2} WITH {house2_ruling_planet}")

            # Check if planet2 is in house1 WITH house1's ruling planet
            if planet2_house == house1 and house1_ruling_planet:
                house1_ruling_house = get_planet_house_from_chart(chart_data, house1_ruling_planet, chart_type)
                if house1_ruling_house == house1:
                    pair_results["with_ruling_planet"] = True
                    relationship_found["with_ruling_planet"] = True
                    relationship_details["with_ruling_planet"].append(f"{planet2_upper} in House {house1} WITH {house1_ruling_planet}")

            # 3. Together Check (Ruling Planet Logic Only)
            together_result = False
            together_type = ""

            # Get ruling planets of the house numbers where the planets are located
            planet1_house_ruling_planet = get_house_ruling_planet(planet1_house)
            planet2_house_ruling_planet = get_house_ruling_planet(planet2_house)

            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get where these ruling planets are located in the chart
                ruling_planet1_house = get_planet_house_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                ruling_planet2_house = get_planet_house_from_chart(chart_data, planet2_house_ruling_planet, chart_type)

                if ruling_planet1_house and ruling_planet2_house:
                    # Check if ruling planets are in the same house (together)
                    if ruling_planet1_house == ruling_planet2_house:
                        together_result = True
                        together_type = "ruling_planets_same_house"
                        relationship_found["together"] = True
                        together_types_found["ruling_planets_same_house"] = True
                        relationship_details["together"].append(f"{planet1_upper} (House {planet1_house}) and {planet2_upper} (House {planet2_house}) are together because their ruling planets {planet1_house_ruling_planet} and {planet2_house_ruling_planet} are both in House {ruling_planet1_house}")
                    else:
                        # Ruling planets are in different houses
                        together_result = False
                        together_type = "ruling_planets_different_house"
                        together_types_found["ruling_planets_different_house"] = True
                        relationship_details["together"].append(f"{planet1_upper} (House {planet1_house}) and {planet2_upper} (House {planet2_house}) are NOT together because their ruling planets {planet1_house_ruling_planet} (House {ruling_planet1_house}) and {planet2_house_ruling_planet} (House {ruling_planet2_house}) are in different houses")

            pair_results["together"] = together_result
            if together_result:
                pair_results["together_type"] = together_type

            # 4. Nakshatra Check (Ruling Planet Logic Only)
            # Check if ruling planet of house1 is in nakshatra ruled by ruling planet of house2
            planet1_house_ruling_planet = get_house_ruling_planet(planet1_house)
            planet2_house_ruling_planet = get_house_ruling_planet(planet2_house)

            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get nakshatra of house1's ruling planet
                ruling_planet1_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                if ruling_planet1_nakshatra:
                    ruling_planet1_nakshatra_lord = get_nakshatra_lord(ruling_planet1_nakshatra)
                    if ruling_planet1_nakshatra_lord == planet2_house_ruling_planet:
                        pair_results["nakshatra"] = True
                        relationship_found["nakshatra"] = True
                        relationship_details["nakshatra"].append(f"House {planet1_house} ruling planet {planet1_house_ruling_planet} is in {ruling_planet1_nakshatra} nakshatra (ruled by House {planet2_house} ruling planet {planet2_house_ruling_planet})")

                # Get nakshatra of house2's ruling planet
                ruling_planet2_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet2_house_ruling_planet, chart_type)
                if ruling_planet2_nakshatra:
                    ruling_planet2_nakshatra_lord = get_nakshatra_lord(ruling_planet2_nakshatra)
                    if ruling_planet2_nakshatra_lord == planet1_house_ruling_planet:
                        pair_results["nakshatra"] = True
                        relationship_found["nakshatra"] = True
                        relationship_details["nakshatra"].append(f"House {planet2_house} ruling planet {planet2_house_ruling_planet} is in {ruling_planet2_nakshatra} nakshatra (ruled by House {planet1_house} ruling planet {planet1_house_ruling_planet})")

            # 5. Aspecting Check (Ruling Planet Logic Only)
            # Check if ruling planet of house1 aspects ruling planet of house2
            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get where ruling planets are located
                ruling_planet1_house = get_planet_house_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                ruling_planet2_house = get_planet_house_from_chart(chart_data, planet2_house_ruling_planet, chart_type)

                if ruling_planet1_house and ruling_planet2_house:
                    # Check if ruling planet1 aspects ruling planet2's house
                    ruling_planet1_aspects = get_planetary_aspects(planet1_house_ruling_planet, ruling_planet1_house)
                    if ruling_planet2_house in ruling_planet1_aspects:
                        pair_results["aspecting"] = True
                        relationship_found["aspecting"] = True
                        relationship_details["aspecting"].append(f"House {planet1_house} ruling planet {planet1_house_ruling_planet} (in House {ruling_planet1_house}) aspects House {ruling_planet2_house} (where House {planet2_house} ruling planet {planet2_house_ruling_planet} is)")

                    # Check if ruling planet2 aspects ruling planet1's house
                    ruling_planet2_aspects = get_planetary_aspects(planet2_house_ruling_planet, ruling_planet2_house)
                    if ruling_planet1_house in ruling_planet2_aspects:
                        pair_results["aspecting"] = True
                        relationship_found["aspecting"] = True
                        relationship_details["aspecting"].append(f"House {planet2_house} ruling planet {planet2_house_ruling_planet} (in House {ruling_planet2_house}) aspects House {ruling_planet1_house} (where House {planet1_house} ruling planet {planet1_house_ruling_planet} is)")

            results["planet_relationships"][pair_key] = pair_results

    # Reverse direction: house2 planets → house1 planets
    for planet2 in house2_planets:
        planet2_upper = planet2.upper()

        for planet1 in house1_planets:
            planet1_upper = planet1.upper()

            # Skip if same planet
            if planet1_upper == planet2_upper:
                continue

            pair_key = f"{planet2_upper}_TO_{planet1_upper}"

            # Skip if this pair was already processed in forward direction
            if pair_key in results["planet_relationships"]:
                continue

            # Get planet positions
            planet1_house = get_planet_house_from_chart(chart_data, planet1_upper, chart_type)
            planet2_house = get_planet_house_from_chart(chart_data, planet2_upper, chart_type)

            pair_results = {
                "basic_position": False,
                "with_ruling_planet": False,
                "together": False,
                "nakshatra": False,
                "aspecting": False,
                "direction": f"{planet2_upper} (House {house2}) → {planet1_upper} (House {house1})"
            }

            # 1. Basic Position Check
            # Check if planet2 (from house2) is in house1, or planet1 (from house1) is in house2
            if planet2_house == house1:
                pair_results["basic_position"] = True
                relationship_found["basic_position"] = True
                relationship_details["basic_position"].append(f"{planet2_upper} (from House {house2}) is in House {house1}")
            elif planet1_house == house2:
                pair_results["basic_position"] = True
                relationship_found["basic_position"] = True
                relationship_details["basic_position"].append(f"{planet1_upper} (from House {house1}) is in House {house2}")

            # 2. WITH Ruling Planet Check
            # Check if planet2 is in house1 WITH house1's ruling planet
            if planet2_house == house1 and house1_ruling_planet:
                house1_ruling_house = get_planet_house_from_chart(chart_data, house1_ruling_planet, chart_type)
                if house1_ruling_house == house1:
                    pair_results["with_ruling_planet"] = True
                    relationship_found["with_ruling_planet"] = True
                    relationship_details["with_ruling_planet"].append(f"{planet2_upper} in House {house1} WITH {house1_ruling_planet}")

            # Check if planet1 is in house2 WITH house2's ruling planet
            if planet1_house == house2 and house2_ruling_planet:
                house2_ruling_house = get_planet_house_from_chart(chart_data, house2_ruling_planet, chart_type)
                if house2_ruling_house == house2:
                    pair_results["with_ruling_planet"] = True
                    relationship_found["with_ruling_planet"] = True
                    relationship_details["with_ruling_planet"].append(f"{planet1_upper} in House {house2} WITH {house2_ruling_planet}")

            # 3. Together Check (Ruling Planet Logic Only)
            together_result = False
            together_type = ""

            # Get ruling planets of the house numbers where the planets are located
            planet1_house_ruling_planet = get_house_ruling_planet(planet1_house)
            planet2_house_ruling_planet = get_house_ruling_planet(planet2_house)

            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get where these ruling planets are located in the chart
                ruling_planet1_house = get_planet_house_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                ruling_planet2_house = get_planet_house_from_chart(chart_data, planet2_house_ruling_planet, chart_type)

                if ruling_planet1_house and ruling_planet2_house:
                    # Check if ruling planets are in the same house (together)
                    if ruling_planet1_house == ruling_planet2_house:
                        together_result = True
                        together_type = "ruling_planets_same_house"
                        relationship_found["together"] = True
                        together_types_found["ruling_planets_same_house"] = True
                        relationship_details["together"].append(f"{planet2_upper} (House {planet2_house}) and {planet1_upper} (House {planet1_house}) are together because their ruling planets {planet2_house_ruling_planet} and {planet1_house_ruling_planet} are both in House {ruling_planet1_house}")
                    else:
                        # Ruling planets are in different houses
                        together_result = False
                        together_type = "ruling_planets_different_house"
                        together_types_found["ruling_planets_different_house"] = True
                        relationship_details["together"].append(f"{planet2_upper} (House {planet2_house}) and {planet1_upper} (House {planet1_house}) are NOT together because their ruling planets {planet2_house_ruling_planet} (House {ruling_planet2_house}) and {planet1_house_ruling_planet} (House {ruling_planet1_house}) are in different houses")

            pair_results["together"] = together_result
            if together_result:
                pair_results["together_type"] = together_type

            # 4. Nakshatra Check (Ruling Planet Logic Only)
            # Check if ruling planet of house2 is in nakshatra ruled by ruling planet of house1
            planet1_house_ruling_planet = get_house_ruling_planet(planet1_house)
            planet2_house_ruling_planet = get_house_ruling_planet(planet2_house)

            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get nakshatra of house2's ruling planet
                ruling_planet2_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet2_house_ruling_planet, chart_type)
                if ruling_planet2_nakshatra:
                    ruling_planet2_nakshatra_lord = get_nakshatra_lord(ruling_planet2_nakshatra)
                    if ruling_planet2_nakshatra_lord == planet1_house_ruling_planet:
                        pair_results["nakshatra"] = True
                        relationship_found["nakshatra"] = True
                        relationship_details["nakshatra"].append(f"House {planet2_house} ruling planet {planet2_house_ruling_planet} is in {ruling_planet2_nakshatra} nakshatra (ruled by House {planet1_house} ruling planet {planet1_house_ruling_planet})")

                # Get nakshatra of house1's ruling planet
                ruling_planet1_nakshatra = get_planet_nakshatra_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                if ruling_planet1_nakshatra:
                    ruling_planet1_nakshatra_lord = get_nakshatra_lord(ruling_planet1_nakshatra)
                    if ruling_planet1_nakshatra_lord == planet2_house_ruling_planet:
                        pair_results["nakshatra"] = True
                        relationship_found["nakshatra"] = True
                        relationship_details["nakshatra"].append(f"House {planet1_house} ruling planet {planet1_house_ruling_planet} is in {ruling_planet1_nakshatra} nakshatra (ruled by House {planet2_house} ruling planet {planet2_house_ruling_planet})")

            # 5. Aspecting Check (Ruling Planet Logic Only)
            # Check if ruling planet of house2 aspects ruling planet of house1
            if planet1_house_ruling_planet and planet2_house_ruling_planet:
                # Get where ruling planets are located
                ruling_planet1_house = get_planet_house_from_chart(chart_data, planet1_house_ruling_planet, chart_type)
                ruling_planet2_house = get_planet_house_from_chart(chart_data, planet2_house_ruling_planet, chart_type)

                if ruling_planet1_house and ruling_planet2_house:
                    # Check if ruling planet2 aspects ruling planet1's house
                    ruling_planet2_aspects = get_planetary_aspects(planet2_house_ruling_planet, ruling_planet2_house)
                    if ruling_planet1_house in ruling_planet2_aspects:
                        pair_results["aspecting"] = True
                        relationship_found["aspecting"] = True
                        relationship_details["aspecting"].append(f"House {planet2_house} ruling planet {planet2_house_ruling_planet} (in House {ruling_planet2_house}) aspects House {ruling_planet1_house} (where House {planet1_house} ruling planet {planet1_house_ruling_planet} is)")

                    # Check if ruling planet1 aspects ruling planet2's house
                    ruling_planet1_aspects = get_planetary_aspects(planet1_house_ruling_planet, ruling_planet1_house)
                    if ruling_planet2_house in ruling_planet1_aspects:
                        pair_results["aspecting"] = True
                        relationship_found["aspecting"] = True
                        relationship_details["aspecting"].append(f"House {planet1_house} ruling planet {planet1_house_ruling_planet} (in House {ruling_planet1_house}) aspects House {ruling_planet2_house} (where House {planet2_house} ruling planet {planet2_house_ruling_planet} is)")

            results["planet_relationships"][pair_key] = pair_results

    # Set summary results
    results["summary"] = relationship_found
    results["summary"]["together_types"] = together_types_found

    # Set details
    for rel_type in relationship_details:
        if relationship_details[rel_type]:
            results["details"][rel_type] = "; ".join(relationship_details[rel_type])
        else:
            results["details"][rel_type] = f"No {rel_type.replace('_', ' ')} relationship found between House {house1} and House {house2} planets"

    # Overall result: TRUE if we have planets in both houses AND found relationships
    has_planets_in_both_houses = len(house1_planets) > 0 and len(house2_planets) > 0
    has_relationships = any(relationship_found.values())
    results["overall_result"] = has_planets_in_both_houses and has_relationships

    # Add comprehensive bidirectional analysis for house planet relationships
    forward_relationships = relationship_found.copy()
    forward_explanations = {}

    # Extract forward explanations from existing details
    for key in relationship_found.keys():
        if key in results["details"]:
            forward_explanations[key] = results["details"][key]
        else:
            forward_explanations[key] = f"Forward {key}: {relationship_found[key]}"

    # Reverse analysis: Check house2 planets to house1 planets
    reverse_relationships = {
        "basic_position": False,
        "with_ruling_planet": False,
        "together": False,
        "nakshatra": False,
        "aspecting": False
    }

    reverse_explanations = {
        "basic_position": "",
        "with_ruling_planet": "",
        "together": "",
        "nakshatra": "",
        "aspecting": ""
    }

    # Perform reverse analysis
    try:
        # Reverse basic position: Check if house2 planets are in house1
        reverse_basic = False
        reverse_basic_details = []
        for planet2 in house2_planets:
            if planet2 in house1_planets:
                reverse_basic = True
                reverse_basic_details.append(f"{planet2} from House {house2_num} found in House {house1_num}")

        reverse_relationships["basic_position"] = reverse_basic
        if reverse_basic:
            reverse_explanations["basic_position"] = f"TRUE: {'; '.join(reverse_basic_details)}"
        else:
            if not house2_planets:
                reverse_explanations["basic_position"] = f"FALSE: House {house2_num} is empty - no planets to check"
            elif not house1_planets:
                reverse_explanations["basic_position"] = f"FALSE: House {house1_num} is empty - no target location"
            else:
                reverse_explanations["basic_position"] = f"FALSE: No planets from House {house2_num} ({house2_planets}) found in House {house1_num} ({house1_planets})"

        # Reverse WITH ruling planet: Check house2 planets WITH house1 ruling planet
        reverse_with_ruling = False
        reverse_with_details = []
        house1_ruling_planet = get_house_ruling_planet(chart_data, house1_num, chart_type)

        if house1_ruling_planet:
            house1_ruling_location = get_planet_house_location(chart_data, house1_ruling_planet, chart_type)
            for planet2 in house2_planets:
                planet2_location = get_planet_house_location(chart_data, planet2, chart_type)
                if planet2_location == house1_ruling_location:
                    reverse_with_ruling = True
                    reverse_with_details.append(f"{planet2} (House {planet2_location}) WITH {house1_ruling_planet} (House {house1_ruling_location})")

        reverse_relationships["with_ruling_planet"] = reverse_with_ruling
        if reverse_with_ruling:
            reverse_explanations["with_ruling_planet"] = f"TRUE: {'; '.join(reverse_with_details)}"
        else:
            if not house2_planets:
                reverse_explanations["with_ruling_planet"] = f"FALSE: House {house2_num} is empty - no planets to check WITH ruling planet"
            else:
                reverse_explanations["with_ruling_planet"] = f"FALSE: No planets from House {house2_num} found WITH House {house1_num} ruling planet ({house1_ruling_planet})"

        # Reverse together: Same as forward (bidirectional by nature)
        reverse_relationships["together"] = relationship_found["together"]
        reverse_explanations["together"] = forward_explanations.get("together", "Together relationship") + " (bidirectional relationship)"

        # Reverse nakshatra and aspecting: Complex analysis
        reverse_relationships["nakshatra"] = False
        reverse_relationships["aspecting"] = False
        reverse_explanations["nakshatra"] = "FALSE: Reverse nakshatra analysis not implemented for house relationships"
        reverse_explanations["aspecting"] = "FALSE: Reverse aspecting analysis not implemented for house relationships"

    except Exception as e:
        # If reverse analysis fails, keep all as False with error explanation
        for key in reverse_relationships:
            reverse_relationships[key] = False
            reverse_explanations[key] = f"FALSE: Reverse analysis error - {str(e)}"

    # Calculate counts and scoring
    forward_count = sum(1 for val in forward_relationships.values() if val)
    reverse_count = sum(1 for val in reverse_relationships.values() if val)
    total_true_count = forward_count + reverse_count
    max_marks = len(forward_relationships) * 2  # Forward + Reverse = 2x marks possible
    success_rate = (total_true_count / max_marks * 100) if max_marks > 0 else 0

    # Update summary with bidirectional counts
    results["summary"]["total_relationship_types"] = len(forward_relationships)
    results["summary"]["forward_count"] = forward_count
    results["summary"]["reverse_count"] = reverse_count
    results["summary"]["total_true_count"] = total_true_count
    results["summary"]["total_marks"] = total_true_count
    results["summary"]["max_marks"] = max_marks
    results["summary"]["success_rate"] = round(success_rate, 1)

    # Add comprehensive bidirectional analysis
    results["bidirectional_analysis"] = {
        "forward_analysis": {
            "relationships": forward_relationships,
            "explanations": forward_explanations,
            "count": forward_count,
            "direction": f"House {house1_num} → House {house2_num}"
        },
        "reverse_analysis": {
            "relationships": reverse_relationships,
            "explanations": reverse_explanations,
            "count": reverse_count,
            "direction": f"House {house2_num} → House {house1_num}"
        },
        "combined_scoring": {
            "forward_points": forward_count,
            "reverse_points": reverse_count,
            "total_points": total_true_count,
            "max_possible_points": max_marks,
            "success_percentage": round(success_rate, 1),
            "calculation": f"Forward ({forward_count}) + Reverse ({reverse_count}) = Total ({total_true_count}) out of {max_marks} possible"
        }
    }

    # Add detailed scoring
    results["scoring"] = {
        "forward_marks": {
            "basic_position": 1 if forward_relationships["basic_position"] else 0,
            "with_ruling_planet": 1 if forward_relationships["with_ruling_planet"] else 0,
            "together": 1 if forward_relationships["together"] else 0,
            "nakshatra": 1 if forward_relationships["nakshatra"] else 0,
            "aspecting": 1 if forward_relationships["aspecting"] else 0
        },
        "reverse_marks": {
            "basic_position": 1 if reverse_relationships["basic_position"] else 0,
            "with_ruling_planet": 1 if reverse_relationships["with_ruling_planet"] else 0,
            "together": 1 if reverse_relationships["together"] else 0,
            "nakshatra": 1 if reverse_relationships["nakshatra"] else 0,
            "aspecting": 1 if reverse_relationships["aspecting"] else 0
        },
        "total_marks_earned": total_true_count,
        "total_marks_possible": max_marks,
        "success_percentage": round(success_rate, 1),
        "rating": get_success_rating(success_rate)
    }

    return results


def evaluate_parsed_query(parsed_query, planet_house_mapping, chart_data=None, chart_type="D1"):
    """
    Evaluate a parsed query against planet-house mapping.

    Args:
        parsed_query (list): Parsed query
        planet_house_mapping (dict): Mapping of planets to their houses
        chart_data (dict): Chart data from MongoDB (required for advanced rules)
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        bool: Result of the query evaluation
    """
    results = []

    for condition_type, condition_data in parsed_query:
        if condition_type == "SINGLE":
            planet, operator, value, cond_type = condition_data
            result = evaluate_condition(planet, operator, value, cond_type, planet_house_mapping, chart_data, chart_type)
            results.append(result)
        elif condition_type == "AND":
            and_results = []
            for planet, operator, value, cond_type in condition_data:
                and_result = evaluate_condition(planet, operator, value, cond_type, planet_house_mapping, chart_data, chart_type)
                and_results.append(and_result)
            results.append(all(and_results))

    # OR logic between all top-level conditions
    return any(results)


def evaluate_rule(query, user_profile_id, member_profile_id, chart_type="D1"):
    """
    Evaluate a rule for a specific user and member profile.

    Args:
        query (str): Rule query string
        user_profile_id (str or int): User profile ID
        member_profile_id (str or int): Member profile ID
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Evaluation result
    """
    try:
        # Validate inputs
        if not query or not isinstance(query, str):
            return {
                "success": False,
                "message": "Query must be a non-empty string"
            }

        if not user_profile_id or not member_profile_id:
            return {
                "success": False,
                "message": "Both user_profile_id and member_profile_id are required"
            }

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return {
                "success": False,
                "message": f"Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}",
                "error_code": "CHART_DATA_NOT_FOUND"
            }

        # Validate chart data structure
        if "chart_data" not in chart_data:
            return {
                "success": False,
                "message": f"Invalid chart data structure - missing 'chart_data' field",
                "error_code": "INVALID_CHART_STRUCTURE"
            }

        # Get planet-house mapping
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        if not planet_house_mapping:
            return {
                "success": False,
                "message": f"No planet data found in {chart_type} chart. Available charts: {list(chart_data.get('chart_data', {}).keys())}",
                "error_code": "NO_PLANET_DATA"
            }

        # Parse query
        parsed_query = parse_complex_query(query)
        if not parsed_query:
            return {
                "success": False,
                "message": "Invalid query format. Please check the query syntax.",
                "error_code": "INVALID_QUERY_FORMAT"
            }

        # Evaluate query
        result = evaluate_parsed_query(parsed_query, planet_house_mapping, chart_data, chart_type)

        return {
            "success": True,
            "query": query,
            "chart_type": chart_type,
            "result": result,
            "planet_positions": planet_house_mapping,
            "user_profile_id": user_profile_id,
            "member_profile_id": member_profile_id
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error evaluating rule: {str(e)}",
            "error_code": "EVALUATION_ERROR"
        }
