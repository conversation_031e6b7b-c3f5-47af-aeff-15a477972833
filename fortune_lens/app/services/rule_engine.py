"""
Rule Engine Service

This module provides functionality to evaluate complex astrological rules and conditions.
It supports logical operators (AND, OR) and comparison operators (IN, NOT IN, etc.).
Enhanced to support ruling planet relationships and planetary relationships.
"""

from bson import ObjectId
import re
from ..extensions import mongo
from ..config import BaseConfig
from .astrology.planetary_relationships import get_planet_relationship, is_planet_aspecting_planet

# House ruling planets mapping (1-based house numbers to planet names)
HOUSE_RULING_PLANETS = {
    1: 'MARS',      # Aries
    2: 'VENUS',     # Taurus
    3: 'MERCURY',   # Gemini
    4: 'MOON',      # Cancer
    5: 'SUN',       # <PERSON>
    6: 'MERCURY',   # Virgo
    7: 'VENUS',     # Libra
    8: 'MARS',      # <PERSON><PERSON><PERSON>
    9: 'JUPITER',   # <PERSON>gittar<PERSON>
    10: 'SATURN',   # Capricorn
    11: 'SATURN',   # Aquarius
    12: 'JUPITER'   # Pisces
}

# Planet name mappings for consistency
PLANET_NAMES = {
    'SUN': 'SUN',
    'MOON': 'MOON',
    'MARS': 'MARS',
    'MERCURY': 'MERCURY',
    'JUPITER': 'JUPITER',
    'VENUS': 'VENUS',
    'SATURN': 'SATURN',
    'RAHU': 'RAHU',
    'KETU': 'KETU'
}


def get_house_ruling_planet(house_number):
    """
    Get the ruling planet for a specific house.

    Args:
        house_number (int): House number (1-12)

    Returns:
        str: Ruling planet name
    """
    return HOUSE_RULING_PLANETS.get(house_number)


def get_chart_data(user_profile_id, member_profile_id):
    """
    Get chart data for a specific user and member profile.

    Args:
        user_profile_id (str or int): User profile ID
        member_profile_id (str or int): Member profile ID

    Returns:
        dict: Chart data or None if not found
    """
    # Convert to int if string and digit
    if isinstance(user_profile_id, str) and user_profile_id.isdigit():
        user_profile_id = int(user_profile_id)

    if isinstance(member_profile_id, str) and member_profile_id.isdigit():
        member_profile_id = int(member_profile_id)

    # First, try to find astro data by user_profile_id and member_profile_id directly
    astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
        "user_profile_id": user_profile_id,
        "member_profile_id": member_profile_id
    })

    if astro_data:
        return astro_data

    # If not found, try to find by member ObjectId
    member_profile = mongo.db[BaseConfig.MONGO_MEMBER_PROFILE_COLLECTION].find_one({
        "user_profile_id": user_profile_id,
        "member_profile_id": member_profile_id
    })

    if member_profile:
        member_id = member_profile["_id"]
        # Try to find astro data using member ObjectId
        astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
            "member_profile_id": member_id
        })

        if astro_data:
            return astro_data

    # If still not found, try alternative field combinations
    # Some records might use different field names
    astro_data = mongo.db[BaseConfig.MONGO_USER_MEMBER_ASTRO_PROFILE_DATA_COLLECTION].find_one({
        "$or": [
            {"user_profile_id": user_profile_id, "member_profile_id": member_profile_id},
            {"user_id": user_profile_id, "member_id": member_profile_id}
        ]
    })

    return astro_data


def get_planet_house_mapping(chart_data, chart_type="D1"):
    """
    Extract planet to house mapping from chart data.

    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Mapping of planets to their houses
    """
    if not chart_data or "chart_data" not in chart_data:
        return {}

    # Get the specified chart
    chart = chart_data["chart_data"].get(chart_type, {})
    if not chart:
        return {}

    # Create planet to house mapping
    planet_house_mapping = {}

    # Handle different chart data structures
    if "houses" in chart:
        # New structure: houses array with planets list
        for house in chart["houses"]:
            house_number = house.get("house_number")
            planets = house.get("planets", [])

            if house_number and planets:
                for planet_name in planets:
                    if isinstance(planet_name, str):
                        # Normalize planet names to uppercase
                        normalized_planet = normalize_planet_name(planet_name)
                        if normalized_planet:
                            planet_house_mapping[normalized_planet] = house_number

    # If no planets found in houses structure, try alternative structures
    if not planet_house_mapping:
        # Try to extract from planets_precise or other structures
        planets_precise = chart.get("planets_precise", {})
        if planets_precise:
            # This structure doesn't directly give house numbers,
            # so we need to map signs to houses using lagna
            lagna_info = chart.get("lagna", {})
            if lagna_info and "sign" in lagna_info:
                lagna_sign = lagna_info["sign"]
                # Map planets based on their signs relative to lagna
                planet_house_mapping = map_planets_by_signs(planets_precise, lagna_sign)

    return planet_house_mapping


def normalize_planet_name(planet_name):
    """
    Normalize planet names to standard uppercase format.

    Args:
        planet_name (str): Planet name in any format

    Returns:
        str: Normalized planet name or None if invalid
    """
    if not isinstance(planet_name, str):
        return None

    # Convert to uppercase and strip whitespace
    planet = planet_name.upper().strip()

    # Handle common variations
    planet_mapping = {
        'SUN': 'SUN',
        'MOON': 'MOON',
        'MARS': 'MARS',
        'MERCURY': 'MERCURY',
        'JUPITER': 'JUPITER',
        'VENUS': 'VENUS',
        'SATURN': 'SATURN',
        'RAHU': 'RAHU',
        'KETU': 'KETU',
        'LAGNAM': 'LAGNA',
        'LAGNA': 'LAGNA'
    }

    return planet_mapping.get(planet, planet if planet in PLANET_NAMES else None)


def map_planets_by_signs(planets_precise, lagna_sign):
    """
    Map planets to houses based on their signs and lagna sign.

    Args:
        planets_precise (dict): Planet positions with signs
        lagna_sign (str): Lagna sign

    Returns:
        dict: Planet to house mapping
    """
    # Sign to number mapping
    sign_numbers = {
        'MESHAM': 1, 'RISHABAM': 2, 'MIDUNAM': 3, 'KADAGAM': 4,
        'SIMMAM': 5, 'KANNI': 6, 'THULAM': 7, 'VIRICHIGAM': 8,
        'DHANUSU': 9, 'MAGARAM': 10, 'KUMBAM': 11, 'MEENAM': 12
    }

    # Get lagna sign number
    lagna_number = sign_numbers.get(lagna_sign.upper(), 1)

    planet_house_mapping = {}

    for planet, planet_info in planets_precise.items():
        if isinstance(planet_info, dict) and "sign" in planet_info:
            planet_sign = planet_info["sign"].upper()
            planet_sign_number = sign_numbers.get(planet_sign, 1)

            # Calculate house number relative to lagna
            house_number = ((planet_sign_number - lagna_number) % 12) + 1

            normalized_planet = normalize_planet_name(planet)
            if normalized_planet:
                planet_house_mapping[normalized_planet] = house_number

    return planet_house_mapping


def debug_chart_structure(chart_data, chart_type="D1"):
    """
    Debug function to analyze chart data structure.

    Args:
        chart_data (dict): Chart data from MongoDB
        chart_type (str): Chart type to analyze

    Returns:
        dict: Debug information about the chart structure
    """
    debug_info = {
        "chart_data_exists": "chart_data" in chart_data if chart_data else False,
        "chart_type_exists": False,
        "chart_structure": {},
        "available_charts": [],
        "houses_structure": {},
        "planets_found": []
    }

    if not chart_data:
        debug_info["error"] = "No chart data provided"
        return debug_info

    if "chart_data" not in chart_data:
        debug_info["error"] = "Missing 'chart_data' field"
        return debug_info

    chart_data_obj = chart_data["chart_data"]
    debug_info["available_charts"] = list(chart_data_obj.keys())

    if chart_type in chart_data_obj:
        debug_info["chart_type_exists"] = True
        chart = chart_data_obj[chart_type]
        debug_info["chart_structure"] = {
            "keys": list(chart.keys()),
            "has_houses": "houses" in chart,
            "has_planets_precise": "planets_precise" in chart,
            "has_lagna": "lagna" in chart
        }

        if "houses" in chart:
            houses = chart["houses"]
            debug_info["houses_structure"] = {
                "houses_count": len(houses),
                "sample_house": houses[0] if houses else None,
                "house_keys": list(houses[0].keys()) if houses else []
            }

            # Extract all planets found
            for house in houses:
                planets = house.get("planets", [])
                debug_info["planets_found"].extend(planets)

            debug_info["planets_found"] = list(set(debug_info["planets_found"]))

    return debug_info


def parse_condition(condition):
    """
    Parse a single condition. Supports multiple formats:
    - "Moon IN House1"
    - "Ketu IN House6 WITH Ruling_Planet"
    - "Ketu IS RELATED TO House6_Ruling_Planet"
    - "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet"

    Args:
        condition (str): Condition string

    Returns:
        tuple: (planet, operator, value, condition_type)
        condition_type can be: 'BASIC', 'WITH_RULING_PLANET', 'RELATED_TO_RULING_PLANET', 'ASPECTING_BIRTH_RULING_PLANET'
    """
    condition = condition.strip()

    # Pattern 1: Basic house placement - "Planet IN/NOT IN House#"
    basic_pattern = r'([A-Za-z]+)\s+(IN|NOT IN)\s+House(\d+)$'
    match = re.match(basic_pattern, condition, re.IGNORECASE)
    if match:
        planet, operator, house_num = match.groups()
        return planet.upper(), operator.upper(), int(house_num), 'BASIC'

    # Pattern 2: Planet with ruling planet - "Planet IN House# WITH Ruling_Planet"
    with_ruling_pattern = r'([A-Za-z]+)\s+(IN|NOT IN)\s+House(\d+)\s+WITH\s+Ruling_Planet'
    match = re.match(with_ruling_pattern, condition, re.IGNORECASE)
    if match:
        planet, operator, house_num = match.groups()
        return planet.upper(), operator.upper(), int(house_num), 'WITH_RULING_PLANET'

    # Pattern 3: Planet relationship to house ruling planet - "Planet IS RELATED TO House#_Ruling_Planet"
    related_pattern = r'([A-Za-z]+)\s+IS\s+RELATED\s+TO\s+House(\d+)_Ruling_Planet'
    match = re.match(related_pattern, condition, re.IGNORECASE)
    if match:
        planet, house_num = match.groups()
        return planet.upper(), 'IS_RELATED_TO', int(house_num), 'RELATED_TO_RULING_PLANET'

    # Pattern 4: Planet aspecting house ruling planet - "Planet IS ASPECTING_BIRTH House#_Ruling_Planet"
    aspecting_pattern = r'([A-Za-z]+)\s+IS\s+ASPECTING_BIRTH\s+House(\d+)_Ruling_Planet'
    match = re.match(aspecting_pattern, condition, re.IGNORECASE)
    if match:
        planet, house_num = match.groups()
        return planet.upper(), 'IS_ASPECTING_BIRTH', int(house_num), 'ASPECTING_BIRTH_RULING_PLANET'

    return None, None, None, None


def parse_complex_query(query):
    """
    Parse a complex query with logical operators.

    Args:
        query (str): Complex query string

    Returns:
        list: List of parsed conditions with logical operators
    """
    # Split by OR
    or_parts = [part.strip() for part in query.split(" OR ")]

    parsed_query = []
    for or_part in or_parts:
        # Split by AND
        and_parts = [part.strip() for part in or_part.split(" AND ")]

        if len(and_parts) > 1:
            # Multiple AND conditions
            and_conditions = []
            for and_part in and_parts:
                result = parse_condition(and_part)
                if len(result) == 4 and result[0] and result[1] and result[2] is not None:
                    planet, operator, value, condition_type = result
                    and_conditions.append((planet, operator, value, condition_type))

            if and_conditions:
                parsed_query.append(("AND", and_conditions))
        else:
            # Single condition
            result = parse_condition(or_part)
            if len(result) == 4 and result[0] and result[1] and result[2] is not None:
                planet, operator, value, condition_type = result
                parsed_query.append(("SINGLE", (planet, operator, value, condition_type)))

    return parsed_query


def evaluate_condition(planet, operator, value, condition_type, planet_house_mapping):
    """
    Evaluate a single condition with enhanced support for ruling planets, relationships, and aspects.

    Args:
        planet (str): Planet name
        operator (str): Operator (IN, NOT IN, IS_RELATED_TO, IS_ASPECTING_BIRTH)
        value (int): House number
        condition_type (str): Type of condition (BASIC, WITH_RULING_PLANET, RELATED_TO_RULING_PLANET, ASPECTING_BIRTH_RULING_PLANET)
        planet_house_mapping (dict): Mapping of planets to their houses

    Returns:
        bool: Result of the condition evaluation
    """
    if planet not in planet_house_mapping:
        return False

    planet_house = planet_house_mapping[planet]

    if condition_type == 'BASIC':
        # Basic house placement check
        if operator == "IN":
            return planet_house == value
        elif operator == "NOT IN":
            return planet_house != value

    elif condition_type == 'WITH_RULING_PLANET':
        # Check if planet is in the house AND with its ruling planet
        if operator == "IN":
            # First check if planet is in the specified house
            if planet_house != value:
                return False

            # Then check if the ruling planet of that house is also in the same house
            house_ruling_planet = get_house_ruling_planet(value)
            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                return ruling_planet_house == value
            return False

        elif operator == "NOT IN":
            # Planet is not in the house OR not with its ruling planet
            if planet_house != value:
                return True

            house_ruling_planet = get_house_ruling_planet(value)
            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                return ruling_planet_house != value
            return True

    elif condition_type == 'RELATED_TO_RULING_PLANET':
        # Check if planet has a relationship (friend/enemy/neutral) with the ruling planet of the specified house
        if operator == "IS_RELATED_TO":
            house_ruling_planet = get_house_ruling_planet(value)
            if house_ruling_planet:
                relationship = get_planet_relationship(planet, house_ruling_planet)
                # Consider any relationship (friend, enemy, or neutral) as "related"
                return relationship in ['FRIEND', 'ENEMY', 'NEUTRAL']
            return False

    elif condition_type == 'ASPECTING_BIRTH_RULING_PLANET':
        # Check if planet is aspecting the ruling planet of the specified house
        if operator == "IS_ASPECTING_BIRTH":
            house_ruling_planet = get_house_ruling_planet(value)
            if house_ruling_planet and house_ruling_planet in planet_house_mapping:
                ruling_planet_house = planet_house_mapping[house_ruling_planet]
                # Check if the planet is aspecting the ruling planet
                return is_planet_aspecting_planet(planet, planet_house, house_ruling_planet, ruling_planet_house)
            return False

    return False


def evaluate_parsed_query(parsed_query, planet_house_mapping):
    """
    Evaluate a parsed query against planet-house mapping.

    Args:
        parsed_query (list): Parsed query
        planet_house_mapping (dict): Mapping of planets to their houses

    Returns:
        bool: Result of the query evaluation
    """
    results = []

    for condition_type, condition_data in parsed_query:
        if condition_type == "SINGLE":
            planet, operator, value, cond_type = condition_data
            result = evaluate_condition(planet, operator, value, cond_type, planet_house_mapping)
            results.append(result)
        elif condition_type == "AND":
            and_results = []
            for planet, operator, value, cond_type in condition_data:
                and_result = evaluate_condition(planet, operator, value, cond_type, planet_house_mapping)
                and_results.append(and_result)
            results.append(all(and_results))

    # OR logic between all top-level conditions
    return any(results)


def evaluate_rule(query, user_profile_id, member_profile_id, chart_type="D1"):
    """
    Evaluate a rule for a specific user and member profile.

    Args:
        query (str): Rule query string
        user_profile_id (str or int): User profile ID
        member_profile_id (str or int): Member profile ID
        chart_type (str): Chart type (D1, D9, etc.)

    Returns:
        dict: Evaluation result
    """
    try:
        # Validate inputs
        if not query or not isinstance(query, str):
            return {
                "success": False,
                "message": "Query must be a non-empty string"
            }

        if not user_profile_id or not member_profile_id:
            return {
                "success": False,
                "message": "Both user_profile_id and member_profile_id are required"
            }

        # Get chart data
        chart_data = get_chart_data(user_profile_id, member_profile_id)
        if not chart_data:
            return {
                "success": False,
                "message": f"Chart data not found for user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}",
                "error_code": "CHART_DATA_NOT_FOUND"
            }

        # Validate chart data structure
        if "chart_data" not in chart_data:
            return {
                "success": False,
                "message": f"Invalid chart data structure - missing 'chart_data' field",
                "error_code": "INVALID_CHART_STRUCTURE"
            }

        # Get planet-house mapping
        planet_house_mapping = get_planet_house_mapping(chart_data, chart_type)
        if not planet_house_mapping:
            return {
                "success": False,
                "message": f"No planet data found in {chart_type} chart. Available charts: {list(chart_data.get('chart_data', {}).keys())}",
                "error_code": "NO_PLANET_DATA"
            }

        # Parse query
        parsed_query = parse_complex_query(query)
        if not parsed_query:
            return {
                "success": False,
                "message": "Invalid query format. Please check the query syntax.",
                "error_code": "INVALID_QUERY_FORMAT"
            }

        # Evaluate query
        result = evaluate_parsed_query(parsed_query, planet_house_mapping)

        return {
            "success": True,
            "query": query,
            "chart_type": chart_type,
            "result": result,
            "planet_positions": planet_house_mapping,
            "user_profile_id": user_profile_id,
            "member_profile_id": member_profile_id
        }

    except Exception as e:
        return {
            "success": False,
            "message": f"Error evaluating rule: {str(e)}",
            "error_code": "EVALUATION_ERROR"
        }
