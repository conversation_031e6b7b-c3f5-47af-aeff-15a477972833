"""
OTP Service
"""

import random
import string
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from datetime import datetime, timedelta
from twilio.rest import Client

from ..extensions import mongo
from ..config import BaseConfig
from ..constants import OtpConfig, Collection, Field, ResponseMessage


class OTPService:
    """Service for generating and validating OTPs"""

    OTP_COLLECTION = Collection.OTP
    OTP_EXPIRY_MINUTES = OtpConfig.EXPIRY_MINUTES

    @staticmethod
    def generate_otp(length=OtpConfig.LENGTH):
        """
        Generate a random OTP

        Args:
            length (int): Length of the OTP

        Returns:
            str: Generated OTP
        """
        # Generate a numeric OTP
        return ''.join(random.choices(string.digits, k=length))

    @staticmethod
    def save_otp(email, otp_type=OtpConfig.TYPE_REGISTRATION):
        """
        Generate and save an OTP for email verification

        Args:
            email (str): Email address
            otp_type (str): Type of OTP (registration, login, reset_password)

        Returns:
            dict: OTP details
        """
        if not email:
            raise ValueError("Email must be provided")

        # Create OTPs collection if it doesn't exist
        if OTPService.OTP_COLLECTION not in mongo.db.list_collection_names():
            mongo.db.create_collection(OTPService.OTP_COLLECTION)

        # Generate OTP
        otp = OTPService.generate_otp()

        # Calculate expiry time
        expiry_time = datetime.utcnow() + timedelta(minutes=OTPService.OTP_EXPIRY_MINUTES)

        # Create OTP document
        otp_doc = {
            Field.OTP: otp,
            Field.EMAIL: email,
            Field.TYPE: otp_type,
            Field.CREATED_AT: datetime.utcnow(),
            Field.EXPIRES_AT: expiry_time,
            Field.VERIFIED: False
        }

        # Save OTP to database
        mongo.db[OTPService.OTP_COLLECTION].insert_one(otp_doc)

        return {
            Field.OTP: otp,
            Field.EMAIL: email,
            Field.EXPIRES_AT: expiry_time
        }

    @staticmethod
    def verify_otp(otp, email, otp_type=OtpConfig.TYPE_REGISTRATION):
        """
        Verify an OTP

        Args:
            otp (str): OTP to verify
            email (str): Email address
            otp_type (str): Type of OTP (registration, login, reset_password)

        Returns:
            bool: True if OTP is valid, False otherwise
        """
        if not email:
            return False

        # Create query
        query = {
            Field.OTP: otp,
            Field.EMAIL: email,
            Field.TYPE: otp_type,
            Field.VERIFIED: False,
            Field.EXPIRES_AT: {'$gt': datetime.utcnow()}  # OTP not expired
        }

        # Find OTP
        otp_doc = mongo.db[OTPService.OTP_COLLECTION].find_one(query)

        if not otp_doc:
            return False

        # Mark OTP as verified
        mongo.db[OTPService.OTP_COLLECTION].update_one(
            {Field.ID: otp_doc[Field.ID]},
            {'$set': {Field.VERIFIED: True}}
        )

        return True

    @staticmethod
    def send_email_otp(email, otp):
        """
        Send OTP via email using SMTP

        Args:
            email (str): Email address
            otp (str): OTP to send

        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            # Create message container
            msg = MIMEMultipart('alternative')
            msg['Subject'] = OtpConfig.EMAIL_SUBJECT
            msg['From'] = BaseConfig.MAIL_DEFAULT_SENDER
            msg['To'] = email

            # Create the body of the message
            html = f"""
            <html>
              <body>
                <h2>Your OTP for Fortune Lens</h2>
                <p>Your One-Time Password (OTP) is: <strong>{otp}</strong></p>
                <p>This OTP is valid for {OTPService.OTP_EXPIRY_MINUTES} minutes.</p>
                <p>If you did not request this OTP, please ignore this email.</p>
              </body>
            </html>
            """

            # Record the MIME type - text/html
            part = MIMEText(html, 'html')

            # Attach parts into message container
            msg.attach(part)

            # Send the message via SMTP server
            server = smtplib.SMTP(BaseConfig.MAIL_SERVER, BaseConfig.MAIL_PORT)
            server.ehlo()
            if BaseConfig.MAIL_USE_TLS:
                server.starttls()

            # Login if credentials are provided
            if BaseConfig.MAIL_USERNAME and BaseConfig.MAIL_PASSWORD:
                server.login(BaseConfig.MAIL_USERNAME, BaseConfig.MAIL_PASSWORD)

            # Send email
            server.sendmail(BaseConfig.MAIL_DEFAULT_SENDER, email, msg.as_string())
            server.quit()

            print(f"Email OTP sent to {email}")
            return True
        except Exception as e:
            print(f"Error sending email OTP: {str(e)}")
            # For development, still print the OTP to console
            print(f"OTP for {email}: {otp}")
            return False

    @staticmethod
    def send_sms_otp(mobile, otp):
        """
        Send OTP via SMS using Twilio

        Args:
            mobile (str): Mobile number
            otp (str): OTP to send

        Returns:
            bool: True if SMS sent successfully, False otherwise
        """
        try:
            # Check if Twilio credentials are configured
            if (BaseConfig.TWILIO_ACCOUNT_SID == 'your-account-sid' or
                BaseConfig.TWILIO_AUTH_TOKEN == 'your-auth-token' or
                BaseConfig.TWILIO_PHONE_NUMBER == 'your-twilio-phone-number'):
                # If not configured, just print the OTP to console
                print(f"Twilio not configured. OTP for {mobile}: {otp}")
                return True

            # Initialize Twilio client
            client = Client(BaseConfig.TWILIO_ACCOUNT_SID, BaseConfig.TWILIO_AUTH_TOKEN)

            # Format mobile number with country code if not present
            if not mobile.startswith('+'):
                mobile = f"+91{mobile}"  # Assuming India country code

            # Send SMS
            message = client.messages.create(
                body=f"Your Fortune Lens OTP is: {otp}. Valid for {OTPService.OTP_EXPIRY_MINUTES} minutes.",
                from_=BaseConfig.TWILIO_PHONE_NUMBER,
                to=mobile
            )

            print(f"SMS OTP sent to {mobile} with SID: {message.sid}")
            return True
        except Exception as e:
            print(f"Error sending SMS OTP: {str(e)}")
            # For development, still print the OTP to console
            print(f"OTP for {mobile}: {otp}")
            return False
