# Career Prediction Services

This package provides services for predicting career paths and life events based on astrological charts.

## Medical Profession Prediction

The medical profession prediction service analyzes a person's birth chart to determine if they have potential for a medical profession based on Vedic astrology principles.

### Rules

The prediction is based on three main rules:

1. **Rule 1**: Analyzes Moon placements in specific houses (1, 3, 6, 10, 11) from Lagna.

2. **Rule 2**: Analyzes Ketu placements and relationships with 6th and 10th house lords:
   - Rule 2.1: Checks if <PERSON><PERSON> is placed in the 6th or 10th house
   - Rule 2.2: Checks if <PERSON><PERSON> is placed with the 6th or 10th house ruling planet
   - Rule 2.3-2.5: Additional checks for <PERSON><PERSON>'s relationships with other planets

3. **Rule 3**: Analyzes Mars placements and relationships with 4th and 10th house lords:
   - Rule 3.1: Checks if <PERSON> is placed in the 4th or 10th house
   - Rule 3.2: Checks if <PERSON> is placed with the 4th or 10th house ruling planet
   - Rule 3.3-3.5: Additional checks for Mars's relationships with other planets

### Usage

To use the medical profession prediction service, call the `predict_medical_profession` function with a member ID:

```python
from app.services.career_prediction.medical_profession import predict_medical_profession

# Predict medical profession potential for a member
result = predict_medical_profession(member_id)

# Print the result
print(f"Medical Potential: {result['medical_potential']}")
print(f"Overall Percentage: {result['overall_percentage']}%")
```

### API Endpoints

#### Medical Profession Prediction

The medical profession prediction service is accessible through the following API endpoint:

```
POST /api/career-prediction/medical-profession
```

Request body:
```json
{
  "member_id": "member_profile_id",
  "print_output": false,
  "use_excel_data": false
}
```

#### Compare Results

To compare results between MongoDB and Excel data (especially for user_profile_id 1 and self member, which should match user_id 100001 in Excel):

```
POST /api/career-prediction/compare-results
```

Request body:
```json
{
  "member_id": "member_profile_id"
}
```

Response:
```json
{
  "success": true,
  "member": {
    "id": "member_id",
    "name": "Member Name",
    "birth_date": "YYYY-MM-DD",
    "birth_time": "HH:MM:SS",
    "birth_place": "Birth Place"
  },
  "chart_path": "/path/to/chart.png",
  "pdf_path": "/path/to/report.pdf",
  "point_table_path": "/path/to/point_table.xlsx",
  "rules": {
    "Rule 1": {
      "status": "pass",
      "percentage": 100.0,
      "details": {...}
    },
    "Rule 2": {
      "status": "pass",
      "percentage": 75.0,
      "details": {...}
    },
    "Rule 3": {
      "status": "pass",
      "percentage": 80.0,
      "details": {...}
    }
  },
  "overall_percentage": 85.0,
  "medical_potential": "High",
  "generated_at": "YYYY-MM-DDTHH:MM:SS"
}
```

### Output Files

The service generates the following output files:

1. **Chart Image**: A South Indian style astrological chart saved as a PNG file.
2. **PDF Report**: A detailed report of the astrological analysis.
3. **Excel Point Table**: A table showing the points scored for each rule.

These files are saved in the `charts` and `outputs` directories in the application root.

## Foreign Travel Prediction

The foreign travel prediction service analyzes a person's birth chart to determine if they have potential for foreign travel based on Vedic astrology principles.

### Rules

The prediction is based on three main rules:

1. **Rule 3.1**: Analyzes planets in houses 9 and 12:
   - Checks if planets in houses 9 and 12 are associated with Rahu or Ketu
   - Checks if the ruling planets of houses 9 and 12 appear in the person's dasha periods

2. **Rule 3.2**: Analyzes planets in houses 8 and 12:
   - Checks if planets in houses 8 and 12 are associated with Rahu or Ketu
   - Checks if the ruling planets of houses 8 and 12 appear in the person's dasha periods

3. **Rule 3.3**: Analyzes planets in houses 6, 7, and 12:
   - Checks if the ruling planets of houses 6, 7, and 12 appear in the person's dasha periods

### Usage

To use the foreign travel prediction service, call the `predict_foreign_travel` function with user_profile_id and member_profile_id:

```python
from app.services.career_prediction.foreign_travel import predict_foreign_travel

# Predict foreign travel potential for a member
result = predict_foreign_travel(
    user_profile_id='1',
    member_profile_id='1',
    foreign_travel_date='2023-05-15'  # Optional
)

# Print the result
print(f"Foreign Travel Potential: {result['foreign_travel_potential']}")
print(f"Overall Percentage: {result['overall_percentage']}%")
```

### API Endpoints

#### Foreign Travel Prediction

The foreign travel prediction service is accessible through the following API endpoint:

```
POST /api/career-prediction/foreign-travel
```

Request body:
```json
{
  "user_profile_id": "1",
  "member_profile_id": "1",
  "foreign_travel_date": "2023-05-15",  // Optional
  "print_output": false  // Optional
}
```

Response:
```json
{
  "success": true,
  "user_profile_id": "1",
  "member_profile_id": "1",
  "foreign_travel_date": "2023-05-15",
  "chart_path": "/path/to/chart.png",
  "rule_results": {
    "Rule 3.1": true,
    "Rule 3.1_count": 2,
    "Rule 3.2": false,
    "Rule 3.2_count": 0,
    "Rule 3.3": true,
    "Rule 3.3_count": 1
  },
  "overall_percentage": 66.67,
  "foreign_travel_potential": "High",
  "detailed_results": {...}  // Only if print_output=true
}
```

### Output Files

The service generates the following output files:

1. **Chart Image**: A South Indian style astrological chart saved as a PNG file.

These files are saved in the `charts` directory in the application root.
