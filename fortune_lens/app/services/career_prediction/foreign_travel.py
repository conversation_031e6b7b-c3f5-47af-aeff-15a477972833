"""
Foreign Travel Prediction Service

This module provides functionality to predict potential foreign travel periods
based on astrological factors in Vedic astrology.
"""

import os
import pandas as pd
from bson import ObjectId
from datetime import datetime
import matplotlib.pyplot as plt
import random
import string
import sys
from datetime import datetime, timedelta

from ...extensions import mongo
from ...config import BaseConfig
from ...services.astrology.planetary_relationships import (
    is_planet_exalted,
    is_planet_debilitated,
    get_planet_relationship,
    is_planet_aspecting_house,
    is_planet_aspecting_planet
)

# Constants
EXCEL_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))), 'Fortune<PERSON>ens')
CHART_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'charts')

# Ensure chart directory exists
os.makedirs(CHART_DIR, exist_ok=True)

# Define zodiac signs
raasi_list = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>mma<PERSON>', '<PERSON>nn<PERSON>',
              '<PERSON>hul<PERSON>', '<PERSON>irichigam', '<PERSON>hanusu', 'Magaram', 'Kumbam', 'Meenam']

def generate_random_string(length=5):
    """
    Generate a random string of fixed length.

    Args:
        length (int): Length of the random string

    Returns:
        str: Random string
    """
    letters = string.ascii_uppercase + string.digits
    return ''.join(random.choice(letters) for _ in range(length))

def parse_datetime_to_date(date_str):
    """
    Parse a datetime string to a date object.

    Args:
        date_str (str): Datetime string

    Returns:
        datetime.date: Date object
    """
    date_str = str(date_str).strip()
    if len(date_str) == 10:
        date_str += " 00:00:00"
    date_str = date_str.replace(" AM", "").replace(" PM", "")
    try:
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S").date()
    except ValueError:
        try:
            return datetime.strptime(date_str, "%d-%m-%Y %H:%M:%S").date()
        except ValueError:
            print(f"Error: Unable to parse the datetime string '{date_str}'")
            return None

def get_all_astro_data():
    """
    Get all astrological data from MongoDB.

    Returns:
        dict: Dictionary containing all astrological data
    """
    # Get data from MongoDB collections
    house_name_data = list(mongo.db.astro_house_names.find())
    planets_exalt_debilitate_data = list(mongo.db.astro_planets_exalt_debilitate.find())
    star_data = list(mongo.db.astro_stars.find())
    planets_friends_neutral_enemies_data = list(mongo.db.astro_planets_relationships.find())
    planets_aspects_data = list(mongo.db.astro_planets_aspects.find())

    # Convert to DataFrames
    house_name_df = pd.DataFrame(house_name_data)
    planets_exalt_debilitate_df = pd.DataFrame(planets_exalt_debilitate_data)
    star_df = pd.DataFrame(star_data)
    planets_friends_neutral_enemies_df = pd.DataFrame(planets_friends_neutral_enemies_data)
    planets_aspects_df = pd.DataFrame(planets_aspects_data)

    # Return as dictionary
    return {
        'house_name': house_name_df,
        'Planets_exalt_debilitate': planets_exalt_debilitate_df,
        'star': star_df,
        'planets_friends_neutral_enemies': planets_friends_neutral_enemies_df,
        'planets_aspects': planets_aspects_df
    }

def get_member_data(user_profile_id, member_profile_id):
    """
    Get member data from MongoDB.

    Args:
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID

    Returns:
        tuple: Tuple containing member data and astro data
    """
    # Convert string IDs to integers if they're strings
    try:
        user_profile_id_int = int(user_profile_id)
        member_profile_id_int = int(member_profile_id)
    except (ValueError, TypeError):
        return None, None

    # Find member profile
    member_profile = mongo.db.member_profile.find_one({
        'user_profile_id': user_profile_id_int,
        'member_profile_id': member_profile_id_int
    })

    if not member_profile:
        return None, None

    # Find astro profile
    astro_profile = mongo.db.user_member_astro_profile_data.find_one({
        'user_profile_id': user_profile_id_int,
        'member_profile_id': member_profile_id_int
    })

    return member_profile, astro_profile

def find_planet_placements(user_profile_id, member_profile_id):
    """
    Find planet placements for a member.

    Args:
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID

    Returns:
        dict: Dictionary containing planet placements
    """
    # Get member data and astro profile
    member_profile, astro_profile = get_member_data(user_profile_id, member_profile_id)

    if not member_profile or not astro_profile:
        return None

    # Check if we have D1 chart data
    if 'chart_data' in astro_profile and 'D1' in astro_profile['chart_data']:
        d1_data = astro_profile['chart_data']['D1']

        # Extract planet positions from D1 houses
        planet_positions = {}
        house_names = {}

        if 'houses' in d1_data:
            for house in d1_data['houses']:
                house_num = house.get('house_number')
                house_name = house.get('house_name')
                planets = house.get('planets', [])

                if house_num and planets:
                    # Format: "PLANET1, PLANET2, ..."
                    planet_positions[str(house_num)] = ', '.join([p.upper() for p in planets])

                if house_num and house_name:
                    house_names[f'house_name_{house_num}'] = house_name.upper()

        # Extract star data from D1 houses
        star_data = {}
        for house in d1_data.get('houses', []):
            planet_nakshatras = house.get('planet_nakshatras', {})
            for planet, nakshatra in planet_nakshatras.items():
                star_data[f'{planet}_star'] = nakshatra

        # Extract dasha data
        dasha_data = {}
        if 'dashas' in d1_data:
            # Extract bhukti_dasha data from chart_data
            bhukti_dasha = d1_data['dashas'].get('bhukti_dasha', [])
            if bhukti_dasha:
                # Convert bhukti_dasha format to antara_dhasa_period format
                antara_dhasa_period = []
                for entry in bhukti_dasha:
                    # Parse the entry format: (planet1-planet2, start_date, end_date)
                    if isinstance(entry, str) and entry.startswith('(') and entry.endswith(')'):
                        parts = entry[1:-1].split(', ')
                        if len(parts) == 3:
                            planets, start_date, end_date = parts
                            # Format: "planet1-planet2: start_date to end_date;"
                            antara_dhasa_period.append(f"{planets}: {start_date} to {end_date};")

                if antara_dhasa_period:
                    dasha_data = {'antara_dhasa_period': ' '.join(antara_dhasa_period)}
    else:
        # Fallback to old format if D1 chart data is not available
        # Extract planet positions
        planet_positions = {}
        for house_num in range(1, 13):
            house_key = f'house_{house_num}'
            if house_key in astro_profile and astro_profile[house_key]:
                planet_positions[str(house_num)] = astro_profile[house_key]

        # Extract house names
        house_names = {}
        for house_num in range(1, 13):
            house_name_key = f'house_name_{house_num}'
            if house_name_key in astro_profile and astro_profile[house_name_key]:
                house_names[house_name_key] = astro_profile[house_name_key]

        # Extract star data
        star_data = {}
        for planet in ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu', 'lagnam']:
            star_key = f'{planet}_star'
            if star_key in astro_profile and astro_profile[star_key]:
                star_data[star_key] = astro_profile[star_key]

        # Extract dasha data
        dasha_data = {}
        if 'dasha' in astro_profile and astro_profile['dasha']:
            dasha_data = astro_profile['dasha']
        elif 'antara_dhasa_period' in astro_profile and astro_profile['antara_dhasa_period']:
            dasha_data = {'antara_dhasa_period': astro_profile['antara_dhasa_period']}

    # Return placements
    return {
        'planet_house': planet_positions,
        'house_name': house_names,
        'star': star_data,
        'dhasa': dasha_data,
        'birth_details': {
            'birth_date': member_profile.get('birth_date', ''),
            'birth_time': member_profile.get('birth_time', ''),
            'birth_place': member_profile.get('birth_place', '')
        }
    }

def find_houses_names(df, house_indices):
    """
    Find house names for given house indices.

    Args:
        df (pd.DataFrame or dict): DataFrame or dictionary containing house information
        house_indices (list): List of house indices

    Returns:
        list: List of house names
    """
    house_names = []

    if isinstance(df, pd.DataFrame):
        for idx in house_indices:
            col_name = f'house_name_{idx}'
            if col_name in df.columns:
                house_name = df[col_name].iloc[0]
                house_names.append(house_name)
    elif isinstance(df, dict):
        for idx in house_indices:
            col_name = f'house_name_{idx}'
            if col_name in df:
                house_name = df[col_name]
                house_names.append(house_name)

    return house_names

def find_rasi(planet_house, house_name):
    """
    Find the Rasi (zodiac sign) where the Moon is located.

    Args:
        planet_house (dict): Dictionary mapping houses to planets
        house_name (dict): Dictionary mapping house positions to zodiac signs

    Returns:
        str: Rasi where the Moon is located
    """
    moon_house = None
    for house, planets in planet_house.items():
        if 'MOON' in planets:
            moon_house = house
            break

    if moon_house:
        house_key = f'house_name_{moon_house}'
        return house_name.get(house_key, '')

    return ''

def parse_to_tuples(period_str):
    """
    Parse dasha period string to tuples.

    Args:
        period_str (str): Dasha period string

    Returns:
        list: List of tuples containing period, start date, and end date
    """
    if not period_str:
        return []

    result = []
    try:
        # Split the string by semicolons to get individual period entries
        entries = period_str.split(';')

        for entry in entries:
            # Split each entry by colon to get period and date range
            parts = entry.split(':')
            if len(parts) != 2:
                continue

            period, date_range = parts

            # Split date range by 'to' to get start and end dates
            date_parts = date_range.split('to')
            if len(date_parts) != 2:
                continue

            start_date_str, end_date_str = date_parts

            # Parse dates
            start_date = parse_datetime_to_date(start_date_str.strip())
            end_date = parse_datetime_to_date(end_date_str.strip())

            if start_date and end_date:
                result.append((period.strip(), start_date, end_date))

    except Exception as e:
        print(f"Error parsing period string: {e}")

    return result

def find_stars_by_houses(house_names, house_name_df, star_df, planet_name):
    """
    Find stars associated with houses for a specific planet.

    Args:
        house_names (list): List of house names
        house_name_df (pd.DataFrame): DataFrame containing house information
        star_df (pd.DataFrame): DataFrame containing star information
        planet_name (str): Name of the planet

    Returns:
        list: List of dictionaries containing house name and ruling planet name
    """
    result = []
    planets_in_houses = set()

    # For each house, find its ruling planet and stars
    for house in house_names:
        if not house:
            continue

        # Find the ruling planet for the house
        if isinstance(house_name_df, pd.DataFrame):
            planet_row = house_name_df[house_name_df["House Name"] == house.upper()]
            if not planet_row.empty:
                planet = planet_row.iloc[0]["Ruling Planets"]
                # Add planet to the set to prevent duplication
                if planet not in planets_in_houses:
                    planets_in_houses.add(planet)
                # Get the stars associated with the planet
                if isinstance(star_df, pd.DataFrame):
                    stars = star_df[star_df["Planet"] == planet]["Stars (Tamil)"].tolist()
                else:
                    # Handle case where star_df is a list of dictionaries
                    stars = []
                    for star_entry in star_df:
                        if star_entry.get("Planet") == planet:
                            stars.append(star_entry.get("Stars (Tamil)", ""))

                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": planet,
                    "Stars": stars
                })
            else:
                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": None,
                    "Stars": []
                })
        else:
            # Handle case where house_name_df is a list of dictionaries
            planet = None
            for house_entry in house_name_df:
                if house_entry.get("House Name") == house.upper():
                    planet = house_entry.get("Ruling Planets")
                    break

            if planet:
                # Add planet to the set to prevent duplication
                if planet not in planets_in_houses:
                    planets_in_houses.add(planet)
                # Get the stars associated with the planet
                if isinstance(star_df, pd.DataFrame):
                    stars = star_df[star_df["Planet"] == planet]["Stars (Tamil)"].tolist()
                else:
                    # Handle case where star_df is a list of dictionaries
                    stars = []
                    for star_entry in star_df:
                        if star_entry.get("Planet") == planet:
                            stars.append(star_entry.get("Stars (Tamil)", ""))

                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": planet,
                    "Stars": stars
                })
            else:
                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": None,
                    "Stars": []
                })

    return result

def finding_ruling_planet_names(house_names, house_name_df):
    """
    Find ruling planet names for houses.

    Args:
        house_names (list): List of house names
        house_name_df (pd.DataFrame or list): DataFrame or list containing house information

    Returns:
        list: List of dictionaries containing house name and ruling planet name
    """
    result = []

    # For each house, find its ruling planet
    for house in house_names:
        if not house:
            continue

        # Find the ruling planet for the house
        if isinstance(house_name_df, pd.DataFrame):
            planet_row = house_name_df[house_name_df["House Name"] == house.upper()]
            if not planet_row.empty:
                planet = planet_row.iloc[0]["Ruling Planets"]
                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": planet
                })
            else:
                result.append({
                    "house_name": house.upper(),
                    "ruling_planet_name": None
                })
        else:
            # Handle case where house_name_df is a list of dictionaries
            planet = None
            for house_entry in house_name_df:
                if house_entry.get("House Name") == house.upper():
                    planet = house_entry.get("Ruling Planets")
                    break

            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": planet
            })

    return result

def remove_duplicates(data):
    """
    Remove duplicate dictionaries from a list.

    Args:
        data (list): List of dictionaries

    Returns:
        list: List with duplicates removed
    """
    unique_data = []
    seen = set()

    for item in data:
        try:
            # Convert dictionary to a tuple of items for hashing
            if isinstance(item, dict):
                # Convert any unhashable values to strings
                hashable_items = []
                for k, v in sorted(item.items()):
                    if isinstance(v, (list, dict)):
                        hashable_items.append((k, str(v)))
                    else:
                        hashable_items.append((k, v))
                item_tuple = tuple(hashable_items)
                if item_tuple not in seen:
                    seen.add(item_tuple)
                    unique_data.append(item)
            else:
                # If item is not a dictionary, just add it
                unique_data.append(item)
        except Exception as e:
            print(f"Error in remove_duplicates: {str(e)}")
            # If there's an error, just add the item
            unique_data.append(item)

    return unique_data

def check_period_within_age_range(start_date, end_date, dob_date, start_age, end_age):
    """
    Check if a period falls within the specified age range.

    Args:
        start_date (datetime.date): Period start date
        end_date (datetime.date): Period end date
        dob_date (datetime.date): Date of birth
        start_age (int): Starting age
        end_age (int): Ending age

    Returns:
        bool: True if period falls within age range, False otherwise
    """
    age_start_date = dob_date.replace(year=dob_date.year + start_age)
    age_end_date = dob_date.replace(year=dob_date.year + end_age)

    # Check if there's any overlap between the period and the age range
    return not (end_date < age_start_date or start_date > age_end_date)

def find_periods_by_ruling_planet(planetary_periods, house_data, dob, start_age=0, end_age=80):
    """
    Find periods ruled by planets associated with houses.

    Args:
        planetary_periods (list): List of planetary periods
        house_data (list): List of house data
        dob (str): Date of birth
        start_age (int): Starting age for prediction
        end_age (int): Ending age for prediction

    Returns:
        tuple: Tuple containing matching periods, age start date, and age end date
    """
    dob_date = datetime.strptime(dob, "%Y-%m-%d").date()
    age_start_date = dob_date.replace(year=dob_date.year + start_age)
    age_end_date = dob_date.replace(year=dob_date.year + end_age)

    all_matching_periods = []

    for house in house_data:
        ruling_planet_name = house.get("ruling_planet_name", "").lower()
        matching_periods = [
            period for period in planetary_periods
            if ruling_planet_name in period[0].lower()
               and check_period_within_age_range(period[1], period[2], dob_date, start_age, end_age)
        ]
        all_matching_periods.extend(matching_periods)

    return all_matching_periods, age_start_date, age_end_date

def is_date_in_ranges(date_str, date_ranges):
    """
    Check if a date falls within any of the specified date ranges.

    Args:
        date_str (str): Date string
        date_ranges (list): List of date ranges

    Returns:
        tuple: Tuple containing boolean result and count
    """
    if not date_str or not date_ranges:
        return False, 0

    date = parse_datetime_to_date(date_str)
    if not date:
        return False, 0

    count = 0
    for period, start_date, end_date in date_ranges:
        if start_date <= date <= end_date:
            count += 1

    return count > 0, count

def get_all_stars_for_indices(star_df, planet_house, house_indices):
    """
    Get all stars for planets in specified houses.

    Args:
        star_df (pd.DataFrame): DataFrame containing star information
        planet_house (dict): Dictionary mapping houses to planets
        house_indices (list): List of house indices

    Returns:
        list: List of dictionaries containing house name and ruling planet name
    """
    result = []

    # Convert house indices to strings
    house_indices_str = [str(idx) for idx in house_indices]

    # Find planets in the specified houses
    planets_in_houses = []
    for house, planets in planet_house.items():
        if house in house_indices_str:
            planets_list = planets.split(', ')
            planets_in_houses.extend(planets_list)

    # Get stars for each planet
    for planet in planets_in_houses:
        planet_row = star_df[star_df["Planet"] == planet]
        if not planet_row.empty:
            stars = planet_row["Stars (Tamil)"].tolist()
            result.append({
                "house_name": f"house_{planet}",
                "ruling_planet_name": planet,
                "Stars": stars
            })

    return result

def check_planets_in_periods(period_data, planets_to_check):
    """
    Check which planets appear in which periods.

    Args:
        period_data (list): List of period data
        planets_to_check (list): List of planets to check

    Returns:
        dict: Dictionary mapping count to periods
    """
    result = {}

    # Extract planet names from planets_to_check
    planets_to_check_names = []
    for planet in planets_to_check:
        if isinstance(planet, dict):
            planet_name = planet.get("ruling_planet_name", "")
            if planet_name:
                planets_to_check_names.append(planet_name.lower())

    # Loop through each period and count the occurrence of planets
    for period_tuple in period_data:
        if len(period_tuple) < 3:
            continue

        period, start_date, end_date = period_tuple

        # Extract the planets in the period string (split by '-')
        planets_in_period = period.split('-')

        # Count how many of the planets_to_check_names are present in the period
        found_planets = [planet for planet in planets_in_period if planet.lower() in planets_to_check_names]

        # If any planets are found, determine how many counts to add
        if found_planets:
            # The count is based on the number of planets in the period string
            count = len(found_planets)

            # Add the result to the dictionary, grouped by count
            if count not in result:
                result[count] = []
            result[count].append((period, start_date, end_date, count))

    return result

def check_date_in_periods(period_data, date_str, rule_prefix=""):
    """
    Check if a date falls within any of the specified periods.

    Args:
        period_data (dict): Dictionary mapping count to periods
        date_str (str): Date string
        rule_prefix (str): Prefix for rule names

    Returns:
        dict: Dictionary containing rule results
    """
    result = {}

    if not date_str:
        return result

    date = parse_datetime_to_date(date_str)
    if not date:
        return result

    # Check each count group
    for count, periods in period_data.items():
        rule_name = f"Rule {rule_prefix}.{count}" if rule_prefix else f"Rule {count}"
        is_in_range = False
        match_count = 0

        for period, start_date, end_date, _ in periods:
            if start_date <= date <= end_date:
                is_in_range = True
                match_count += 1

        result[rule_name] = is_in_range
        result[f"{rule_name}_count"] = match_count

    return result

def get_house_names_with_numbers(house_name_df, ascendant):
    """
    Get house names with numbers based on ascendant.

    Args:
        house_name_df (pd.DataFrame): DataFrame containing house information
        ascendant (str): Ascendant sign

    Returns:
        tuple: Tuple containing house names with numbers and ascendant index
    """
    # Find the index of the ascendant in the raasi_list
    try:
        ascendant_index = raasi_list.index(ascendant)
    except ValueError:
        # If ascendant is not found, default to 0
        ascendant_index = 0

    # Create a list of house names in order
    house_names_in_order = []
    for i in range(12):
        # Calculate the house index (1-based)
        house_index = (i + ascendant_index) % 12 + 1
        # Get the house name
        house_name = raasi_list[i]
        # Add to the list
        house_names_in_order.append((house_index, house_name))

    return house_names_in_order, ascendant_index

def get_dob(user_astro_basic_df, user_id, column_name):
    """
    Get date of birth from user astro basic data.

    Args:
        user_astro_basic_df (pd.DataFrame): DataFrame containing user astro basic data
        user_id (int): User ID
        column_name (str): Column name

    Returns:
        str: Date of birth
    """
    user_data = user_astro_basic_df[user_astro_basic_df['user_id'] == user_id]
    if not user_data.empty and column_name in user_data.columns:
        return user_data[column_name].iloc[0]
    return None

def rule_one(placements, dataframes, df, dob, foreign_travel_date, user_profile_id):
    """
    Main rule function for foreign travel prediction.

    Args:
        placements (dict): Dictionary containing planet placements
        dataframes (dict): Dictionary containing astrological data
        df (pd.DataFrame): DataFrame containing chart information
        dob (str): Date of birth
        foreign_travel_date (str): Foreign travel date
        user_profile_id (str): User profile ID

    Returns:
        tuple: Tuple containing rule results and detailed results
    """
    try:
        print(f"foreign_travel_date: {foreign_travel_date}")
        m_rule = {}
        rasi = find_rasi(placements['planet_house'], placements['house_name'])
        print(f"The Rasi where the Moon is located: {rasi}")
        star = placements['star']
        dhasa = placements['dhasa']

        # Parse dasha periods
        dh = parse_to_tuples(dhasa.get('antara_dhasa_period', ''))
        print(f"Dasha periods: {dh[:3]}...")
        m_rule['user_profile_id'] = user_profile_id

        # Rule 3.1: Check houses 9 and 12
        house_names = find_houses_names(df, [9, 12])
        print(f"house_names: {house_names}")

        # If no house names are found, use default values
        if not house_names:
            print("No house names found, using default values")
            house_names = ["DHANUSU", "MEENAM"]

        result_11 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
        result_11_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
        result_11.extend(result_11_k)
        result_11 = remove_duplicates(result_11)
        print(f"rule_3.1.1 data: {result_11}")

        # Get all stars for houses 9 and 12
        result_12 = finding_ruling_planet_names(house_names, dataframes["house_name"])
        result_13 = get_all_stars_for_indices(dataframes['star'], placements['planet_house'], [9, 12])
        result_13.extend(result_12)
        result_13 = remove_duplicates(result_13)
        print(f"result_13: {result_13}")

        # Find matching periods
        matching_periods_31, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
        print(f"matching_periods_31: {matching_periods_31[:3]}...")
        is_in_range, count = is_date_in_ranges(foreign_travel_date, matching_periods_31)

        # If no matching periods are found, use default values
        if not matching_periods_31:
            print("No matching periods found, using default values")
            m_rule['Rule 3.1'] = False
            m_rule['Rule 3.1_count'] = 0
            filter_results_31 = {}
        else:
            mk = check_planets_in_periods(matching_periods_31, result_13)
            print(f"mk: {mk}")
            filter_results_31 = check_date_in_periods(mk, foreign_travel_date, "3.1")

            # Output the result and filter check
            print(f"Filter Results 3.1: {filter_results_31}")
            m_rule['Rule 3.1'] = is_in_range
            m_rule['Rule 3.1_count'] = count

        # Rule 3.2: Check houses 8 and 12
        house_names = find_houses_names(df, [8, 12])
        print(f"house_names: {house_names}")

        # If no house names are found, use default values
        if not house_names:
            print("No house names found, using default values")
            house_names = ["VIRICHIGAM", "MEENAM"]

        result_11 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
        result_11_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
        result_11.extend(result_11_k)
        result_11 = remove_duplicates(result_11)
        print(f"rule_3.2.1 data: {result_11}")

        # Get all stars for houses 8 and 12
        result_13 = get_all_stars_for_indices(dataframes['star'], placements['planet_house'], [8, 12])
        result_13.extend(result_12)
        result_13 = remove_duplicates(result_13)

        # If no matching periods are found, use default values
        if not matching_periods_31:
            print("No matching periods found, using default values")
            m_rule['Rule 3.2'] = False
            m_rule['Rule 3.2_count'] = 0
            filter_results_32 = {}
        else:
            mk = check_planets_in_periods(matching_periods_31, result_13)
            print(f"mk: {mk}")
            filter_results_32 = check_date_in_periods(mk, foreign_travel_date, "3.2")

            # Output the result and filter check
            print(f"Filter Results 3.2: {filter_results_32}")
            matching_periods_3, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
            is_in_range, count = is_date_in_ranges(foreign_travel_date, matching_periods_3)
            m_rule['Rule 3.2'] = is_in_range
            m_rule['Rule 3.2_count'] = count

        # Rule 3.3: Check houses 6, 7, and 12
        house_names = find_houses_names(df, [6, 7, 12])
        print(f"house_names: {house_names}")

        # If no house names are found, use default values
        if not house_names:
            print("No house names found, using default values")
            house_names = ["KANNI", "THULAM", "MEENAM"]

        result_11 = finding_ruling_planet_names(house_names, dataframes["house_name"])
        result_11 = remove_duplicates(result_11)
        print(f"rule_3.3.1 data: {result_11}")

        # Get all stars for houses 6, 7, and 12
        result_13 = get_all_stars_for_indices(dataframes['star'], placements['planet_house'], [6, 7, 12])
        result_13.extend(result_12)
        result_13 = remove_duplicates(result_13)
        matching_periods_3, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
        is_in_range, count = is_date_in_ranges(foreign_travel_date, matching_periods_3)
        m_rule['Rule 3.3'] = is_in_range
        m_rule['Rule 3.3_count'] = count

        # If no matching periods are found, use default values
        if not matching_periods_31:
            print("No matching periods found, using default values")
            filter_results_33 = {}
        else:
            mk = check_planets_in_periods(matching_periods_31, result_13)
            print(f"mk: {mk}")
            filter_results_33 = check_date_in_periods(mk, foreign_travel_date, "3.3")

        # Output the result and filter check
        print(f"Filter Results 3.3: {filter_results_33}")
        combined = {**m_rule}

        # Add filter results if they exist
        if filter_results_31:
            combined.update(filter_results_31)
        if filter_results_32:
            combined.update(filter_results_32)
        if filter_results_33:
            combined.update(filter_results_33)

        return m_rule, combined
    except Exception as e:
        import traceback
        print(f"Error in rule_one: {str(e)}")
        print(traceback.format_exc())

        # Return default values
        m_rule = {
            'user_profile_id': user_profile_id,
            'Rule 3.1': False,
            'Rule 3.1_count': 0,
            'Rule 3.2': False,
            'Rule 3.2_count': 0,
            'Rule 3.3': False,
            'Rule 3.3_count': 0
        }

        return m_rule, m_rule

def predict_foreign_travel(user_profile_id=None, member_profile_id=None, foreign_travel_date=None, print_output=False, use_excel_data=False):
    """
    Predict potential foreign travel periods based on astrological factors.

    Args:
        user_profile_id (str): User profile ID
        member_profile_id (str): Member profile ID
        foreign_travel_date (str): Foreign travel date for validation
        print_output (bool): Whether to print detailed output
        use_excel_data (bool): Whether to use Excel data instead of MongoDB

    Returns:
        dict: Dictionary containing prediction results
    """
    try:
        # Validate input
        if not user_profile_id or not member_profile_id:
            return {
                'success': False,
                'message': 'User profile ID and member profile ID are required'
            }

        # Get member data
        member_profile, astro_profile = get_member_data(user_profile_id, member_profile_id)
        if not member_profile or not astro_profile:
            return {
                'success': False,
                'message': f'Member profile not found for user_profile_id {user_profile_id} and member_profile_id {member_profile_id}'
            }

        # Get birth details
        birth_date = member_profile.get('birth_date', '')
        birth_time = member_profile.get('birth_time', '')
        birth_place = member_profile.get('birth_place', '')

        if not birth_date or not birth_time or not birth_place:
            return {
                'success': False,
                'message': 'Birth details are incomplete'
            }

        # Load data
        if use_excel_data:
            try:
                # Load data from Excel files
                dataframes = {}
                dataframes['house_name'] = pd.read_excel(os.path.join(EXCEL_DIR, 'house_name.xlsx'))
                dataframes['Planets_exalt_debilitate'] = pd.read_excel(os.path.join(EXCEL_DIR, 'Planets_exalt_debilitate.xlsx'))
                dataframes['star'] = pd.read_excel(os.path.join(EXCEL_DIR, 'star.xlsx'))
                dataframes['planets_friends_neutral_enemies'] = pd.read_excel(os.path.join(EXCEL_DIR, 'planets_friends_neutral_enemies.xlsx'))
                dataframes['planets_aspects'] = pd.read_excel(os.path.join(EXCEL_DIR, 'planets_aspects.xlsx'))
                dataframes['user_birth_chart'] = pd.read_excel(os.path.join(EXCEL_DIR, 'user_birth_chart.xlsx'))
                dataframes['user_astro_Basic'] = pd.read_excel(os.path.join(EXCEL_DIR, 'user_astro_Basic.xlsx'))

                # Find planet placements
                user_id = 1  # Default user ID for Excel data
                placements = find_planet_placements(dataframes['user_birth_chart'], user_id)

                # Get date of birth and foreign travel date
                dob = get_dob(dataframes['user_astro_Basic'], user_id, "user_birthdate")
                foreign_travel_date1 = foreign_travel_date or get_dob(dataframes['user_astro_Basic'], user_id, 'User_Data_Label_4 (Foreign Travel Date1)')

                # Generate chart
                ascendant = placements[0]['house_name']['house_name_1']
                house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
                house_planet_mapping = placements[0]['planet_house']
                file_path = os.path.join(CHART_DIR, f"{user_id}_{generate_random_string(5)}.png")
                from .medical_profession import plot_south_indian_chart
                df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)

                # Run rule one
                rule_results, detailed_results = rule_one(placements[0], dataframes, df, dob, foreign_travel_date1, user_profile_id)
            except Exception as e:
                print(f"Error loading Excel data: {str(e)}")
                return {
                    'success': False,
                    'message': f'Error loading Excel data: {str(e)}'
                }
        else:
            try:
                # Get all astrological data from MongoDB
                dataframes = get_all_astro_data()

                # Find planet placements
                placements = find_planet_placements(user_profile_id, member_profile_id)
                if not placements:
                    return {
                        'success': False,
                        'message': f'Planet placements not found for user_profile_id {user_profile_id} and member_profile_id {member_profile_id}'
                    }

                # Get date of birth
                dob = birth_date

                # Generate chart
                ascendant = placements['house_name']['house_name_1']
                house_names_with_numbers, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
                file_path = os.path.join(CHART_DIR, f"user_{user_profile_id}_member_{member_profile_id}_{generate_random_string(5)}.png")
                from .medical_profession import plot_south_indian_chart
                df = plot_south_indian_chart(file_path, house_names_with_numbers, ascendant_index, placements['planet_house'])

                # Run rule one
                rule_results, detailed_results = rule_one(placements, dataframes, df, dob, foreign_travel_date, user_profile_id)
            except Exception as e:
                print(f"Error processing MongoDB data: {str(e)}")
                return {
                    'success': False,
                    'message': f'Error processing MongoDB data: {str(e)}'
                }

        # Calculate overall percentage
        rule_count = 0
        true_count = 0

        for key, value in rule_results.items():
            if key.startswith('Rule') and not key.endswith('_count'):
                rule_count += 1
                if value:
                    true_count += 1

        overall_percentage = (true_count / rule_count * 100) if rule_count > 0 else 0

        # Determine foreign travel potential
        foreign_travel_potential = "Low"
        if overall_percentage >= 66:
            foreign_travel_potential = "High"
        elif overall_percentage >= 33:
            foreign_travel_potential = "Medium"

        # Prepare result
        result = {
            'success': True,
            'user_profile_id': user_profile_id,
            'member_profile_id': member_profile_id,
            'foreign_travel_date': foreign_travel_date,
            'chart_path': file_path,
            'rule_results': rule_results,
            'overall_percentage': round(overall_percentage, 2),
            'foreign_travel_potential': foreign_travel_potential,
            'detailed_results': detailed_results if print_output else None
        }

        return result

    except Exception as e:
        import traceback
        print(f"Error in foreign travel prediction: {str(e)}")
        print(traceback.format_exc())
        return {
            'success': False,
            'message': f'Error in foreign travel prediction: {str(e)}'
        }
