"""
Utility functions for career prediction.
"""

import os
import random
import string
import matplotlib
# Use a non-interactive backend to avoid GUI issues
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import pandas as pd
from bson import ObjectId
from datetime import datetime

# Create chart directory if it doesn't exist
CHART_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'charts')
if not os.path.exists(CHART_DIR):
    os.makedirs(CHART_DIR)


def generate_random_string(length):
    """
    Generate a random string of specified length.

    Args:
        length (int): Length of the random string to generate

    Returns:
        str: Random string
    """
    characters = string.ascii_letters + string.digits
    return ''.join(random.choice(characters) for _ in range(length))


def extract_planet_positions(d1_chart):
    """
    Extract planet positions from D1 chart.

    Args:
        d1_chart (dict): D1 chart data from MongoDB

    Returns:
        dict: Dictionary mapping house numbers to planets
    """
    planet_positions = {}

    # Initialize all houses with empty strings
    for i in range(1, 13):
        planet_positions[str(i)] = ''

    # Extract houses data
    houses = d1_chart.get('houses', [])

    # Debug output
    print(f"Houses type: {type(houses)}")
    if houses and len(houses) > 0:
        print(f"First house type: {type(houses[0])}")

    # Process each house
    for house in houses:
        # Handle different data formats
        if isinstance(house, dict):
            house_num = house.get('house_number')
            planets_data = house.get('planets', [])

            if planets_data and house_num:
                # Handle different planet data formats
                planet_names = []
                for planet in planets_data:
                    if isinstance(planet, dict):
                        planet_name = planet.get('name', '')
                        if planet_name:
                            planet_names.append(planet_name.upper())
                    elif isinstance(planet, str):
                        planet_names.append(planet.upper())

                if planet_names:
                    planet_positions[str(house_num)] = ', '.join(planet_names)
        elif isinstance(house, str):
            # Handle string format (house number:planet1,planet2)
            parts = house.split(':')
            if len(parts) == 2:
                house_num = parts[0].strip()
                planets_str = parts[1].strip()
                if planets_str:
                    planet_positions[house_num] = planets_str.upper()

    # Debug output
    print(f"Extracted planet positions: {planet_positions}")
    return planet_positions


def extract_house_names(d1_chart):
    """
    Extract house names from D1 chart.

    Args:
        d1_chart (dict): D1 chart data from MongoDB

    Returns:
        dict: Dictionary mapping house numbers to house names
    """
    house_names = {}

    # Initialize all houses with default values
    for i in range(1, 13):
        house_names[f'house_name_{i}'] = ''

    # Extract houses data
    houses = d1_chart.get('houses', [])

    # Process each house
    for house in houses:
        if isinstance(house, dict):
            house_num = house.get('house_number')
            sign = house.get('sign', '').upper()
            if house_num and sign:
                house_names[f'house_name_{house_num}'] = sign
        elif isinstance(house, str):
            # Handle string format (house number:sign)
            parts = house.split(':')
            if len(parts) == 2:
                house_num = parts[0].strip()
                sign = parts[1].strip().upper()
                if house_num.isdigit() and sign:
                    house_names[f'house_name_{house_num}'] = sign

    # Debug output
    print(f"Extracted house names: {house_names}")
    return house_names


def extract_nakshatra_data(d1_chart):
    """
    Extract nakshatra data from D1 chart.

    Args:
        d1_chart (dict): D1 chart data from MongoDB

    Returns:
        dict: Dictionary mapping planets to their nakshatras
    """
    nakshatra_data = {}

    # Initialize with default values for all planets
    for planet in ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu', 'lagnam']:
        nakshatra_data[f'{planet}_star'] = ''
        nakshatra_data[f'{planet}_pada'] = 0

    # First try to extract from planets_precise (dictionary format)
    planets_precise = d1_chart.get('planets_precise', {})
    if isinstance(planets_precise, dict):
        print(f"Processing planets_precise as dictionary: {planets_precise.keys()}")
        for planet_name, planet_data in planets_precise.items():
            if isinstance(planet_data, dict):
                # Extract nakshatra data if available
                if 'nakshatra' in planet_data and isinstance(planet_data['nakshatra'], dict):
                    nakshatra = planet_data['nakshatra'].get('name', '').upper()
                    pada = planet_data['nakshatra'].get('pada', 0)
                    nakshatra_data[f'{planet_name}_star'] = nakshatra
                    nakshatra_data[f'{planet_name}_pada'] = pada
                    print(f"Found nakshatra for {planet_name}: {nakshatra}, pada: {pada}")

    # If planets_precise is a list, process it differently
    elif isinstance(planets_precise, list):
        print(f"Processing planets_precise as list with {len(planets_precise)} items")
        for planet in planets_precise:
            if isinstance(planet, dict):
                planet_name = planet.get('name', '').lower()
                if planet_name and 'nakshatra' in planet and isinstance(planet['nakshatra'], dict):
                    nakshatra = planet['nakshatra'].get('name', '').upper()
                    pada = planet['nakshatra'].get('pada', 0)
                    nakshatra_data[f'{planet_name}_star'] = nakshatra
                    nakshatra_data[f'{planet_name}_pada'] = pada
                    print(f"Found nakshatra for {planet_name}: {nakshatra}, pada: {pada}")

    # Fallback: Extract from houses data
    houses = d1_chart.get('houses', [])
    if isinstance(houses, list):
        for house in houses:
            if isinstance(house, dict):
                planets = house.get('planets', [])
                for planet in planets:
                    if isinstance(planet, dict):
                        planet_name = planet.get('name', '').lower()
                        if planet_name and 'nakshatra' in planet and isinstance(planet['nakshatra'], dict):
                            nakshatra = planet['nakshatra'].get('name', '').upper()
                            pada = planet['nakshatra'].get('pada', 0)
                            nakshatra_data[f'{planet_name}_star'] = nakshatra
                            nakshatra_data[f'{planet_name}_pada'] = pada
                    elif isinstance(planet, str):
                        # Handle string format (planet:nakshatra:pada)
                        parts = planet.split(':')
                        if len(parts) >= 2:
                            planet_name = parts[0].strip().lower()
                            nakshatra = parts[1].strip().upper()
                            pada = int(parts[2]) if len(parts) > 2 and parts[2].strip().isdigit() else 0
                            nakshatra_data[f'{planet_name}_star'] = nakshatra
                            nakshatra_data[f'{planet_name}_pada'] = pada

    # Add lagna nakshatra
    lagna = d1_chart.get('lagna', {})
    if isinstance(lagna, dict) and 'nakshatra' in lagna:
        nakshatra_data['lagnam_star'] = lagna.get('nakshatra', {}).get('name', '').upper() if isinstance(lagna.get('nakshatra'), dict) else ''
        nakshatra_data['lagnam_pada'] = lagna.get('nakshatra', {}).get('pada', 0) if isinstance(lagna.get('nakshatra'), dict) else 0

    # Debug output
    print(f"Extracted nakshatra data: {nakshatra_data}")
    return nakshatra_data


def plot_south_indian_chart(file_path, house_names_with_numbers, ascendant_index, house_planet_mapping):
    """
    Plot the South Indian style horoscope chart.

    Args:
        file_path (str): Path to save the chart image
        house_names_with_numbers (list): List of house names with their numbers
        ascendant_index (int): The index of the ascendant house
        house_planet_mapping (dict): Dictionary mapping house numbers to planet names

    Returns:
        pd.DataFrame: DataFrame containing the horoscope data
    """
    data = []
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.set_xlim(0, 4)
    ax.set_ylim(0, 4)
    ax.set_xticks([])
    ax.set_yticks([])

    for i in range(5):
        ax.plot([i, i], [0, 4], color='black')
        ax.plot([0, 4], [i, i], color='black')

    # Generate box positions
    box_positions = generate_box_positions(ascendant_index)

    for house_number, house_name in house_names_with_numbers:
        x, y = box_positions[house_number]
        planet_name = house_planet_mapping.get(str(house_number), "")
        data.append({
            'planet_name': planet_name,
            'house_number': house_number,
            'house_name': house_name
        })
        ax.text(x + 0.5, y + 0.5, f'{house_number}\n{house_name}\n{planet_name}', ha='center', va='center', fontsize=12,
                bbox=dict(facecolor='white', edgecolor='black'))

    df = pd.DataFrame(data)

    plt.title('South Indian Style Horoscope Chart')
    plt.savefig(file_path)
    plt.close()

    return df


def generate_box_positions(ascendant_index):
    """
    Generate the positions of the boxes in the South Indian chart.

    Args:
        ascendant_index (int): The index of the ascendant house

    Returns:
        dict: A dictionary mapping house numbers to their positions
    """
    base_positions = [
        (1, 3), (2, 3), (3, 3), (3, 2),
        (3, 1), (3, 0), (2, 0), (1, 0),
        (0, 0), (0, 1), (0, 2), (0, 3)
    ]

    rotated_positions = base_positions[ascendant_index:] + base_positions[:ascendant_index]
    box_positions = {i + 1: pos for i, pos in enumerate(rotated_positions)}

    return box_positions


def get_house_names_with_numbers(house_name_df, ascendant):
    """
    Get house names with their corresponding numbers, starting from the ascendant.

    Args:
        house_name_df (dict): Dictionary containing house names
        ascendant (str): The ascendant house name

    Returns:
        tuple: A tuple containing the list of house names with numbers and the index of the ascendant
    """
    # Create a list of house names from the dictionary
    house_names = []
    for i in range(1, 13):
        house_names.append(house_name_df.get(f'house_name_{i}', ''))

    if ascendant not in house_names:
        raise ValueError(f"Ascendant '{ascendant}' is not a valid house name.")

    ascendant_index = house_names.index(ascendant)
    rotated_house_names = house_names[ascendant_index:] + house_names[:ascendant_index]
    house_names_with_numbers = [(i + 1, name) for i, name in enumerate(rotated_house_names)]

    return house_names_with_numbers, ascendant_index


def check_rule_status_with_percentage(rule_dict):
    """
    Check if at least one value in the rule dictionary or list of dictionaries is True.
    If at least one value is True, return 'pass', otherwise return 'fail'.
    Also, calculate the percentage of True values.

    Args:
        rule_dict (dict or list): Dictionary or list of dictionaries containing rule conditions

    Returns:
        tuple: A tuple containing the status ('pass' or 'fail') and the percentage of True values
    """
    total_values = 0
    true_values = 0

    def check_values(item):
        nonlocal total_values, true_values
        if isinstance(item, dict):
            for key, value in item.items():
                if isinstance(value, dict) or isinstance(value, list):
                    check_values(value)
                else:
                    total_values += 1
                    if value:
                        true_values += 1
        elif isinstance(item, list):
            for sub_item in item:
                check_values(sub_item)

    check_values(rule_dict)
    status = 'pass' if true_values > 0 else 'fail'
    percentage = (true_values / total_values) * 100 if total_values > 0 else 0
    return status, percentage


def calculate_rule_pass_percentages(data):
    """
    Calculate the percentage of users who pass each rule.

    Args:
        data (list): List of user data with rule information

    Returns:
        dict: Dictionary mapping rule names to pass percentages
    """
    rule_totals = {}
    user_count = len(data)

    for user in data:
        for rule in user['rules']:
            rule_name = rule['rule_name']
            if rule_name not in rule_totals:
                rule_totals[rule_name] = {'pass_count': 0, 'user_count': 0}
            rule_totals[rule_name]['user_count'] += 1
            if rule['status'] == 'pass':
                rule_totals[rule_name]['pass_count'] += 1

    overall_pass_percentages = {rule: round((totals['pass_count'] / user_count) * 100, 2) for rule, totals in
                                rule_totals.items()}

    return overall_pass_percentages
