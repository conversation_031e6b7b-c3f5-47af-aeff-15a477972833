# Rule Engine API Testing Guide

## Overview

This guide provides comprehensive instructions for testing the Rule Engine API using Postman and direct testing methods.

## Test Results Summary

### ✅ Basic Functions Working
- **parse_condition**: Successfully parses all rule types including advanced rules
- **parse_complex_query**: Correctly handles OR, AND, and mixed logical operations
- **evaluate_condition**: Properly evaluates basic planet-house conditions

### ✅ Advanced Rule Types Supported
- `Moon IN House1` - Basic IN rule
- `Sun NOT IN House8` - Basic NOT IN rule  
- `Ketu IN House6 WITH Ruling_Planet` - WITH ruling planet rule
- `Ketu IS RELATED TO House6_Ruling_Planet` - Related to ruling planet rule
- `Ketu IS ASPECTING_BIRTH House6_Ruling_Planet` - Aspecting birth ruling planet rule

### ✅ Complex Logic Working
- Single conditions: `Moon IN House1`
- OR logic: `Moon IN House1 OR Sun IN House5`
- AND logic: `Moon IN House1 AND Sun IN House5`
- Mixed logic: `Moon IN House1 AND Sun IN House5 OR Mars IN House10`

## Postman Testing Instructions

### Step 1: Import Collection
1. Open Postman
2. Click "Import" 
3. Select the file: `fortune_lens/Rule_Engine_Postman_Collection.json`
4. The collection will be imported with all test cases

### Step 2: Set Environment Variables
1. Create a new environment in Postman
2. Add variable: `base_url` = `http://127.0.0.1:5003`
3. Add variable: `access_token` = (will be set automatically after login)

### Step 3: Authentication Setup
Before testing rule engine APIs, you need a valid access token:

```bash
# First, ensure you have a test user (if not already created)
curl -X POST "http://127.0.0.1:5003/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Rule Test User",
    "mobile": "9876543210"
  }'

# Then login to get access token
curl -X POST "http://127.0.0.1:5003/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>", 
    "password": "password123"
  }'
```

### Step 4: Run Test Collection
Execute the tests in this order:

1. **Authentication** - Get access token
2. **Debug Chart** - Verify chart data structure
3. **Basic Rules** - Test simple IN/NOT IN rules
4. **Complex Rules** - Test AND/OR logic
5. **Advanced Rules** - Test ruling planet rules
6. **Error Handling** - Test validation and error scenarios
7. **Suggestions** - Test rule suggestions endpoint
8. **Different Chart Types** - Test D9, D10, etc.

## Manual API Testing Examples

### 1. Debug Chart Structure
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/debug-chart" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1", 
    "chart_type": "D1"
  }'
```

### 2. Basic Rule Evaluation
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Moon IN House4",
    "chart_type": "D1"
  }'
```

### 3. Complex OR Logic
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Moon IN House1 OR Moon IN House4 OR Moon IN House7",
    "chart_type": "D1"
  }'
```

### 4. Complex AND Logic
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Sun IN House1 AND Mercury IN House1",
    "chart_type": "D1"
  }'
```

### 5. Advanced Ruling Planet Rule
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "Ketu IN House6 WITH Ruling_Planet",
    "chart_type": "D1"
  }'
```

### 6. Get Rule Suggestions
```bash
curl -X GET "http://127.0.0.1:5003/api/rule-engine/suggestions" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Expected Response Formats

### Successful Rule Evaluation
```json
{
  "success": true,
  "query": "Moon IN House4",
  "chart_type": "D1", 
  "result": true,
  "planet_positions": {
    "SUN": 1,
    "MOON": 4,
    "MARS": 11,
    "MERCURY": 1,
    "JUPITER": 9,
    "VENUS": 2,
    "SATURN": 7,
    "RAHU": 12,
    "KETU": 6
  },
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Missing required fields: member_profile_id, query",
  "error_code": "MISSING_REQUIRED_FIELDS",
  "missing_fields": ["member_profile_id", "query"]
}
```

### Debug Chart Response
```json
{
  "success": true,
  "user_profile_id": "1",
  "member_profile_id": "1",
  "chart_type": "D1",
  "chart_data_exists": true,
  "chart_type_exists": true,
  "available_charts": ["D1", "D2", "D3", "D9", "D10"],
  "chart_structure": {
    "keys": ["houses", "lagna", "planets_precise"],
    "has_houses": true,
    "has_planets_precise": true,
    "has_lagna": true
  },
  "houses_structure": {
    "houses_count": 12,
    "sample_house": {
      "house_number": 1,
      "planets": ["sun", "mercury"]
    },
    "house_keys": ["house_number", "planets", "planet_degrees"]
  },
  "planets_found": ["sun", "moon", "mars", "mercury", "jupiter", "venus", "saturn", "rahu", "ketu"]
}
```

## Test Cases Coverage

### ✅ Basic Rule Types
- [x] Planet IN House
- [x] Planet NOT IN House
- [x] Case insensitive planet names
- [x] Case insensitive house numbers

### ✅ Complex Logic
- [x] OR conditions (any condition true)
- [x] AND conditions (all conditions true)
- [x] Mixed AND/OR logic
- [x] Multiple planet conditions

### ✅ Advanced Rule Types
- [x] WITH Ruling Planet
- [x] IS RELATED TO Ruling Planet
- [x] IS ASPECTING_BIRTH Ruling Planet

### ✅ Error Handling
- [x] Missing required fields
- [x] Invalid chart types
- [x] Invalid query format
- [x] Non-existent user/member
- [x] Chart data not found
- [x] Invalid chart structure

### ✅ Chart Type Support
- [x] D1 (Rasi/Birth Chart)
- [x] D9 (Navamsa)
- [x] D10 (Dasamsa)
- [x] All 23 divisional charts

### ✅ API Endpoints
- [x] POST /api/rule-engine/evaluate
- [x] POST /api/rule-engine/debug-chart
- [x] GET /api/rule-engine/suggestions

## Troubleshooting

### Authentication Issues
- Ensure you have a valid access token
- Check token expiration
- Verify user exists in database

### Chart Data Issues
- Use debug-chart endpoint to analyze data structure
- Verify user_profile_id and member_profile_id exist
- Check if chart data is properly generated

### Rule Parsing Issues
- Verify query syntax matches supported patterns
- Check planet names are valid
- Ensure house numbers are 1-12

## Performance Notes

- Rule evaluation is optimized for real-time use
- Complex queries with multiple AND/OR conditions are supported
- Chart data is cached for better performance
- Error responses include specific error codes for debugging
