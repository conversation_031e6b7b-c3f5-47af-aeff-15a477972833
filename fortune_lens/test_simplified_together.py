#!/usr/bin/env python3
"""
Test Simplified Together Logic
Tests the simplified ruling planet together logic - only checks if ruling planets are in same/different houses
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"

def test_ruling_planet_together_logic():
    """Test the simplified ruling planet together logic"""
    print("=" * 80)
    print("🔗 TESTING SIMPLIFIED RULING PLANET TOGETHER LOGIC")
    print("=" * 80)
    
    print("SIMPLIFIED TOGETHER LOGIC:")
    print("✅ Together = TRUE  → Ruling planets of house numbers are in SAME house")
    print("❌ Together = FALSE → Ruling planets of house numbers are in DIFFERENT houses")
    print("\nNo more complex angular relationships - just ruling planet positions!")
    
    # Test cases based on actual house ruling planets
    test_cases = [
        {
            "query": "10th_House_Planet IS RELATED TO 11th_House_Planet",
            "house1": 10,
            "house2": 11,
            "house1_ruling": "SATURN",
            "house2_ruling": "SATURN", 
            "ruling_planet_location": "House 12",
            "expected_together": True,
            "description": "Both houses ruled by SATURN (same ruling planet in same house)"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 8th_House_Planet",
            "house1": 1,
            "house2": 8,
            "house1_ruling": "MARS",
            "house2_ruling": "MARS",
            "ruling_planet_location": "House 11", 
            "expected_together": True,
            "description": "Both houses ruled by MARS (same ruling planet in same house)"
        },
        {
            "query": "9th_House_Planet IS RELATED TO 12th_House_Planet",
            "house1": 9,
            "house2": 12,
            "house1_ruling": "JUPITER",
            "house2_ruling": "JUPITER",
            "ruling_planet_location": "House 10",
            "expected_together": True,
            "description": "Both houses ruled by JUPITER (same ruling planet in same house)"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 10th_House_Planet",
            "house1": 1,
            "house2": 10,
            "house1_ruling": "MARS",
            "house2_ruling": "SATURN",
            "mars_location": "House 11",
            "saturn_location": "House 12",
            "expected_together": False,
            "description": "Different ruling planets in different houses"
        },
        {
            "query": "1st_House_Planet IS RELATED TO 5th_House_Planet",
            "house1": 1,
            "house2": 5,
            "house1_ruling": "MARS",
            "house2_ruling": "SUN",
            "mars_location": "House 11",
            "sun_location": "House 9",
            "expected_together": False,
            "description": "Different ruling planets in different houses"
        }
    ]
    
    print("\n" + "=" * 60)
    print("TESTING SIMPLIFIED TOGETHER LOGIC")
    print("=" * 60)
    
    passed = 0
    total = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test {i}: {test_case['query']}")
        print(f"Description: {test_case['description']}")
        print(f"Expected Together: {test_case['expected_together']}")
        
        # Make API request
        data = {
            "user_profile_id": "1",
            "member_profile_id": "1",
            "query": test_case['query'],
            "chart_type": "D1"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/api/rule-engine/house-planet-relationship", 
                                   json=data, headers={"Content-Type": "application/json"})
            
            if response.status_code == 200:
                result = response.json()
                actual_together = result.get('summary', {}).get('together', False)
                together_types = result.get('summary', {}).get('together_types', {})
                
                print(f"Actual Together: {actual_together}")
                print(f"Together Types: {together_types}")
                
                if actual_together == test_case['expected_together']:
                    print("✅ PASS")
                    passed += 1
                    
                    # Show ruling planet analysis
                    if actual_together:
                        print(f"✅ Ruling planets are in same house")
                        if 'ruling_planet_location' in test_case:
                            print(f"✅ {test_case['house1_ruling']} is in {test_case['ruling_planet_location']}")
                    else:
                        print(f"❌ Ruling planets are in different houses")
                        if 'mars_location' in test_case and 'saturn_location' in test_case:
                            print(f"❌ {test_case['house1_ruling']} in {test_case['mars_location']}, {test_case['house2_ruling']} in {test_case['saturn_location']}")
                        elif 'mars_location' in test_case and 'sun_location' in test_case:
                            print(f"❌ {test_case['house1_ruling']} in {test_case['mars_location']}, {test_case['house2_ruling']} in {test_case['sun_location']}")
                else:
                    print(f"❌ FAIL: Expected {test_case['expected_together']}, got {actual_together}")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 80)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! SIMPLIFIED TOGETHER LOGIC WORKING!")
        print("\n✅ VERIFIED FUNCTIONALITY:")
        print("   ✅ Together = TRUE when ruling planets in same house")
        print("   ✅ Together = FALSE when ruling planets in different houses")
        print("   ✅ No complex angular relationships")
        print("   ✅ Pure ruling planet position logic")
        
        print("\n🔗 RULING PLANET TOGETHER EXAMPLES:")
        print("   ✅ Houses 10 & 11: Both ruled by SATURN (in House 12)")
        print("   ✅ Houses 1 & 8: Both ruled by MARS (in House 11)")
        print("   ✅ Houses 9 & 12: Both ruled by JUPITER (in House 10)")
        
        print("\n❌ RULING PLANETS DIFFERENT EXAMPLES:")
        print("   ❌ Houses 1 & 10: MARS (House 11) ≠ SATURN (House 12)")
        print("   ❌ Houses 1 & 5: MARS (House 11) ≠ SUN (House 9)")
        
    else:
        print(f"\n❌ {total-passed} TESTS FAILED")

def show_house_ruling_analysis():
    """Show detailed house ruling planet analysis"""
    print("\n" + "=" * 80)
    print("📊 HOUSE RULING PLANET ANALYSIS")
    print("=" * 80)
    
    house_rulers = {
        1: {"ruling_planet": "MARS", "location": "House 11"},
        2: {"ruling_planet": "VENUS", "location": "House 10"},
        3: {"ruling_planet": "MERCURY", "location": "House 8"},
        4: {"ruling_planet": "MOON", "location": "House 5"},
        5: {"ruling_planet": "SUN", "location": "House 9"},
        6: {"ruling_planet": "MERCURY", "location": "House 8"},
        7: {"ruling_planet": "VENUS", "location": "House 10"},
        8: {"ruling_planet": "MARS", "location": "House 11"},
        9: {"ruling_planet": "JUPITER", "location": "House 10"},
        10: {"ruling_planet": "SATURN", "location": "House 12"},
        11: {"ruling_planet": "SATURN", "location": "House 12"},
        12: {"ruling_planet": "JUPITER", "location": "House 10"}
    }
    
    print("House Ruling Planet Locations:")
    for house_num, info in house_rulers.items():
        print(f"House {house_num:2d}: Ruled by {info['ruling_planet']:7s} → Located in {info['location']}")
    
    print("\n✅ TOGETHER COMBINATIONS (Same ruling planet location):")
    together_groups = {}
    for house_num, info in house_rulers.items():
        location = info['location']
        if location not in together_groups:
            together_groups[location] = []
        together_groups[location].append(f"House {house_num} ({info['ruling_planet']})")
    
    for location, houses in together_groups.items():
        if len(houses) > 1:
            print(f"   {location}: {', '.join(houses)}")
    
    print("\n🔗 SPECIFIC TOGETHER RELATIONSHIPS:")
    print("   ✅ Houses 10 & 11: Both ruling planets (SATURN) in House 12")
    print("   ✅ Houses 1 & 8: Both ruling planets (MARS) in House 11") 
    print("   ✅ Houses 9 & 12: Both ruling planets (JUPITER) in House 10")
    print("   ✅ Houses 2 & 7: Both ruling planets (VENUS) in House 10")
    print("   ✅ Houses 3 & 6: Both ruling planets (MERCURY) in House 8")

def main():
    """Main testing function"""
    print("SIMPLIFIED RULING PLANET TOGETHER LOGIC TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing simplified together logic - only ruling planet positions matter")
    
    # Show analysis
    show_house_ruling_analysis()
    
    # Test API
    test_ruling_planet_together_logic()
    
    print("\n" + "=" * 80)
    print("🎯 SIMPLIFIED TOGETHER LOGIC SUMMARY")
    print("=" * 80)
    print("✅ Together condition simplified to pure ruling planet logic")
    print("✅ Together = TRUE when ruling planets in same house")
    print("✅ Together = FALSE when ruling planets in different houses")
    print("✅ No more complex angular relationships")
    print("✅ Clear, simple, astrologically accurate")

if __name__ == "__main__":
    main()
