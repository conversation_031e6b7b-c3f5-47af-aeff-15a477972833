#!/usr/bin/env python3
"""
Test Aspecting Query
Tests: "House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu"
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

# Sample MongoDB chart data with planetary positions
SAMPLE_CHART_DATA = {
    "chart_data": {
        "D1": {
            "houses": [
                {
                    "house_number": 1,
                    "house_name": "MESHAM",     # Aries - ruled by Mars
                    "planets": ["sun", "mercury"],
                    "planet_degrees": {"sun": "16°30'", "mercury": "12°15'"},
                    "planet_nakshatras": {"sun": "ASHWINI", "mercury": "ASHWINI"}
                },
                {
                    "house_number": 2,
                    "house_name": "<PERSON>IS<PERSON><PERSON><PERSON>",   # Taurus - ruled by Venus
                    "planets": ["venus"],
                    "planet_degrees": {"venus": "25°45'"},
                    "planet_nakshatras": {"venus": "<PERSON><PERSON><PERSON><PERSON>"}
                },
                {
                    "house_number": 4,
                    "house_name": "<PERSON><PERSON><PERSON><PERSON>",    # Cancer - ruled by Moon
                    "planets": ["moon"],
                    "planet_degrees": {"moon": "25°45'"},
                    "planet_nakshatras": {"moon": "ASHLESHA"}
                },
                {
                    "house_number": 6,
                    "house_name": "KUMBAM",     # Aquarius - ruled by Saturn
                    "planets": ["ketu"],        # Ketu is in House 6
                    "planet_degrees": {"ketu": "8°30'"},
                    "planet_nakshatras": {"ketu": "DHANISHTA"}
                },
                {
                    "house_number": 9,
                    "house_name": "DHANUSU",    # Sagittarius - ruled by Jupiter
                    "planets": ["jupiter"],
                    "planet_degrees": {"jupiter": "22°10'"},
                    "planet_nakshatras": {"jupiter": "PURVA_ASHADHA"}
                },
                {
                    "house_number": 10,
                    "house_name": "MAGARAM",    # Capricorn - ruled by Saturn
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 11,
                    "house_name": "KANNI",      # Virgo - ruled by Mercury
                    "planets": ["mars", "saturn"],  # Saturn is in House 11
                    "planet_degrees": {"mars": "18°45'", "saturn": "5°20'"},
                    "planet_nakshatras": {"mars": "HASTA", "saturn": "MAGAM"}
                },
                {
                    "house_number": 12,
                    "house_name": "MEENAM",     # Pisces - ruled by Jupiter
                    "planets": ["rahu"],
                    "planet_degrees": {"rahu": "8°30'"},
                    "planet_nakshatras": {"rahu": "UTTARA_BHADRAPADA"}
                }
            ]
        }
    }
}

def analyze_aspecting_query():
    """Analyze the aspecting query step by step"""
    print("=" * 80)
    print("ASPECTING QUERY ANALYSIS")
    print("Query: House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu")
    print("=" * 80)
    
    # House name to ruling planet mapping
    house_name_ruling_planets = {
        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
    }
    
    print("Chart Analysis:")
    print("Planet Positions:")
    ketu_house = None
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        house_num = house["house_number"]
        house_name = house["house_name"]
        ruling_planet = house_name_ruling_planets.get(house_name)
        planets = house.get("planets", [])
        
        print(f"  House {house_num}: {house_name} (ruled by {ruling_planet}) → Planets: {planets}")
        
        if "ketu" in planets:
            ketu_house = house_num
            print(f"    *** KETU is in House {house_num} ***")
    
    print(f"\nKetu Location: House {ketu_house}")
    
    def get_planetary_aspects(planet_name, planet_house):
        """Calculate planetary aspects"""
        if not planet_house or planet_house < 1 or planet_house > 12:
            return []
        
        aspects = []
        
        # 7th house aspect (all planets)
        seventh_house = ((planet_house + 6) % 12) + 1
        if seventh_house == 13:
            seventh_house = 1
        aspects.append(seventh_house)
        
        # Special aspects
        if planet_name.upper() == "MARS":
            fourth_house = ((planet_house + 3) % 12) + 1
            if fourth_house == 13:
                fourth_house = 1
            eighth_house = ((planet_house + 7) % 12) + 1
            if eighth_house == 13:
                eighth_house = 1
            aspects.extend([fourth_house, eighth_house])
            
        elif planet_name.upper() == "JUPITER":
            fifth_house = ((planet_house + 4) % 12) + 1
            if fifth_house == 13:
                fifth_house = 1
            ninth_house = ((planet_house + 8) % 12) + 1
            if ninth_house == 13:
                ninth_house = 1
            aspects.extend([fifth_house, ninth_house])
            
        elif planet_name.upper() == "SATURN":
            third_house = ((planet_house + 2) % 12) + 1
            if third_house == 13:
                third_house = 1
            tenth_house = ((planet_house + 9) % 12) + 1
            if tenth_house == 13:
                tenth_house = 1
            aspects.extend([third_house, tenth_house])
        
        return sorted(list(set(aspects)))
    
    print("\n" + "=" * 60)
    print("CONDITION 1: House6_Ruling_Planet IS_ASPECTING Ketu")
    print("=" * 60)
    
    # Find House 6 ruling planet
    house_6_name = None
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        if house["house_number"] == 6:
            house_6_name = house["house_name"]
            break
    
    house_6_ruling_planet = house_name_ruling_planets.get(house_6_name)
    print(f"Step 1: House 6 name → {house_6_name}")
    print(f"Step 2: House 6 ruling planet → {house_6_ruling_planet}")
    
    # Find where the ruling planet is located
    ruling_planet_house = None
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        planets = house.get("planets", [])
        
        if house_6_ruling_planet.lower() in planets:
            ruling_planet_house = house["house_number"]
            print(f"Step 3: {house_6_ruling_planet} is in House {ruling_planet_house}")
            break
    
    if ruling_planet_house:
        # Get aspects of the ruling planet
        aspects = get_planetary_aspects(house_6_ruling_planet, ruling_planet_house)
        print(f"Step 4: {house_6_ruling_planet} in House {ruling_planet_house} aspects houses: {aspects}")
        
        condition_1_result = ketu_house in aspects
        print(f"Step 5: Is Ketu's house ({ketu_house}) in aspected houses? → {'YES ✓' if condition_1_result else 'NO ✗'}")
    else:
        condition_1_result = False
        print("Step 3-5: Ruling planet location not found → FALSE")
    
    print(f"CONDITION 1 RESULT: {'TRUE ✓' if condition_1_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 60)
    print("CONDITION 2: House10_Ruling_Planet IS_ASPECTING Ketu")
    print("=" * 60)
    
    # Find House 10 ruling planet
    house_10_name = None
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        if house["house_number"] == 10:
            house_10_name = house["house_name"]
            break
    
    house_10_ruling_planet = house_name_ruling_planets.get(house_10_name)
    print(f"Step 1: House 10 name → {house_10_name}")
    print(f"Step 2: House 10 ruling planet → {house_10_ruling_planet}")
    
    # Find where the ruling planet is located (same as House 6 in this case)
    ruling_planet_house_10 = None
    for house in SAMPLE_CHART_DATA["chart_data"]["D1"]["houses"]:
        planets = house.get("planets", [])
        
        if house_10_ruling_planet.lower() in planets:
            ruling_planet_house_10 = house["house_number"]
            print(f"Step 3: {house_10_ruling_planet} is in House {ruling_planet_house_10}")
            break
    
    if ruling_planet_house_10:
        # Get aspects of the ruling planet
        aspects_10 = get_planetary_aspects(house_10_ruling_planet, ruling_planet_house_10)
        print(f"Step 4: {house_10_ruling_planet} in House {ruling_planet_house_10} aspects houses: {aspects_10}")
        
        condition_2_result = ketu_house in aspects_10
        print(f"Step 5: Is Ketu's house ({ketu_house}) in aspected houses? → {'YES ✓' if condition_2_result else 'NO ✗'}")
    else:
        condition_2_result = False
        print("Step 3-5: Ruling planet location not found → FALSE")
    
    print(f"CONDITION 2 RESULT: {'TRUE ✓' if condition_2_result else 'FALSE ✗'}")
    
    print("\n" + "=" * 60)
    print("FINAL OR LOGIC")
    print("=" * 60)
    
    final_result = condition_1_result or condition_2_result
    print(f"Condition 1 OR Condition 2 → {condition_1_result} OR {condition_2_result} → {'TRUE ✓' if final_result else 'FALSE ✗'}")
    
    return final_result

def demonstrate_planetary_aspects():
    """Demonstrate planetary aspect concepts"""
    print("\n" + "=" * 80)
    print("PLANETARY ASPECTS DEMONSTRATION")
    print("=" * 80)
    
    print("Planetary Aspect Rules:")
    print("1. All planets aspect the 7th house from their position (opposition)")
    print("2. Mars additionally aspects 4th and 8th houses from its position")
    print("3. Jupiter additionally aspects 5th and 9th houses from its position")
    print("4. Saturn additionally aspects 3rd and 10th houses from its position")
    
    print("\nExample: Saturn in House 11")
    print("  7th aspect: House 11 + 6 = House 5 (17 % 12 = 5)")
    print("  3rd aspect: House 11 + 2 = House 1 (13 % 12 = 1)")
    print("  10th aspect: House 11 + 9 = House 8 (20 % 12 = 8)")
    print("  Saturn in House 11 aspects: Houses 1, 5, 8")
    
    print("\nKetu is in House 6")
    print("  Saturn aspects Houses 1, 5, 8")
    print("  Does Saturn aspect Ketu? NO (6 not in [1, 5, 8])")

def create_api_test_requests():
    """Create API test requests for aspecting queries"""
    print("\n" + "=" * 80)
    print("API TEST REQUESTS FOR ASPECTING QUERIES")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "Test House 6 Ruling Planet Aspecting Ketu",
            "query": "House6_Ruling_Planet IS_ASPECTING Ketu",
            "expected": False,
            "explanation": "Saturn (House 6 ruler) in House 11 aspects Houses 1,5,8 but not House 6 (where Ketu is)"
        },
        {
            "name": "Test House 10 Ruling Planet Aspecting Ketu",
            "query": "House10_Ruling_Planet IS_ASPECTING Ketu",
            "expected": False,
            "explanation": "Saturn (House 10 ruler) in House 11 aspects Houses 1,5,8 but not House 6 (where Ketu is)"
        },
        {
            "name": "Test Complex OR Query",
            "query": "House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu",
            "expected": False,
            "explanation": "FALSE OR FALSE = FALSE"
        },
        {
            "name": "Test Combined Nakshatra and Aspecting",
            "query": "House6_Ruling_Planet IN_STAR_OF Ketu OR House6_Ruling_Planet IS_ASPECTING Ketu",
            "expected": True,
            "explanation": "TRUE (nakshatra) OR FALSE (aspecting) = TRUE"
        }
    ]
    
    print("Test Cases:")
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Query: {test['query']}")
        print(f"   Expected: {'TRUE ✓' if test['expected'] else 'FALSE ✗'}")
        print(f"   Explanation: {test['explanation']}")
        
        # Show curl command
        print(f"   Curl command:")
        print(f'   curl -X POST "{BASE_URL}/api/rule-engine/evaluate" \\')
        print(f'     -H "Content-Type: application/json" \\')
        print(f'     -H "Authorization: Bearer YOUR_TOKEN" \\')
        print(f'     -d \'{{"user_profile_id": "{TEST_USER_ID}", "member_profile_id": "{TEST_MEMBER_ID}", "query": "{test["query"]}", "chart_type": "D1"}}\'')

def main():
    """Main demonstration function"""
    print("ASPECTING QUERY TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Target Query: House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu")
    
    # Run analysis
    expected_result = analyze_aspecting_query()
    demonstrate_planetary_aspects()
    create_api_test_requests()
    
    print("\n" + "=" * 80)
    print("SUMMARY")
    print("=" * 80)
    print("✓ Enhanced rule engine supports planetary aspecting queries")
    print("✓ Can check if house ruling planets are aspecting specific planet houses")
    print("✓ Supports OR logic with aspecting conditions")
    print("✓ Uses accurate planetary aspect calculations")
    
    print("\nNew Query Syntax:")
    print("• House#_Ruling_Planet IS_ASPECTING Planet")
    print("• Example: House6_Ruling_Planet IS_ASPECTING Ketu")
    print("• Complex: House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu")
    
    print(f"\nExpected Result for Target Query: {'TRUE ✓' if expected_result else 'FALSE ✗'}")
    
    print("\nBoth Query Types Now Supported:")
    print("1. Nakshatra: House6_Ruling_Planet IN_STAR_OF Ketu")
    print("2. Aspecting: House6_Ruling_Planet IS_ASPECTING Ketu")
    print("3. Combined: House6_Ruling_Planet IN_STAR_OF Ketu OR House6_Ruling_Planet IS_ASPECTING Ketu")
    
    print("\nNext Steps:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test aspecting queries with API")
    print("4. Verify results match analysis")

if __name__ == "__main__":
    main()
