#!/usr/bin/env python3
"""
MongoDB Structure Rule Engine Demonstration
Shows how the rule engine works with actual MongoDB chart data structure
"""

import json
from datetime import datetime

# Actual MongoDB chart data structure (as per the codebase)
MONGODB_CHART_DATA = {
    "chart_data": {
        "D1": {
            "houses": [
                {
                    "house_number": 1,
                    "house_name": "MESHAM",     # Aries - ruled by Mars
                    "planets": ["sun", "mercury"],
                    "planet_degrees": {"sun": "16°30'", "mercury": "12°15'"},
                    "planet_nakshatras": {"sun": "BARANI", "mercury": "ASHWINI"}
                },
                {
                    "house_number": 2,
                    "house_name": "RISHA<PERSON><PERSON>",   # Taurus - ruled by Venus
                    "planets": ["venus"],
                    "planet_degrees": {"venus": "25°45'"},
                    "planet_nakshatras": {"venus": "ROHIN<PERSON>"}
                },
                {
                    "house_number": 3,
                    "house_name": "<PERSON><PERSON><PERSON><PERSON>",    # Gemini - ruled by <PERSON>
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 4,
                    "house_name": "KAD<PERSON><PERSON>",    # Cancer - ruled by <PERSON>
                    "planets": ["moon"],
                    "planet_degrees": {"moon": "25°45'"},
                    "planet_nakshatras": {"moon": "ASHLESHA"}
                },
                {
                    "house_number": 5,
                    "house_name": "SIMMAM",     # Leo - ruled by Sun
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 6,
                    "house_name": "KUMBAM",     # Aquarius - ruled by Saturn
                    "planets": ["ketu"],
                    "planet_degrees": {"ketu": "8°30'"},
                    "planet_nakshatras": {"ketu": "DHANISHTA"}
                },
                {
                    "house_number": 7,
                    "house_name": "THULAM",     # Libra - ruled by Venus
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 8,
                    "house_name": "VIRICHIGAM", # Scorpio - ruled by Mars
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 9,
                    "house_name": "DHANUSU",    # Sagittarius - ruled by Jupiter
                    "planets": ["jupiter"],
                    "planet_degrees": {"jupiter": "22°10'"},
                    "planet_nakshatras": {"jupiter": "PURVA_ASHADHA"}
                },
                {
                    "house_number": 10,
                    "house_name": "MAGARAM",    # Capricorn - ruled by Saturn
                    "planets": [],
                    "planet_degrees": {},
                    "planet_nakshatras": {}
                },
                {
                    "house_number": 11,
                    "house_name": "KANNI",      # Virgo - ruled by Mercury
                    "planets": ["mars", "saturn"],
                    "planet_degrees": {"mars": "18°45'", "saturn": "5°20'"},
                    "planet_nakshatras": {"mars": "HASTA", "saturn": "UTTARA_PHALGUNI"}
                },
                {
                    "house_number": 12,
                    "house_name": "MEENAM",     # Pisces - ruled by Jupiter
                    "planets": ["rahu"],
                    "planet_degrees": {"rahu": "8°30'"},
                    "planet_nakshatras": {"rahu": "UTTARA_BHADRAPADA"}
                }
            ]
        }
    }
}

# Planet positions extracted from chart data
PLANET_POSITIONS = {
    "SUN": 1,
    "MERCURY": 1,
    "VENUS": 2,
    "MOON": 4,
    "KETU": 6,
    "JUPITER": 9,
    "MARS": 11,
    "SATURN": 11,
    "RAHU": 12
}

def demo_mongodb_structure():
    """Show the actual MongoDB structure"""
    print("=" * 70)
    print("ACTUAL MONGODB CHART DATA STRUCTURE")
    print("=" * 70)
    
    print("House Structure in MongoDB:")
    print("houses[index] where index 0-11 corresponds to house_number 1-12")
    print()
    
    for i, house in enumerate(MONGODB_CHART_DATA["chart_data"]["D1"]["houses"]):
        house_num = house["house_number"]
        house_name = house["house_name"]
        planets = house["planets"]
        print(f"Index {i:2d} → House {house_num:2d} → {house_name:10s} → Planets: {planets}")

def demo_get_house_name_and_ruling_planet():
    """Demonstrate getting house names and ruling planets from MongoDB structure"""
    print("\n" + "=" * 70)
    print("DEMO: Getting House Names and Ruling Planets from MongoDB")
    print("=" * 70)
    
    # House name to ruling planet mapping
    house_name_ruling_planets = {
        'MESHAM': 'MARS',       # Aries
        'RISHABAM': 'VENUS',    # Taurus
        'MIDUNAM': 'MERCURY',   # Gemini
        'KADAGAM': 'MOON',      # Cancer
        'SIMMAM': 'SUN',        # Leo
        'KANNI': 'MERCURY',     # Virgo
        'THULAM': 'VENUS',      # Libra
        'VIRICHIGAM': 'MARS',   # Scorpio
        'DHANUSU': 'JUPITER',   # Sagittarius
        'MAGARAM': 'SATURN',    # Capricorn
        'KUMBAM': 'SATURN',     # Aquarius
        'MEENAM': 'JUPITER'     # Pisces
    }
    
    def get_house_name_and_ruling_planet_from_mongodb(chart_data, house_number):
        chart = chart_data["chart_data"]["D1"]
        for house in chart["houses"]:
            if house.get("house_number") == house_number:
                house_name = house.get("house_name")
                if house_name:
                    ruling_planet = house_name_ruling_planets.get(house_name.upper())
                    return house_name.upper(), ruling_planet
        return None, None
    
    print("House → House Name → Ruling Planet (from MongoDB)")
    print("-" * 50)
    
    for house_num in range(1, 13):
        house_name, ruling_planet = get_house_name_and_ruling_planet_from_mongodb(MONGODB_CHART_DATA, house_num)
        print(f"House {house_num:2d} → {house_name:10s} → {ruling_planet}")

def demo_ketu_house6_with_ruling_planet():
    """Demonstrate the specific rule: Ketu IN House6 WITH Ruling_Planet"""
    print("\n" + "=" * 70)
    print("DEMO: 'Ketu IN House6 WITH Ruling_Planet' Analysis")
    print("=" * 70)
    
    print("Step-by-step analysis:")
    print("-" * 30)
    
    # Step 1: Check if Ketu is in House 6
    ketu_house = PLANET_POSITIONS.get("KETU")
    print(f"Step 1: Is Ketu in House 6?")
    print(f"        Ketu position: House {ketu_house}")
    print(f"        Result: {'YES ✓' if ketu_house == 6 else 'NO ✗'}")
    
    if ketu_house == 6:
        # Step 2: Find what house name (sign) is in House 6
        chart = MONGODB_CHART_DATA["chart_data"]["D1"]
        house_6_data = None
        for house in chart["houses"]:
            if house.get("house_number") == 6:
                house_6_data = house
                break
        
        if house_6_data:
            house_name = house_6_data.get("house_name")
            print(f"\nStep 2: What house name (sign) is in House 6?")
            print(f"        House 6 name: {house_name}")
            
            # Step 3: Determine ruling planet of that house name
            house_name_ruling_planets = {
                'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
                'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
                'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
                'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
            }
            
            ruling_planet = house_name_ruling_planets.get(house_name.upper())
            print(f"\nStep 3: Who is the ruling planet of {house_name}?")
            print(f"        Ruling planet: {ruling_planet}")
            
            # Step 4: Check if ruling planet is also in House 6
            ruling_planet_house = PLANET_POSITIONS.get(ruling_planet)
            print(f"\nStep 4: Is {ruling_planet} also in House 6?")
            print(f"        {ruling_planet} position: House {ruling_planet_house}")
            print(f"        Result: {'YES ✓' if ruling_planet_house == 6 else 'NO ✗'}")
            
            # Final result
            final_result = ruling_planet_house == 6
            print(f"\nFINAL RESULT: {'TRUE ✓' if final_result else 'FALSE ✗'}")
            
            if final_result:
                print(f"Explanation: Ketu is in House 6 ({house_name}) WITH {ruling_planet} (ruling planet)")
            else:
                print(f"Explanation: Ketu is in House 6 ({house_name}) but {ruling_planet} (ruling planet) is in House {ruling_planet_house}")

def demo_other_with_ruling_planet_examples():
    """Demonstrate other WITH Ruling_Planet examples"""
    print("\n" + "=" * 70)
    print("DEMO: Other 'WITH Ruling_Planet' Examples")
    print("=" * 70)
    
    test_cases = [
        ("JUPITER", 9),  # Jupiter in House 9 (Sagittarius - ruled by Jupiter)
        ("MARS", 11),    # Mars in House 11 (Virgo - ruled by Mercury)
        ("SUN", 1),      # Sun in House 1 (Aries - ruled by Mars)
    ]
    
    house_name_ruling_planets = {
        'MESHAM': 'MARS', 'RISHABAM': 'VENUS', 'MIDUNAM': 'MERCURY',
        'KADAGAM': 'MOON', 'SIMMAM': 'SUN', 'KANNI': 'MERCURY',
        'THULAM': 'VENUS', 'VIRICHIGAM': 'MARS', 'DHANUSU': 'JUPITER',
        'MAGARAM': 'SATURN', 'KUMBAM': 'SATURN', 'MEENAM': 'JUPITER'
    }
    
    for planet, house_num in test_cases:
        print(f"\n'{planet} IN House{house_num} WITH Ruling_Planet':")
        
        # Check if planet is in the house
        planet_house = PLANET_POSITIONS.get(planet)
        if planet_house != house_num:
            print(f"  Result: FALSE ✗ - {planet} is not in House {house_num} (it's in House {planet_house})")
            continue
        
        # Get house name from MongoDB
        chart = MONGODB_CHART_DATA["chart_data"]["D1"]
        house_name = None
        for house in chart["houses"]:
            if house.get("house_number") == house_num:
                house_name = house.get("house_name")
                break
        
        if house_name:
            ruling_planet = house_name_ruling_planets.get(house_name.upper())
            ruling_planet_house = PLANET_POSITIONS.get(ruling_planet)
            
            result = ruling_planet_house == house_num
            print(f"  House {house_num} name: {house_name}")
            print(f"  Ruling planet: {ruling_planet}")
            print(f"  {ruling_planet} position: House {ruling_planet_house}")
            print(f"  Result: {'TRUE ✓' if result else 'FALSE ✗'}")
            
            if result:
                print(f"  Explanation: {planet} is in House {house_num} ({house_name}) WITH {ruling_planet}")
            else:
                print(f"  Explanation: {planet} is in House {house_num} ({house_name}) but {ruling_planet} is in House {ruling_planet_house}")

def demo_api_response_format():
    """Show enhanced API response format with MongoDB structure"""
    print("\n" + "=" * 70)
    print("DEMO: Enhanced API Response Format (MongoDB Structure)")
    print("=" * 70)
    
    # Simulate API response for "Ketu IN House6 WITH Ruling_Planet"
    api_response = {
        "success": True,
        "query": "Ketu IN House6 WITH Ruling_Planet",
        "chart_type": "D1",
        "result": False,  # Ketu is in House 6 (Aquarius) but Saturn (ruler) is in House 11
        "planet_positions": PLANET_POSITIONS,
        "house_analysis": {
            "house_6": {
                "house_number": 6,
                "house_name": "KUMBAM",  # Aquarius
                "ruling_planet": "SATURN",
                "ruling_planet_position": "House 11",
                "planets_in_house": ["KETU"],
                "explanation": "Ketu is in House 6 (KUMBAM/Aquarius) but Saturn (ruling planet) is in House 11, not House 6"
            }
        },
        "mongodb_structure_info": {
            "houses_array_index": 5,  # Index 5 in houses array corresponds to house_number 6
            "house_data_from_mongodb": {
                "house_number": 6,
                "house_name": "KUMBAM",
                "planets": ["ketu"],
                "planet_degrees": {"ketu": "8°30'"},
                "planet_nakshatras": {"ketu": "DHANISHTA"}
            }
        },
        "user_profile_id": "1",
        "member_profile_id": "1"
    }
    
    print("Enhanced API Response with MongoDB Structure:")
    print(json.dumps(api_response, indent=2))

def main():
    """Main demonstration function"""
    print("RULE ENGINE - MONGODB STRUCTURE DEMONSTRATION")
    print("=" * 70)
    print(f"Demo started at: {datetime.now()}")
    
    # Run demonstrations
    demo_mongodb_structure()
    demo_get_house_name_and_ruling_planet()
    demo_ketu_house6_with_ruling_planet()
    demo_other_with_ruling_planet_examples()
    demo_api_response_format()
    
    print("\n" + "=" * 70)
    print("KEY POINTS")
    print("=" * 70)
    print("✓ MongoDB uses 'house_name' field (not 'sign')")
    print("✓ Houses array index 0-11 maps to house_number 1-12")
    print("✓ Rule engine reads actual house_name from chart data")
    print("✓ Ruling planet determined from house_name mapping")
    print("✓ 'WITH Ruling_Planet' checks if both planets are in same house")
    print("✓ More accurate analysis based on individual's chart")
    
    print("\nExample: 'Ketu IN House6 WITH Ruling_Planet'")
    print("- Reads House 6 from MongoDB: house_name = 'KUMBAM'")
    print("- Determines ruling planet: KUMBAM = Saturn")
    print("- Checks if Saturn is also in House 6")
    print("- Result: FALSE (Saturn is in House 11, not House 6)")

if __name__ == "__main__":
    main()
