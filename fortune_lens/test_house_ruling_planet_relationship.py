#!/usr/bin/env python3
"""
Test House Ruling Planet Relationship Functionality
Tests the new 5-rule comprehensive relationship checking between house ruling planets
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "http://127.0.0.1:5003"
TEST_USER_ID = "1"
TEST_MEMBER_ID = "1"

def test_house_ruling_planet_relationship():
    """Test the house ruling planet relationship functionality"""
    print("=" * 80)
    print("🔍 HOUSE RULING PLANET RELATIONSHIP TESTING")
    print("=" * 80)
    
    print("Your Query: 'House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet'")
    print("\nThis will check ALL 5 relationship types:")
    print("1. Basic Position: house6_ruling_planet IN house10 OR house10_ruling_planet IN house6")
    print("2. WITH Ruling Planet: house6_ruling_planet IN house10 WITH house10_ruling_planet OR house10_ruling_planet IN house6 WITH house6_ruling_planet")
    print("3. Together: House6_Ruling_Planet TOGETHER_WITH house10_ruling_planet")
    print("4. Nakshatra: House6_Ruling_Planet IN_STAR_OF house10_ruling_planet OR house10_ruling_planet IN_STAR_OF House6_Ruling_Planet")
    print("5. Aspecting: House6_Ruling_Planet IS_ASPECTING house10_ruling_planet OR house10_ruling_planet IS_ASPECTING House6_Ruling_Planet")
    
    # Expected results based on our chart data
    print("\n" + "=" * 60)
    print("EXPECTED RESULTS ANALYSIS")
    print("=" * 60)
    
    print("Chart Data Summary:")
    print("• House 6: KUMBAM (Aquarius) → ruled by SATURN")
    print("• House 10: MAGARAM (Capricorn) → ruled by SATURN") 
    print("• SATURN: Located in House 11, in MAGAM nakshatra")
    
    print("\nFor House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet:")
    print("Both houses are ruled by the SAME planet (SATURN), so:")
    
    print("\n1. Basic Position:")
    print("   • house6_ruling_planet (SATURN) IN house10 → FALSE ❌ (Saturn in House 11, not 10)")
    print("   • house10_ruling_planet (SATURN) IN house6 → FALSE ❌ (Saturn in House 11, not 6)")
    print("   Result: FALSE ❌")
    
    print("\n2. WITH Ruling Planet:")
    print("   • house6_ruling_planet (SATURN) IN house10 WITH house10_ruling_planet (SATURN) → FALSE ❌")
    print("   • house10_ruling_planet (SATURN) IN house6 WITH house6_ruling_planet (SATURN) → FALSE ❌")
    print("   Result: FALSE ❌")
    
    print("\n3. Together:")
    print("   • House6_Ruling_Planet (SATURN) TOGETHER_WITH house10_ruling_planet (SATURN) → TRUE ✅")
    print("   (Same planet, so they are 'together' by definition)")
    print("   Result: TRUE ✅")
    
    print("\n4. Nakshatra:")
    print("   • House6_Ruling_Planet (SATURN) IN_STAR_OF house10_ruling_planet (SATURN) → FALSE ❌")
    print("   • house10_ruling_planet (SATURN) IN_STAR_OF House6_Ruling_Planet (SATURN) → FALSE ❌")
    print("   (Same planet can't be in its own star)")
    print("   Result: FALSE ❌")
    
    print("\n5. Aspecting:")
    print("   • House6_Ruling_Planet (SATURN) IS_ASPECTING house10_ruling_planet (SATURN) → FALSE ❌")
    print("   • house10_ruling_planet (SATURN) IS_ASPECTING House6_Ruling_Planet (SATURN) → FALSE ❌")
    print("   (Same planet can't aspect itself)")
    print("   Result: FALSE ❌")
    
    print("\nFINAL RESULT: TRUE (1 out of 5 relationships satisfied - 'Together')")

def show_api_usage():
    """Show how to use the house ruling planet relationship API"""
    print("\n" + "=" * 80)
    print("📡 API USAGE INSTRUCTIONS")
    print("=" * 80)
    
    print("🔧 New Endpoint: /api/rule-engine/house-ruling-planet-relationship")
    print("Method: POST")
    print("Authentication: Bearer token required")
    
    print("\n📝 Request Body:")
    request_body = {
        "user_profile_id": "1",
        "member_profile_id": "1",
        "query": "House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet",
        "chart_type": "D1"
    }
    print(json.dumps(request_body, indent=2))
    
    print("\n🌐 Curl Command:")
    print(f'curl -X POST "{BASE_URL}/api/rule-engine/house-ruling-planet-relationship" \\')
    print('  -H "Content-Type: application/json" \\')
    print('  -H "Authorization: Bearer YOUR_TOKEN" \\')
    print(f"  -d '{json.dumps(request_body)}'")
    
    print("\n📊 Expected Response Structure:")
    expected_response = {
        "success": True,
        "query": "House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet",
        "chart_type": "D1",
        "overall_result": True,
        "house1_ruling_planet": "SATURN",
        "house2_ruling_planet": "SATURN",
        "relationships": {
            "basic_position": False,
            "with_ruling_planet": False,
            "together": True,
            "nakshatra": False,
            "aspecting": False
        },
        "details": {
            "basic_position": "SATURN in House 11, SATURN in House 11 - no cross-house placement",
            "with_ruling_planet": "No WITH ruling planet relationship found",
            "together": "SATURN and SATURN are both in House 11",
            "nakshatra": "No nakshatra relationship between SATURN and SATURN",
            "aspecting": "No aspecting relationship between SATURN and SATURN"
        },
        "user_profile_id": "1",
        "member_profile_id": "1"
    }
    print(json.dumps(expected_response, indent=2))

def show_individual_queries():
    """Show the individual queries that are checked"""
    print("\n" + "=" * 80)
    print("🔍 INDIVIDUAL QUERIES BREAKDOWN")
    print("=" * 80)
    
    individual_queries = [
        {
            "category": "1. Basic Position",
            "queries": [
                "house6_ruling_planet IN house10",
                "house10_ruling_planet IN house6"
            ],
            "expected": [False, False]
        },
        {
            "category": "2. WITH Ruling Planet",
            "queries": [
                "house6_ruling_planet IN house10 WITH house10_ruling_planet",
                "house10_ruling_planet IN house6 WITH house6_ruling_planet"
            ],
            "expected": [False, False]
        },
        {
            "category": "3. Together",
            "queries": [
                "House6_Ruling_Planet TOGETHER_WITH house10_ruling_planet"
            ],
            "expected": [True]
        },
        {
            "category": "4. Nakshatra (Star)",
            "queries": [
                "House6_Ruling_Planet IN_STAR_OF house10_ruling_planet",
                "house10_ruling_planet IN_STAR_OF House6_Ruling_Planet"
            ],
            "expected": [False, False]
        },
        {
            "category": "5. Aspecting",
            "queries": [
                "House6_Ruling_Planet IS_ASPECTING house10_ruling_planet",
                "house10_ruling_planet IS_ASPECTING House6_Ruling_Planet"
            ],
            "expected": [False, False]
        }
    ]
    
    print("The house ruling planet relationship check evaluates these individual queries:")
    
    for category_data in individual_queries:
        print(f"\n{category_data['category']}:")
        for i, (query, expected) in enumerate(zip(category_data['queries'], category_data['expected'])):
            status = "✅ TRUE" if expected else "❌ FALSE"
            print(f"  • {query} → {status}")

def show_other_examples():
    """Show other example queries"""
    print("\n" + "=" * 80)
    print("🧪 OTHER EXAMPLE QUERIES")
    print("=" * 80)
    
    examples = [
        {
            "query": "House1_Ruling_Planet IS RELATED TO House7_Ruling_Planet",
            "description": "Check relationship between 1st house ruler (Mars) and 7th house ruler (Venus)",
            "expected": "Likely TRUE (different planets, may have relationships)"
        },
        {
            "query": "House9_Ruling_Planet IS RELATED TO House5_Ruling_Planet",
            "description": "Check relationship between 9th house ruler (Jupiter) and 5th house ruler (Sun)",
            "expected": "Depends on chart positions"
        },
        {
            "query": "House2_Ruling_Planet IS RELATED TO House8_Ruling_Planet",
            "description": "Check relationship between 2nd house ruler (Venus) and 8th house ruler (Mars)",
            "expected": "Depends on chart positions"
        }
    ]
    
    print("Other house ruling planet relationship queries you can test:")
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['query']}")
        print(f"   Description: {example['description']}")
        print(f"   Expected: {example['expected']}")

def show_postman_tests():
    """Show Postman test cases for house ruling planet relationships"""
    print("\n" + "=" * 80)
    print("📋 POSTMAN TEST CASES")
    print("=" * 80)
    
    test_cases = [
        {
            "name": "Test Your Main Query",
            "query": "House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet",
            "expected_overall": True,
            "expected_together": True
        },
        {
            "name": "Test Different Houses",
            "query": "House1_Ruling_Planet IS RELATED TO House7_Ruling_Planet",
            "expected_overall": True,
            "expected_together": False
        },
        {
            "name": "Test Same House",
            "query": "House9_Ruling_Planet IS RELATED TO House9_Ruling_Planet",
            "expected_overall": True,
            "expected_together": True
        }
    ]
    
    print("Recommended Postman Test Cases:")
    for i, test in enumerate(test_cases, 1):
        print(f"\n{i}. {test['name']}")
        print(f"   Query: {test['query']}")
        print(f"   Expected Overall: {'TRUE ✅' if test['expected_overall'] else 'FALSE ❌'}")
        
        # Show test script
        print(f"   Test Script:")
        print(f"   pm.test('{test['name']}', function () {{")
        print(f"       pm.response.to.have.status(200);")
        print(f"       const response = pm.response.json();")
        print(f"       pm.expect(response.success).to.be.true;")
        print(f"       pm.expect(response.overall_result).to.be.{'true' if test['expected_overall'] else 'false'};")
        if 'expected_together' in test:
            print(f"       pm.expect(response.relationships.together).to.be.{'true' if test['expected_together'] else 'false'};")
        print(f"   }});")

def main():
    """Main testing function"""
    print("HOUSE RULING PLANET RELATIONSHIP TESTING")
    print("=" * 80)
    print(f"Test started at: {datetime.now()}")
    print("Testing the new 5-rule house ruling planet relationship functionality")
    
    # Run all demonstrations
    test_house_ruling_planet_relationship()
    show_api_usage()
    show_individual_queries()
    show_other_examples()
    show_postman_tests()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY")
    print("=" * 80)
    print("✅ New house ruling planet relationship endpoint implemented")
    print("✅ Checks all 5 relationship types automatically")
    print("✅ Returns detailed results for each relationship")
    print("✅ Handles same planet scenarios correctly")
    print("✅ Your specific query format supported")
    
    print("\n🎯 YOUR QUERY SUPPORT:")
    print("✅ 'House6_Ruling_Planet IS RELATED TO House10_Ruling_Planet'")
    print("✅ Returns all 5 relationship results")
    print("✅ Overall result: TRUE (Together relationship satisfied)")
    
    print("\n📋 NEXT STEPS:")
    print("1. Start Flask server: python run.py")
    print("2. Get authentication token")
    print("3. Test new endpoint: /api/rule-engine/house-ruling-planet-relationship")
    print("4. Use your exact query format")
    print("5. Review detailed relationship breakdown")
    print("6. Add to Postman collection for regular testing")

if __name__ == "__main__":
    main()
