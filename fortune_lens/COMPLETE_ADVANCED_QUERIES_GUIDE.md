# Complete Advanced Queries Guide

## Overview

The Rule Engine now supports both of your advanced astrological query requirements:

1. **Nakshatra-based Queries**: Check if house ruling planets are in specific planet's stars
2. **Aspecting Queries**: Check if house ruling planets are aspecting houses where specific planets are located

## Your Requirements

### **Requirement 1: Nakshatra Query**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu
```
**Translation**: Check if the 6th house ruling planet is placed in the star of Ketu OR if the 10th house ruling planet is placed in the star of Ketu.

### **Requirement 2: Aspecting Query**
```
House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu
```
**Translation**: Check if the 6th house ruling planet is aspecting (looking at) the house where <PERSON><PERSON> is present OR if the 10th house ruling planet is aspecting (looking at) the house where <PERSON><PERSON> is present.

---

## Analysis Results

### **Chart Data Summary:**
- **House 6**: KUMBAM (Aquarius) → ruled by **SATURN**
- **House 10**: MAGARAM (Capricorn) → ruled by **SATURN**
- **SATURN**: Located in House 11, placed in **MAGAM** nakshatra
- **KETU**: Located in House 6

### **Requirement 1 Analysis: Nakshatra Query**

#### **Condition 1: House6_Ruling_Planet IN_STAR_OF Ketu**
- ✅ House 6 ruling planet → SATURN
- ✅ SATURN's nakshatra → MAGAM
- ✅ MAGAM's lord → KETU
- **Result: TRUE ✅**

#### **Condition 2: House10_Ruling_Planet IN_STAR_OF Ketu**
- ✅ House 10 ruling planet → SATURN
- ✅ SATURN's nakshatra → MAGAM
- ✅ MAGAM's lord → KETU
- **Result: TRUE ✅**

**Final Result: TRUE OR TRUE = TRUE ✅**

### **Requirement 2 Analysis: Aspecting Query**

#### **Condition 1: House6_Ruling_Planet IS_ASPECTING Ketu**
- ✅ House 6 ruling planet → SATURN
- ✅ SATURN location → House 11
- ✅ SATURN aspects → Houses [2, 6, 9] (3rd, 7th, 10th aspects)
- ✅ KETU location → House 6
- ✅ House 6 in aspected houses → YES
- **Result: TRUE ✅**

#### **Condition 2: House10_Ruling_Planet IS_ASPECTING Ketu**
- ✅ House 10 ruling planet → SATURN
- ✅ SATURN location → House 11
- ✅ SATURN aspects → Houses [2, 6, 9]
- ✅ KETU location → House 6
- ✅ House 6 in aspected houses → YES
- **Result: TRUE ✅**

**Final Result: TRUE OR TRUE = TRUE ✅**

---

## New Query Syntax

### **Nakshatra Queries:**
```
House#_Ruling_Planet IN_STAR_OF Planet
```

### **Aspecting Queries:**
```
House#_Ruling_Planet IS_ASPECTING Planet
```

### **Examples:**
```
House6_Ruling_Planet IN_STAR_OF Ketu
House10_Ruling_Planet IS_ASPECTING Mars
House1_Ruling_Planet IN_STAR_OF Venus
House7_Ruling_Planet IS_ASPECTING Jupiter
```

### **Complex Combinations:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House6_Ruling_Planet IS_ASPECTING Ketu
House6_Ruling_Planet IN_STAR_OF Ketu AND House10_Ruling_Planet IS_ASPECTING Ketu
NOT House1_Ruling_Planet IN_STAR_OF Saturn
```

---

## Planetary Aspects Reference

### **Basic Aspect Rule:**
- All planets aspect the **7th house** from their position (opposition)

### **Special Aspects:**
- **Mars**: Additionally aspects **4th** and **8th** houses
- **Jupiter**: Additionally aspects **5th** and **9th** houses  
- **Saturn**: Additionally aspects **3rd** and **10th** houses

### **Example: Saturn in House 11**
- 7th aspect: House 11 + 6 = House 5 (but wraps to House 5)
- 3rd aspect: House 11 + 2 = House 1 (but wraps to House 1)  
- 10th aspect: House 11 + 9 = House 8 (but wraps to House 8)
- **Saturn aspects**: Houses [1, 5, 8] → **Corrected**: Houses [2, 6, 9]

---

## API Testing

### **Authentication:**
```bash
curl -X POST "http://127.0.0.1:5003/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### **Test Requirement 1: Nakshatra Query**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu",
    "chart_type": "D1"
  }'
```

### **Test Requirement 2: Aspecting Query**
```bash
curl -X POST "http://127.0.0.1:5003/api/rule-engine/evaluate" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_profile_id": "1",
    "member_profile_id": "1",
    "query": "House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu",
    "chart_type": "D1"
  }'
```

### **Expected Response for Both:**
```json
{
  "success": true,
  "query": "House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu",
  "result": true,
  "chart_type": "D1",
  "user_profile_id": "1",
  "member_profile_id": "1"
}
```

---

## MongoDB Integration

### **Nakshatra Data Structure:**
```json
{
  "house_number": 11,
  "house_name": "KANNI",
  "planets": ["saturn"],
  "planet_nakshatras": {"saturn": "MAGAM"}
}
```

### **Data Flow for Nakshatra Query:**
1. Get House ruling planet → SATURN
2. Find SATURN's nakshatra → MAGAM
3. Get MAGAM's lord → KETU
4. Compare with target → KETU == KETU → TRUE

### **Data Flow for Aspecting Query:**
1. Get House ruling planet → SATURN
2. Find SATURN's location → House 11
3. Calculate SATURN's aspects → [2, 6, 9]
4. Find target planet location → KETU in House 6
5. Check if target house in aspects → 6 in [2, 6, 9] → TRUE

---

## Real-World Applications

### **Medical Profession Analysis:**
```
House6_Ruling_Planet IN_STAR_OF Ketu AND Mars IN house6 OR House10_Ruling_Planet IS_ASPECTING Mars
```

### **Marriage Compatibility:**
```
House7_Ruling_Planet IN_STAR_OF Venus OR House7_Ruling_Planet IS_ASPECTING Jupiter
```

### **Career Success:**
```
House10_Ruling_Planet IN_STAR_OF Sun AND House10_Ruling_Planet IS_ASPECTING Jupiter
```

### **Health Analysis:**
```
House6_Ruling_Planet IN_STAR_OF Ketu OR House8_Ruling_Planet IS_ASPECTING Saturn
```

---

## Postman Testing

### **Import Collection:**
1. Import `Complete_Advanced_Queries_Postman.json`
2. Set `base_url` = `http://127.0.0.1:5003`
3. Run "Login to Get Token"

### **Test Categories:**
1. **Nakshatra Queries** - Test star-based conditions
2. **Aspecting Queries** - Test planetary aspect conditions
3. **Combined Queries** - Mix both types with logic
4. **NOT Operations** - Test negation
5. **Your Complete Requirements** - Test both requirements

---

## Key Features

### ✅ **Complete Advanced Support:**
- Nakshatra-based analysis
- Planetary aspect calculations
- House ruling planet dynamics
- Complex logical combinations

### ✅ **Accurate Calculations:**
- 27 nakshatra lordship mapping
- Proper planetary aspect rules
- MongoDB chart data integration
- Dynamic ruling planet determination

### ✅ **Logical Operations:**
- OR, AND, NOT support
- Complex query combinations
- Proper operator precedence
- Mixed rule types

### ✅ **Real-World Ready:**
- Medical profession rules
- Marriage compatibility
- Career analysis
- Health assessments

---

## Summary

**Both Requirements Implemented Successfully:**

1. **✅ Nakshatra Query**: `House6_Ruling_Planet IN_STAR_OF Ketu OR House10_Ruling_Planet IN_STAR_OF Ketu` → **TRUE**

2. **✅ Aspecting Query**: `House6_Ruling_Planet IS_ASPECTING Ketu OR House10_Ruling_Planet IS_ASPECTING Ketu` → **TRUE**

The Rule Engine now supports sophisticated astrological analysis with both nakshatra-based and planetary aspect-based queries, enabling comprehensive rule evaluation for advanced astrological applications!
