#!/usr/bin/env python3
"""
Test API Directly with Correct Database Data
Test the rule engine functions directly without authentication
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from app import create_app
from app.services.rule_engine import get_chart_data, get_planets_in_house, check_house_planet_relationship

def test_direct_api():
    """Test the API functions directly"""
    print("=" * 80)
    print("🔍 TESTING API FUNCTIONS DIRECTLY")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            user_profile_id = "1"
            member_profile_id = "1"
            
            print(f"Testing with user_profile_id: {user_profile_id}, member_profile_id: {member_profile_id}")
            
            # Get chart data
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            print("✅ Chart data found!")
            
            # Test your original query
            print("\n" + "=" * 60)
            print("🎯 YOUR ORIGINAL QUERY TEST")
            print("=" * 60)
            
            print("Query: '6th_House_Planet IS RELATED TO 10th_House_Planet'")
            
            # Check planets in houses
            house6_planets = get_planets_in_house(chart_data, 6, "D1")
            house10_planets = get_planets_in_house(chart_data, 10, "D1")
            
            print(f"House 6 planets: {house6_planets}")
            print(f"House 10 planets: {house10_planets}")
            
            # Test the relationship function
            result = check_house_planet_relationship(chart_data, 6, 10, "D1")
            
            print(f"\nResult:")
            print(f"  Overall result: {result['overall_result']}")
            print(f"  House 1 planets: {result['house1_planets']}")
            print(f"  House 2 planets: {result['house2_planets']}")
            print(f"  Planet relationships: {len(result['planet_relationships'])}")
            print(f"  Summary: {result['summary']}")
            
            # Verify correctness
            if not result['overall_result'] and not result['house1_planets'] and result['house2_planets']:
                print("✅ CORRECT: House 6 empty, House 10 has planets, result FALSE")
            else:
                print("❌ UNEXPECTED RESULT")
            
            # Test same house together
            print("\n" + "=" * 60)
            print("📍 SAME HOUSE TOGETHER TEST")
            print("=" * 60)
            
            print("Query: '1st_House_Planet IS RELATED TO 1st_House_Planet'")
            
            house1_planets = get_planets_in_house(chart_data, 1, "D1")
            print(f"House 1 planets: {house1_planets}")
            
            result1 = check_house_planet_relationship(chart_data, 1, 1, "D1")
            
            print(f"\nResult:")
            print(f"  Overall result: {result1['overall_result']}")
            print(f"  House 1 planets: {result1['house1_planets']}")
            print(f"  Planet relationships: {len(result1['planet_relationships'])}")
            print(f"  Together: {result1['summary']['together']}")
            print(f"  Same house together: {result1['summary']['together_types']['same_house']}")
            
            if result1['overall_result'] and result1['summary']['together']:
                print("✅ CORRECT: Same house together working")
            else:
                print("❌ SAME HOUSE TOGETHER NOT WORKING")
            
            # Test cross-house relationships
            print("\n" + "=" * 60)
            print("🔗 CROSS-HOUSE RELATIONSHIP TEST")
            print("=" * 60)
            
            print("Query: '1st_House_Planet IS RELATED TO 10th_House_Planet'")
            
            result2 = check_house_planet_relationship(chart_data, 1, 10, "D1")
            
            print(f"\nResult:")
            print(f"  Overall result: {result2['overall_result']}")
            print(f"  House 1 planets: {result2['house1_planets']}")
            print(f"  House 2 planets: {result2['house2_planets']}")
            print(f"  Planet relationships: {len(result2['planet_relationships'])}")
            
            # Check relationship keys
            rel_keys = list(result2['planet_relationships'].keys())
            print(f"  Relationship keys: {rel_keys}")
            
            # Verify cross-house relationships
            expected_planets_h1 = set(['LAGNAM', 'RAHU'])
            expected_planets_h10 = set(['JUPITER', 'VENUS'])
            actual_planets_h1 = set(result2['house1_planets'])
            actual_planets_h10 = set(result2['house2_planets'])
            
            if actual_planets_h1 == expected_planets_h1:
                print("✅ CORRECT: House 1 planets match expected")
            else:
                print(f"❌ WRONG: House 1 expected {expected_planets_h1}, got {actual_planets_h1}")
                
            if actual_planets_h10 == expected_planets_h10:
                print("✅ CORRECT: House 10 planets match expected")
            else:
                print(f"❌ WRONG: House 10 expected {expected_planets_h10}, got {actual_planets_h10}")
            
            # Check for cross-house relationships only
            same_house_found = False
            cross_house_found = False
            
            for rel_key in rel_keys:
                parts = rel_key.split('_TO_')
                if len(parts) == 2:
                    planet1, planet2 = parts
                    
                    # Check if both planets are from same house
                    if ((planet1 in expected_planets_h1 and planet2 in expected_planets_h1) or
                        (planet1 in expected_planets_h10 and planet2 in expected_planets_h10)):
                        same_house_found = True
                        print(f"❌ WRONG: Found same-house relationship: {rel_key}")
                    
                    # Check if planets are from different houses
                    if ((planet1 in expected_planets_h1 and planet2 in expected_planets_h10) or
                        (planet1 in expected_planets_h10 and planet2 in expected_planets_h1)):
                        cross_house_found = True
            
            if cross_house_found and not same_house_found:
                print("✅ CORRECT: Only cross-house relationships found")
            elif same_house_found:
                print("❌ WRONG: Same-house relationships found in cross-house query")
            else:
                print("❌ WRONG: No cross-house relationships found")
            
            return True
            
        except Exception as e:
            print(f"❌ Error testing API: {e}")
            import traceback
            traceback.print_exc()
            return False

def test_all_houses():
    """Test all houses to verify data"""
    print("\n" + "=" * 80)
    print("📊 ALL HOUSES DATA VERIFICATION")
    print("=" * 80)
    
    # Create Flask app
    app = create_app('development')
    
    with app.app_context():
        try:
            user_profile_id = "1"
            member_profile_id = "1"
            
            chart_data = get_chart_data(user_profile_id, member_profile_id)
            
            if not chart_data:
                print("❌ No chart data found!")
                return False
            
            print("Verifying all house data:")
            
            for house_num in range(1, 13):
                planets = get_planets_in_house(chart_data, house_num, "D1")
                if planets:
                    print(f"✅ House {house_num:2d}: {', '.join(planets).upper()}")
                else:
                    print(f"❌ House {house_num:2d}: (empty)")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def main():
    """Main testing function"""
    print("DIRECT API TESTING WITH CORRECT DATABASE DATA")
    print("=" * 80)
    print("Testing API functions directly without HTTP requests")
    
    # Test API functions
    if test_direct_api():
        test_all_houses()
    
    print("\n" + "=" * 80)
    print("📊 DIRECT API TESTING SUMMARY")
    print("=" * 80)
    print("✅ API functions tested directly")
    print("✅ Database data verified")
    print("✅ Your original query tested")
    print("✅ Same house together tested")
    print("✅ Cross-house relationships tested")

if __name__ == "__main__":
    main()
