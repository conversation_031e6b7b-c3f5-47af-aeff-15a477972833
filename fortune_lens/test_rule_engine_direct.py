#!/usr/bin/env python3
"""
Direct test script for Rule Engine functionality
This script tests the rule engine without going through the API layer
"""

import sys
import os
import json
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'fortune_lens'))

# Import the rule engine functions
from app.services.rule_engine import (
    evaluate_rule, 
    get_chart_data, 
    get_planet_house_mapping,
    debug_chart_structure,
    parse_condition,
    parse_complex_query,
    evaluate_condition,
    evaluate_parsed_query
)

def test_basic_functions():
    """Test basic rule engine functions"""
    print("=" * 60)
    print("TESTING BASIC RULE ENGINE FUNCTIONS")
    print("=" * 60)
    
    # Test parse_condition
    print("\n1. Testing parse_condition:")
    test_conditions = [
        "Moon IN House1",
        "Sun NOT IN House8", 
        "Ketu IN House6 WITH Ruling_Planet",
        "<PERSON>tu IS RELATED TO House6_Ruling_Planet",
        "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet",
        "Invalid Query"
    ]
    
    for condition in test_conditions:
        result = parse_condition(condition)
        print(f"  '{condition}' -> {result}")
    
    # Test parse_complex_query
    print("\n2. Testing parse_complex_query:")
    test_queries = [
        "Moon IN House1",
        "Moon IN House1 OR Sun IN House5",
        "Moon IN House1 AND Sun IN House5",
        "Moon IN House1 AND Sun IN House5 OR Mars IN House10"
    ]
    
    for query in test_queries:
        result = parse_complex_query(query)
        print(f"  '{query}' -> {result}")
    
    # Test evaluate_condition
    print("\n3. Testing evaluate_condition:")
    planet_house_mapping = {
        "SUN": 1,
        "MOON": 4, 
        "MARS": 11,
        "MERCURY": 1,
        "JUPITER": 9,
        "VENUS": 2,
        "SATURN": 7,
        "RAHU": 12,
        "KETU": 6
    }
    
    test_evaluations = [
        ("MOON", "IN", 4, "BASIC"),
        ("MOON", "IN", 1, "BASIC"),
        ("SUN", "NOT IN", 8, "BASIC"),
        ("MARS", "IN", 11, "BASIC")
    ]
    
    for planet, operator, house, condition_type in test_evaluations:
        result = evaluate_condition(planet, operator, house, condition_type, planet_house_mapping)
        print(f"  {planet} {operator} House{house} -> {result}")

def test_chart_data_access():
    """Test chart data access functions"""
    print("\n" + "=" * 60)
    print("TESTING CHART DATA ACCESS")
    print("=" * 60)
    
    # Test get_chart_data
    print("\n1. Testing get_chart_data:")
    user_profile_id = 1
    member_profile_id = 1
    
    chart_data = get_chart_data(user_profile_id, member_profile_id)
    if chart_data:
        print(f"  ✓ Chart data found for user {user_profile_id}, member {member_profile_id}")
        print(f"  Chart data keys: {list(chart_data.keys())}")
        
        # Test debug_chart_structure
        print("\n2. Testing debug_chart_structure:")
        debug_info = debug_chart_structure(chart_data, "D1")
        print(f"  Debug info: {json.dumps(debug_info, indent=2, default=str)}")
        
        # Test get_planet_house_mapping
        print("\n3. Testing get_planet_house_mapping:")
        planet_mapping = get_planet_house_mapping(chart_data, "D1")
        print(f"  Planet-house mapping: {planet_mapping}")
        
        return chart_data, planet_mapping
    else:
        print(f"  ✗ No chart data found for user {user_profile_id}, member {member_profile_id}")
        return None, None

def test_rule_evaluation(chart_data, planet_mapping):
    """Test rule evaluation with real data"""
    print("\n" + "=" * 60)
    print("TESTING RULE EVALUATION WITH REAL DATA")
    print("=" * 60)
    
    if not chart_data or not planet_mapping:
        print("  ✗ No chart data available for testing")
        return
    
    print(f"\nAvailable planets and their houses: {planet_mapping}")
    
    # Test various rule types
    test_rules = [
        "Moon IN House4",
        "Sun IN House1 AND Mercury IN House1", 
        "Moon IN House1 OR Moon IN House4 OR Moon IN House7",
        "Jupiter IN House9",
        "Saturn NOT IN House8",
        "Rahu IN House12",
        "Ketu IN House6"
    ]
    
    print("\nTesting rules:")
    for rule in test_rules:
        try:
            result = evaluate_rule(rule, 1, 1, "D1")
            if result.get('success'):
                print(f"  ✓ '{rule}' -> {result['result']}")
            else:
                print(f"  ✗ '{rule}' -> Error: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"  ✗ '{rule}' -> Exception: {str(e)}")

def test_advanced_rules():
    """Test advanced rule types"""
    print("\n" + "=" * 60)
    print("TESTING ADVANCED RULE TYPES")
    print("=" * 60)
    
    advanced_rules = [
        "Ketu IN House6 WITH Ruling_Planet",
        "Ketu IS RELATED TO House6_Ruling_Planet", 
        "Ketu IS ASPECTING_BIRTH House6_Ruling_Planet",
        "Mars IN House11 WITH Ruling_Planet"
    ]
    
    print("\nTesting advanced rules:")
    for rule in advanced_rules:
        try:
            result = evaluate_rule(rule, 1, 1, "D1")
            if result.get('success'):
                print(f"  ✓ '{rule}' -> {result['result']}")
            else:
                print(f"  ✗ '{rule}' -> Error: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"  ✗ '{rule}' -> Exception: {str(e)}")

def test_error_handling():
    """Test error handling scenarios"""
    print("\n" + "=" * 60)
    print("TESTING ERROR HANDLING")
    print("=" * 60)
    
    error_test_cases = [
        # Invalid user/member
        ("Moon IN House1", 999, 999, "D1"),
        # Invalid query format
        ("Invalid Query Format", 1, 1, "D1"),
        # Invalid chart type
        ("Moon IN House1", 1, 1, "INVALID"),
        # Empty query
        ("", 1, 1, "D1")
    ]
    
    print("\nTesting error scenarios:")
    for query, user_id, member_id, chart_type in error_test_cases:
        try:
            result = evaluate_rule(query, user_id, member_id, chart_type)
            if result.get('success'):
                print(f"  ? '{query}' (user:{user_id}, member:{member_id}, chart:{chart_type}) -> Unexpected success: {result['result']}")
            else:
                error_code = result.get('error_code', 'NO_ERROR_CODE')
                print(f"  ✓ '{query}' (user:{user_id}, member:{member_id}, chart:{chart_type}) -> Expected error: {error_code}")
        except Exception as e:
            print(f"  ✓ '{query}' (user:{user_id}, member:{member_id}, chart:{chart_type}) -> Exception: {str(e)}")

def main():
    """Main test function"""
    print("FORTUNE LENS RULE ENGINE - DIRECT TESTING")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")
    
    try:
        # Test basic functions
        test_basic_functions()
        
        # Test chart data access
        chart_data, planet_mapping = test_chart_data_access()
        
        # Test rule evaluation with real data
        test_rule_evaluation(chart_data, planet_mapping)
        
        # Test advanced rules
        test_advanced_rules()
        
        # Test error handling
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\nFATAL ERROR: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
