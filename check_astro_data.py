"""
Check astro data in the database.
"""

import sys
import pymongo
from bson import ObjectId

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Check if the collection exists
collections = db.list_collection_names()
print(f"Collections in the database: {collections}")

# Check if there's any data in the user_member_astro_profile_data collection
if "user_member_astro_profile_data" in collections:
    count = db["user_member_astro_profile_data"].count_documents({})
    print(f"Number of documents in user_member_astro_profile_data: {count}")
    
    # Get a sample document
    sample = db["user_member_astro_profile_data"].find_one()
    if sample:
        print(f"Sample document ID: {sample['_id']}")
        print(f"Sample document member_profile_id: {sample.get('member_profile_id')}")
        
        # Find the corresponding member profile
        member_id = sample.get('member_profile_id')
        if member_id:
            member = db["member_profile"].find_one({"_id": member_id})
            if member:
                print(f"Found member profile with ID: {member['_id']}")
                print(f"Member profile user_profile_id: {member.get('user_profile_id')}")
                print(f"Member profile member_profile_id: {member.get('member_profile_id')}")
            else:
                print(f"No member profile found with ID: {member_id}")
else:
    print("user_member_astro_profile_data collection not found")

# Check if there's any data in the member_profile collection
if "member_profile" in collections:
    count = db["member_profile"].count_documents({})
    print(f"\nNumber of documents in member_profile: {count}")
    
    # Get all member profiles with user_profile_id=1
    members = list(db["member_profile"].find({"user_profile_id": 1}))
    print(f"Number of member profiles with user_profile_id=1: {len(members)}")
    
    for member in members:
        print(f"Member ID: {member['_id']}, member_profile_id: {member.get('member_profile_id')}")
        
        # Check if there's astro data for this member
        astro_data = db["user_member_astro_profile_data"].find_one({"member_profile_id": member["_id"]})
        if astro_data:
            print(f"  Found astro data with ID: {astro_data['_id']}")
        else:
            print("  No astro data found for this member")
else:
    print("member_profile collection not found")
