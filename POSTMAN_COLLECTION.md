# Fortune Lens API - Postman Collection

This document provides information about the Postman collection for testing the Fortune Lens API.

## Importing the Collection

1. Open Postman
2. Click on "Import" in the top left corner
3. Select "Link" and enter the following URL: `https://www.getpostman.com/collections/YOUR_COLLECTION_ID`
   (Replace YOUR_COLLECTION_ID with the actual collection ID)
4. Click "Import"

## Environment Setup

Create an environment with the following variables:

- `base_url`: The base URL of your API (e.g., `http://localhost:5000/api`)
- `access_token`: This will be automatically set after successful login

## Available Endpoints

### Authentication

#### Login
- **Method**: POST
- **URL**: `{{base_url}}/login`
- **Body**:
```json
{
    "email": "<EMAIL>",
    "password": "your_password"
}
```
- **Response**: Contains access token which is automatically set in the environment

### User Management

#### Register User
- **Method**: POST
- **URL**: `{{base_url}}/register`
- **Body**:
```json
{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "mobile": "1234567890"
}
```

#### Verify OTP
- **Method**: POST
- **URL**: `{{base_url}}/verify-otp`
- **Body**:
```json
{
    "email": "<EMAIL>",
    "otp": "123456"
}
```

### Member Profiles

#### Create Member Profile
- **Method**: POST
- **URL**: `{{base_url}}/member-profiles`
- **Headers**: Authorization: Bearer {{access_token}}
- **Body**:
```json
{
    "name": "Jane Doe",
    "gender": "female",
    "date_of_birth": "1990-01-01",
    "time_of_birth": "12:00",
    "place_of_birth": "New York",
    "latitude": 40.7128,
    "longitude": -74.0060
}
```

#### Get Member Profiles
- **Method**: GET
- **URL**: `{{base_url}}/member-profiles`
- **Headers**: Authorization: Bearer {{access_token}}

#### Get Member Profile by ID
- **Method**: GET
- **URL**: `{{base_url}}/member-profiles/1`
- **Headers**: Authorization: Bearer {{access_token}}

### Charts

#### Get Available Chart Types
- **Method**: GET
- **URL**: `{{base_url}}/charts/types`

#### Generate All Charts
- **Method**: POST
- **URL**: `{{base_url}}/charts/generate`
- **Headers**: Authorization: Bearer {{access_token}}
- **Body**:
```json
{
    "profile_id": 1
}
```

#### Generate Divisional Chart
- **Method**: POST
- **URL**: `{{base_url}}/charts/divisional`
- **Headers**: Authorization: Bearer {{access_token}}
- **Body**:
```json
{
    "profile_id": 1,
    "chart_type": "D1"
}
```

### Marriage Matching

#### Analyze Marriage Compatibility
- **Method**: POST
- **URL**: `{{base_url}}/marriage-matching`
- **Headers**: Authorization: Bearer {{access_token}}
- **Body**:
```json
{
    "bride_id": 36,
    "groom_id": 6
}
```

#### Get Marriage Compatibility
- **Method**: GET
- **URL**: `{{base_url}}/marriage-matching/36/6`
- **Headers**: Authorization: Bearer {{access_token}}

### Tamil Panchanga

#### Get Daily Panchanga
- **Method**: GET
- **URL**: `{{base_url}}/panchanga/daily?date=2023-01-01`

#### Get Monthly Panchanga
- **Method**: GET
- **URL**: `{{base_url}}/panchanga/monthly?month=1&year=2023`

#### Get Yearly Festivals
- **Method**: GET
- **URL**: `{{base_url}}/panchanga/festivals?year=2023`

## Testing Workflow

1. Register a new user
2. Verify OTP
3. Login to get access token
4. Create member profiles (at least one male and one female)
5. Generate charts for both profiles
6. Test marriage matching between the profiles
7. Test Tamil panchanga functionality

## Troubleshooting

- If you get a 401 Unauthorized error, make sure your access token is valid
- If you get a 400 Bad Request error, check your request body for missing or invalid fields
- If you get a 404 Not Found error, make sure the resource exists
- If you get a 500 Internal Server Error, check the server logs for more information
