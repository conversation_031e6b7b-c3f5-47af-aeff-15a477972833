from data_loading import *
from medical_profession import *

dataframes = read_excel_sheets(fileName)
# dfs = [dataframes["Planets_exalt_debilitate"], dataframes["star"], dataframes["planets_friends_neutral_enemies"],
#        dataframes["planets_aspects"]]
# merged_df = merge_dataframes(dfs)
# print(dataframes)

#
# user_input = input("Enter the user_id or 'ALL': ")
#
# if user_input.upper() == "ALL":
#     # Retrieve all planet placements
#     all_placements = dataframes['user_birth_chart']
#     # print(all_placements)
#     user_data = []
#     over_all_data = []
#
#     for user_id in all_placements['user_id'].unique():
#         rule_check = {}
#         ru_ch = []
#         print(f"user_id: {user_id}")
#         print("Lagna style")
#         placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
#         ascendant = placements[0]['house_name']['house_name_1']
#         house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
#         house_planet_mapping = placements[0]['planet_house']
#         file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
#         df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
#
#         rule_check['Rule 1'], aas = rule_one(df)
#         rule_check['Rule 2'], ji = rule_two(placements, dataframes, df)
#         rule_check['Rule 3'], mk = rule_three(placements, dataframes, df)
#         main(df, placements, dataframes)
#
#         for rule_name, rule_conditions in rule_check.items():
#             status, percentage = check_rule_status_with_percentage(rule_conditions)
#
#             rule_info = {
#                 'rule_name': rule_name,
#                 'status': status,
#                 'percentage': percentage
#             }
#             ru_ch.append(rule_info)
#             # print(f"{rule_name}: {status}, {percentage:.2f}%, True")
#
#         user_info = {
#             'user_id': int(user_id),
#             'rules': ru_ch
#         }
#         user_data.append(user_info)
#         over_all_data.append(user_info)
#     # print(over_all_data)
#     # print(user_data)
#     # print(f"Chart for user_id {user_id} plotted.")
#     overall_percentages = calculate_rule_pass_percentages(over_all_data)
#     print(overall_percentages)
#
#     # planets_to_check = ['MOON', 'SUN']
#     # houses = find_house_names(placements, planets_to_check)
#     # for planet, house in houses.items():
#     #     print(f"\n{planet}_style is in {house}")
#     #     df_rotated = rotate_houses_from_planet(df, planet)
#     #     rule_check['Rule 1'], aas = rule_one(df_rotated)
#     #     rule_check['Rule 2'], ji = rule_two(placements, dataframes, df_rotated)
#     #     rule_check['Rule 3'], mk = rule_three(placements, dataframes, df_rotated)
#     #     main(df_rotated, placements, dataframes)
#     #     for rule_name, rule_conditions in rule_check.items():
#     #         status, percentage = check_rule_status_with_percentage(rule_conditions)
#     #         print(f"{rule_name}: {status}, {percentage:.2f}%, True")
#     #     print(rule_check)
#     #     print(f"Chart for user_id {user_id} plotted.")
# else:
#     rule_check = {}  # Retrieve planet placements for the given user_id
#     user_id = int(user_input)
#     placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
#     # print(placements)
#     print("Lagna style")
#     ascendant = placements[0]['house_name']['house_name_1']
#     house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
#     house_planet_mapping = placements[0]['planet_house']
#     file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
#     df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
#     rule_check['Rule 1'], aas = rule_one(df)
#     rule_check['Rule 2'], ji = rule_two(placements, dataframes, df)
#     rule_check['Rule 3'], mk = rule_three(placements, dataframes, df)
#     main(df, placements, dataframes)
#     for rule_name, rule_conditions in rule_check.items():
#         status, percentage = check_rule_status_with_percentage(rule_conditions)
#         print(f"{rule_name}: {status}, {percentage:.2f}%")
#     print(rule_check)
#     print(f"Chart for user_id {user_id} plotted.")
#     # print(df)
#     planets_to_check = ['MOON', 'SUN']
#     houses = find_house_names(placements, planets_to_check)
#     for planet, house in houses.items():
#         print(f"\n{planet}_style is in {house}")
#         df_rotated = rotate_houses_from_planet(df, planet)
#         rule_check['Rule 1'], aas = rule_one(df_rotated)
#         rule_check['Rule 2'], ji = rule_two(placements, dataframes, df_rotated)
#         rule_check['Rule 3'], mk = rule_three(placements, dataframes, df_rotated)
#         main(df_rotated, placements, dataframes)
#         for rule_name, rule_conditions in rule_check.items():
#             status, percentage = check_rule_status_with_percentage(rule_conditions)
#             print(f"{rule_name}: {status}, {percentage:.2f}%, True")
#         print(rule_check)
#         print(f"Chart for user_id {user_id} plotted.")

import pandas as pd


def filter_by_profession(dataframe, profession):
    """
    Filters the dataframe to include only rows where the user_profession matches the specified profession.

    Parameters:
        dataframe (pd.DataFrame): The dataframe containing user data.
        profession (str): The profession to filter by.

    Returns:

        pd.DataFrame: A filtered dataframe with only the specified profession.
    """

    # print(dataframe['user_profession'])
    filtered_df = dataframe[dataframe['user_profession'] == profession]
    filtered_user_birth_chart = dataframes['user_birth_chart'][
        dataframes['user_birth_chart']['user_id'].isin(filtered_df['user_id'])]
    print(filtered_user_birth_chart)
    # Update dataframes['user_birth_chart'] with the filtered data
    dataframes['user_birth_chart'] = filtered_user_birth_chart

    # Print the updated dataframe to verify
    # print(dataframes['user_birth_chart'])
    # print(filtered_df['user_id'])
    return dataframes['user_birth_chart']


def point_table(datasets, file_name):
    file_path = 'user_data_' + file_name + '.xlsx'
    # Points table
    points_table = {
        '1.1': 10, '2.1.1': 10, '2.1.2': 9, '2.2.1': 8, '2.2.2': 8, '2.2.3': 8, '2.2.4': 7,
        '2.3.1': 9, '2.3.2': 7, '2.3.3': 7, '2.3.4': 7, '2.3.5': 6, '2.4.1': 9, '2.4.2': 7,
        '2.4.3': 7, '2.4.4': 7, '2.4.5': 6, '2.5.1': 8, '2.5.2': 8, '3.1.1': 10, '3.1.2': 9,
        '3.2.1': 8, '3.2.2': 8, '3.2.3': 8, '3.2.4': 7, '3.3.1': 10, '3.3.2': 9, '3.3.3': 8,
        '3.3.4': 8, '3.3.5': 7, '3.4.1': 10, '3.4.2': 9, '3.4.3': 8, '3.4.4': 8, '3.4.5': 7,
        '3.5.1': 10, '3.5.2': 9, '3.5.3': 8, '3.5.4': 8, '3.5.5': 7
    }

    # Flatten and combine data
    flattened_data = []
    for item in datasets:  # This should iterate over datasets directly
        user_id = item['user_id']
        row = {'user_id': user_id}
        for rule_name, sub_rules in item.items():
            if rule_name == 'user_id':
                continue
            if isinstance(sub_rules, dict):
                for condition, result in sub_rules.items():
                    row[condition] = result
                    row[condition + '_points'] = points_table.get(condition, 0) if result else 0
            elif isinstance(sub_rules, list):
                for sub_rule in sub_rules:
                    for condition, result in sub_rule.items():
                        row[condition] = result
                        row[condition + '_points'] = points_table.get(condition, 0) if result else 0
        flattened_data.append(row)

    # Convert to DataFrame
    df = pd.DataFrame(flattened_data)

    # Reorder columns to have user_id first, then booleans, then points
    columns = ['user_id'] + sorted(
        [col for col in df.columns if col != 'user_id' and not col.endswith('_points')]) + sorted(
        [col for col in df.columns if col.endswith('_points')])
    df = df[columns]

    # Calculate total points for each user, only adding the points columns
    total_points = df[['user_id'] + [col for col in df.columns if col.endswith('_points')]].groupby(
        'user_id').sum().reset_index()
    total_points['total_points'] = total_points[[col for col in total_points.columns if col != 'user_id']].sum(axis=1)

    # Print the DataFrames to check
    # print(df)
    # print(total_points[['user_id', 'total_points']])

    # Write the results to an Excel file
    with pd.ExcelWriter(file_path) as writer:
        df.to_excel(writer, sheet_name='Ordered_Data', index=False)
        total_points[['user_id', 'total_points']].to_excel(writer, sheet_name='Total_Points', index=False)


user_input = input("Enter the user_id or 'ALL': ")

if user_input.upper() == "ALL":
    user_profession = input("Enter the user profession: ")

    if user_profession.upper() != "ALL":
        # Retrieve all planet placements
        all_placements = filter_by_profession(dataframes['user_astro_Basic'], user_profession)
        # print(all_placements.columns)
        # print(all_placements)

    else:
        all_placements = dataframes['user_birth_chart']

    user_data = []
    over_all_data = []
    rule_all_details = []

    print("Lagna style")
    for index, row in all_placements.iterrows():
        user_id = row['user_id']
        rule_check = {}
        ru_ch = []

        print(f"user_id: {user_id}")
        # print("Lagna style")
        placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
        ascendant = placements[0]['house_name']['house_name_1']
        house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
        house_planet_mapping = placements[0]['planet_house']
        file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
        df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
        rule_check['Rule 1'], aas = rule_one(df)
        rule_check['Rule 2'], ji = rule_two(placements, dataframes, df)
        rule_check['Rule 3'], mk = rule_three(placements, dataframes, df)
        main(df, placements, dataframes)
        # print(rule_check)

        for rule_name, rule_conditions in rule_check.items():
            status, percentage = check_rule_status_with_percentage(rule_conditions)

            rule_info = {
                'rule_name': rule_name,
                'status': status,
                'percentage': percentage
            }
            ru_ch.append(rule_info)

        user_info = {
            'user_id': int(user_id),
            'rules': ru_ch,
            'profession': user_profession
        }
        user_data.append(user_info)
        over_all_data.append(user_info)
        rule_check['user_id'] = user_id
        rule_all_details.append(rule_check)
    # print(rule_all_details)
    point_table(rule_all_details, 'medical_use_case')
    overall_percentages = calculate_rule_pass_percentages(over_all_data)
    print("Lagna style : ", overall_percentages)
    # print("\nMoon style")
    # user_data1 = []
    # over_all_data1 = []
    # rule_all_details = []
    # for index, row in all_placements.iterrows():
    #     user_id = row['user_id']
    #     rule_check = {}
    #     ru_ch = []
    #
    #     print(f"user_id: {user_id}")
    #
    #     planets_to_check = ['moon']
    #     # print(planets_to_check)
    #     houses = find_house_names(placements, planets_to_check)
    #     # print(houses)
    #     for planet, house in houses.items():
    #         # print(f"\n{planet}_style is in {house}")
    #         df_rotated = rotate_houses_from_planet(df, planet)
    #         rule_check['Rule 1'], aas = rule_one(df_rotated)
    #         rule_check['Rule 2'], ji = rule_two(placements, dataframes, df_rotated)
    #         rule_check['Rule 3'], mk = rule_three(placements, dataframes, df_rotated)
    #         main(df_rotated, placements, dataframes)
    #         for rule_name, rule_conditions in rule_check.items():
    #             status, percentage = check_rule_status_with_percentage(rule_conditions)
    #
    #             rule_infos = {
    #                 'rule_name': rule_name,
    #                 'status': status,
    #                 'percentage': percentage
    #             }
    #             ru_ch.append(rule_infos)
    #
    #         user_infos = {
    #             'user_id': int(user_id),
    #             'rules': ru_ch,
    #             'profession': user_profession
    #         }
    #         user_data1.append(user_infos)
    #         over_all_data1.append(user_infos)
    #         rule_check['user_id'] = user_id
    #         rule_all_details.append(rule_check)
    # # print(rule_all_details)
    # point_table(rule_all_details, 'Moon')
    # # print(user_data1, over_all_data1)
    # overall_percentages_q = calculate_rule_pass_percentages(over_all_data1)
    # print("Moon style : ", overall_percentages_q)

else:
    rule_check = {}
    user_id = int(user_input)
    placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
    print("Lagna style")
    ascendant = placements[0]['house_name']['house_name_1']
    house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
    house_planet_mapping = placements[0]['planet_house']
    file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
    df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
    rule_check['Rule 1'], aas = rule_one(df)
    rule_check['Rule 2'], ji = rule_two(placements, dataframes, df)
    rule_check['Rule 3'], mk = rule_three(placements, dataframes, df)
    main(df, placements, dataframes)
    for rule_name, rule_conditions in rule_check.items():
        status, percentage = check_rule_status_with_percentage(rule_conditions)
        print(f"{rule_name}: {status}, {percentage:.2f}%")
    print(rule_check)
    print(f"Chart for user_id {user_id} plotted.")
    # planets_to_check = ['MOON', 'SUN']
    # houses = find_house_names(placements, planets_to_check)
    # for planet, house in houses.items():
    #     print(f"\n{planet}_style is in {house}")
    #     df_rotated = rotate_houses_from_planet(df, planet)
    #     rule_check['Rule 1'], aas = rule_one(df_rotated)
    #     rule_check['Rule 2'], ji = rule_two(placements, dataframes, df_rotated)
    #     rule_check['Rule 3'], mk = rule_three(placements, dataframes, df_rotated)
    #     main(df_rotated, placements, dataframes)
    #     for rule_name, rule_conditions in rule_check.items():
    #         status, percentage = check_rule_status_with_percentage(rule_conditions)
    #         print(f"{rule_name}: {status}, {percentage:.2f}%, True")
    #     print(rule_check)
    #     print(f"Chart for user_id {user_id} plotted.")
