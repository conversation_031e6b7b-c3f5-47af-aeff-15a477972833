from bulit_in_function import *
from fpdf import FPDF


class PDF(FPDF):
    def header(self):
        self.set_font('Arial', 'B', 12)
        self.cell(0, 10, 'Astrological Rules Output', 0, 1, 'C')

    def chapter_title(self, title):
        self.set_font('Arial', 'B', 12)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(10)

    def chapter_body(self, body):
        self.set_font('Arial', '', 12)
        self.multi_cell(0, 10, body)
        self.ln()


def save_to_pdf(content, filename):
    pdf = PDF()
    pdf.add_page()
    pdf.chapter_title("Output")
    pdf.chapter_body(content)
    pdf.output(filename)


def rule_one(df):
    rules = {}
    placements = find_placements_in_houses(df, 'MOON', [1, 3, 6, 10, 11])
    rules['1.1'] = placements
    output = "Rule 1 :-\n"
    output += f" Moon placed in 1, 3, 6, 10, 11 houses from Lagna : {placements}\n"
    return rules, output


def rule_two(placements, dataframes, df):
    rule_t = {}
    rules_two = []
    output = "\nRule 2 :-\n"
    output += " Rule 2.1\n"
    output += "     Rule 2.1.1 :-\n"

    house_names = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_names, dataframes["house_name"])
    add_planet_names = add_planet_to_ruling_planets('Ketu', ruling_planet_name)
    comparison_results = compare_ruling_planets(add_planet_names, df, [6, 10])
    rule_t['2.1.1'] = any_match_true(comparison_results)
    output += f"""Check if Ketu is placed in the 6th house along with the Ruling Planet of 6th house from Lagna 
    or if Ketu is placed in 10th house along with the ruling planet of 10th House from Lagna : {rule_t['2.1.1']}"""
    # for rer in comparison_results:
    #     output += f"Comparison Results: {rer}\n"
    output += "        Rule 2.1.2 :- \n"
    ru_per = find_placements_in_houses(df, 'KETU', [6, 10])
    rule_t['2.1.2'] = ru_per
    output += f"KETU placements in 6, 10 houses: {ru_per}\n"

    output += "Rule 2.2\n"
    output += "\n   Rule 2.2.1 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    add_planet_names = add_planet_to_ruling_planets('Ketu', ruling_planet_name)
    comparison_results = compare_ruling_planets(add_planet_names, df)
    rule_t['2.2.1'] = any_match_true(comparison_results)
    # for rer in comparison_results:
    #     output += f"Comparison Results: {rer}\n"
    output += f"""Check if Ketu is placed with the 6th house Ruling Planet in any other house
              or if Ketu is placed with the 10th house Ruling Planet in any other house : {rule_t['2.2.1']}\n"""

    output += "     Rule 2.2.2 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name)
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = check_star_in_stars(fy, placements[0]['star']['ketu_star'])
    rule_t['2.2.2'] = ch_fy
    output += f"""Check if Ketu is placed in the Star of the 6th house Ruling Planet
           or if Ketu is placed in the star of the 10th house Ruling Planet: {rule_t['2.2.2']}\n"""

    output += "\n   Rule 2.2.3 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    stars = get_stars_by_planets(dataframes['star'], ['KETU'])
    result = is_user_star_in_planet_stars(fy, stars)
    rule_t['2.2.3'] = result
    output += f"""Check if Ketu is placed in the Star of the 6th house Ruling Planet
                  or if Ketu is placed in the star of the 10th house Ruling Planet: {rule_t['2.2.3']}\n"""
    output += "\n   Rule 2.2.4  :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # print(house_nam, ruling_planet_name, result_df)
    result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    data1 = check_ketu_in_aspects(result_df, df, 'KETU')
    rule_t['2.2.4'] = True in data1['Ketu_Present']
    output += f"""Check if the 6th house Ruling Planet is aspecting (looking at) the house where Ketu is present
    or if the 10th house Ruling Planet is aspecting (looking at) the house where Ketu is present: {rule_t['2.2.4']}"""
    output += f"\n   Rule 2.3\n"
    output += f"\n   Rule 2.3.1 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df, [10, 6])
    rule_t['2.3.1'] = any_match_true(comparison_results)
    output += f"""Check if 6th house lord is placed in the 10th house along with 10th house lord 
            or if 10th house lord is placed in the 6th house along with 6th house lord: {rule_t['2.3.1']}"""

    output += "\n   Rule 2.3.2 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(ruling_planet_name, df, [10, 6])
    rule_t['2.3.2'] = any_match_true(comparison_results)
    output += f"""Check if 6th house lord is placed in the 10th house without the 10th house lord
    or if 10th house lord is placed in the 6th house without the 6th house lord: {rule_t['2.3.2']}"""

    output += "\n   Rule 2.3.3 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df)
    rule_t['2.3.3'] = any_match_true(comparison_results)
    output += f"""Check if 6th house lord is placed in the 10th house without the 10th house lord
        or if 10th house lord is placed in the 6th house without the 6th house lord: {rule_t['2.3.3']}"""
    output += "\n   Rule 2.3.4 :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = check_star_in_stars(fy)
    rule_t['2.3.4'] = ch_fy
    output += f"""Check if the 6th house Ruling Planet is placed in the star of 10th house lord 
    or if the 10th house Ruling Planet is placed in the star of 6th house lord: {rule_t['2.3.4']}\n"""
    output += "\n   Rule 2.3.5  :- \n"
    house_nam = find_houses_names(df, [6, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # print(house_nam, ruling_planet_name, result_df)
    result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    data1 = check_ketu_in_aspects(result_df, df, 'MERCURY')
    data2 = check_ketu_in_aspects(result_df, df, 'SATURN')
    if True in data1['Ketu_Present'] or True in data2['Ketu_Present']:
        rule_t['2.3.5'] = True
    else:
        rule_t['2.3.5'] = False
    output += f"""Check if the 6th house Ruling Planet is aspecting (looking at) the 10th house lord (where ever it is present) 
    or if the 10th house Ruling Planet is aspecting (looking at) the 6th house lord (where ever it is present)t: {rule_t['2.3.5']}"""

    output += f"\n   Rule 2.4\n"
    output += f"\n   Rule 2.4.1 :- \n"
    house_nam = find_houses_names(df, [10, 12])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df, [12, 10])
    rule_t['2.4.1'] = any_match_true(comparison_results)
    output += f"""Check if 10th house lord is placed in the 12th house along with 10th house lord 
                or if 12th house lord is placed in the 10th house along with 6th house lord: {rule_t['2.4.1']}"""
    output += "\n   Rule 2.4.2 :- \n"
    house_nam = find_houses_names(df, [10, 12])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(ruling_planet_name, df, [12, 10])
    rule_t['2.4.2'] = any_match_true(comparison_results)
    output += f"""Check if 10th house lord is placed in the 12th house without the 12th house lord
        or if 12th house lord is placed in the 10th house without the 10th house lord: {rule_t['2.4.2']}"""
    output += "\n   Rule 2.4.3 :- \n"
    house_nam = find_houses_names(df, [10, 12])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df)
    rule_t['2.4.3'] = any_match_true(comparison_results)
    output += f"""Check if 10th house lord is placed in the 12th house without the 12th house lord
            or if 12th house lord is placed in the 10th house without the 10th house lord: {rule_t['2.4.3']}"""
    output += "\n   Rule 2.4.4 :- \n"
    house_nam = find_houses_names(df, [10, 12])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = check_star_in_stars(fy)
    rule_t['2.4.4'] = ch_fy
    output += f"""Check if the 10th house Ruling Planet is placed in the star of 12th house lord 
        or if the 12th house Ruling Planet is placed in the star of 10th house lord: {rule_t['2.4.4']}\n"""
    output += "\n   Rule 2.4.5  :- \n"
    house_nam = find_houses_names(df, [10, 12])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # print(house_nam, ruling_planet_name, result_df)
    result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    ten_house_name = house_nam[0]
    data1 = check_ketu_in_aspects(result_df, df, 'SUN')
    data2 = check_ketu_in_aspects(result_df, df, 'MERCURY')
    if True in data1['Ketu_Present'] or True in data2['Ketu_Present']:
        rule_t['2.4.5'] = True
    else:
        rule_t['2.4.5'] = False
    output += f"""Check if the 10th house Ruling Planet is aspecting (looking at) the 12th house lord (where ever it is present) 
        or if the 12th house Ruling Planet is aspecting (looking at) the 10th house lord (where ever it is present)t: {rule_t['2.4.5']}"""

    output += "\nRule 2.5 \n"
    output += "\n   Rule 2.5.1 :- \n"
    house_nam = find_houses_names(df, [6])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = is_user_star_in_planet_stars(fy, {'star_name': ['ASHWINI', 'AYILAM', 'ANUSHAM', 'POORATADHI']})
    rule_t['2.5.1'] = ch_fy
    output += f"""Check if the 6th house lord's star is Ashwini, Ayilyam, Anusham, Pooratadhi : {rule_t['2.5.1']} """

    output += "\n   Rule 2.5.2 :- \n"
    house_nam = find_houses_names(df, [10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name)
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = is_user_star_in_planet_stars(fy, {'star_name': ['ASHWINI', 'AYILAM', 'ANUSHAM', 'POORATADHI']})
    rule_t['2.5.2'] = ch_fy
    output += f"""Check if the 10th house lord's star is Ashwini, Ayilyam, Anusham, Pooratadhi : {rule_t['2.5.2']} """

    rules_two.append(rule_t)
    return rules_two, output


def rule_three(placements, dataframes, df):
    rule_th = {}
    rules_three = []
    output = "\nRule 3 :-\n"
    output += " Rule 3.1\n"
    output += "     Rule 3.1.1 :-\n"

    house_names = find_houses_names(df, [4, 10])
    ruling_planet_name = finding_ruling_planet_names(house_names, dataframes["house_name"])
    add_planet_names = add_planet_to_ruling_planets('MARS', ruling_planet_name)
    comparison_results = compare_ruling_planets(add_planet_names, df, [4, 10])
    rule_th['3.1.1'] = any_match_true(comparison_results)
    output += f"""Check if Mars is placed in the 4th house along with the Ruling Planet of 4th house from Lagna 
    or if Mars is placed in 10th house along with the ruling planet of 10th House from Lagna : {rule_th['3.1.1']}"""
    output += "     Rule 3.1.2 :-\n"
    placement = find_placements_in_houses(df, 'MARS', [4, 10])
    rule_th['3.1.2'] = placement
    output += f"""Check if Mars is placed in 4th house from Lagna or 10th House from Lagna : {rule_th['3.1.2']}"""
    output += "Rule 3.2\n"
    output += "\n   Rule 3.2.1 :- \n"
    house_nam = find_houses_names(df, [4, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    add_planet_names = add_planet_to_ruling_planets('MARS', ruling_planet_name)
    comparison_results = compare_ruling_planets(add_planet_names, df)
    rule_th['3.2.1'] = any_match_true(comparison_results)

    output += f"""Check if Mars is placed with the 4th house Ruling Planet in any other house 
            or if Mars is placed with the 10th house Ruling Planet in any other house  : {rule_th['3.2.1']}\n"""

    output += "     Rule 3.2.2 :- \n"
    house_nam = find_houses_names(df, [4, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name)
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    ch_fy = check_star_in_stars(fy, placements[0]['star']['mars_star'])
    rule_th['3.2.2'] = ch_fy
    output += f"""Check if Mars is placed in the Star of the 4th house Ruling Planet
     or if Mars is placed in the star of the 10th house Ruling Planet: {rule_th['3.2.2']}\n"""

    output += "\n   Rule 3.2.3 :- \n"
    house_nam = find_houses_names(df, [4, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    stars = get_stars_by_planets(dataframes['star'], ['MARS'])
    result = is_user_star_in_planet_stars(fy, stars)
    rule_th['3.2.3'] = result
    output += f"""Check if the 4th house Ruling Planet is placed in the star of Mars 
    or if the 10th house Ruling Planet is placed in the star of Mars: {rule_th['3.2.3']}\n"""
    output += "\n   Rule 3.2.4  :- \n"
    house_nam = find_houses_names(df, [4, 10])
    ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # print(house_nam, ruling_planet_name, result_df)
    result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    data1 = check_ketu_in_aspects(result_df, df, 'MARS')
    rule_th['3.2.4'] = True in data1['Ketu_Present']
    output += f"""Check if the 4th house Ruling Planet is aspecting the house where Mars is present 
    or if the 10th house Ruling Planet is aspecting (looking at) the house where Mars is present: {rule_th['3.2.4']}"""

    output += f"\n   Rule 3.3\n"
    output += f"\n   Rule 3.3.1 :- \n"
    rule_th['3.3.1'] = check_dynamic_placements(df, ['Sun', 'Mars'], ['SIMMAM', 'MESHAM', 'VIRICHIGAM'])
    output += f"""Check if Mars is placed in SIMMA RASI along with SUN or 
    if SUN is placed in MESHAM RASI or VRICHIGAM RASI along with Mars: {rule_th['3.3.1']}"""
    output += f"\n   Rule 3.3.2 :- \n"
    R1 = check_dynamic_placements(df, ['Sun'], ['SIMMAM'])
    R2 = check_dynamic_placements(df, ['Mars'], ['MESHAM', 'VIRICHIGAM'])
    rule_th['3.3.2'] = R1 or R2
    output += f"""Check if Mars is placed in SIMMA RASI (without SUN) 
    or if SUN is placed in MESHAM RASI or VRICHIGAM RASI (without Mars): {rule_th['3.3.2']}"""

    output += f"\n   Rule 3.3.3 :- \n"
    rule_th['3.3.3'] = check_dynamic_placements(df, ['Sun,Mars'])
    output += f"""Check if Mars and Sun is placed with together : {rule_th['3.3.3']}"""
    output += f"\n   Rule 3.3.4 :- \n"
    rule_th['3.3.4'] = check_dynamic_star_placements(placements[0]['star'], dataframes['star'], ['SUN', 'MARS'])
    output += f"""Check if Mars is placed in the star of Sun or if Sun is placed in the star of Mars : {rule_th['3.3.4']}"""

    output += f"\n   Rule 3.3.5 :- \n"
    rule_th['3.3.5'] = check_aspects(df, dataframes['planets_aspects'], ['MARS', 'SUN'])
    output += f"""Check if Mars is aspecting (looking at) Sun (where ever it is present) 
    or if Sun is aspecting (looking at) Mars (where ever it is present) : {rule_th['3.3.5']}"""

    output += f"\n   Rule 3.4\n"
    output += f"\n   Rule 3.4.1 :- \n"
    rule_th['3.4.1'] = check_dynamic_placements(df, ['Mars', 'jupiter'], ['DHANUSU', 'MEENAM', 'MESHAM', 'VRICHIGAM'])
    output += f"""Check if Mars is placed in DHANUSU RASI or MEENAM RASI along with JUPITER 
    or if JUPITER is placed in MESHAM RASI or VRICHIGAM RASI along with Mars: {rule_th['3.4.1']}"""
    output += f"\n   Rule 3.4.2 :- \n"
    R1 = check_dynamic_placements(df, ['JUPITER'], ['MESHAM', 'MEENAM'])
    R2 = check_dynamic_placements(df, ['Mars'], ['MESHAM', 'DHANUSU'])
    rule_th['3.4.2'] = R1 or R2
    output += f"""Check if Mars is placed in DHANUSU RASI or MEENAM RASI (without JUPITER) 
    or if JUPITER is placed in MESHAM RASI or VRICHIGAM RASI (without Mars): {rule_th['3.4.2']}"""

    output += f"\n   Rule 3.4.3 :- \n"
    rule_th['3.4.3'] = check_dynamic_placements(df, ['Mars', 'Jupiter'])
    output += f"""Check if Mars and Jupiter is placed with together : {rule_th['3.4.3']}"""
    output += f"\n   Rule 3.4.4 :- \n"
    rule_th['3.4.4'] = check_dynamic_star_placements(placements[0]['star'], dataframes['star'], ['MARS', 'JUPITER'])
    output += f"""Check if Mars is placed in the star of JUPITER or if JUPITER is placed in the star of Mars : {rule_th['3.4.4']}"""

    output += f"\n   Rule 3.4.5 :- \n"
    rule_th['3.4.5'] = check_aspects(df, dataframes['planets_aspects'], ['MARS', 'JUPITER'])
    output += f"""Check if Mars is aspecting (looking at) Jupiter (where ever it is present) 
    or if Jupiter is aspecting (looking at) Mars (where ever it is present) : {rule_th['3.4.5']}"""

    output += f"\n   Rule 3.5\n"
    output += f"\n   Rule 3.5.1 :- \n"
    rule_th['3.5.1'] = check_dynamic_placements(df, ['MARS', 'MERCURY'], ['MIDHUNAM', 'KANNI', 'MESHAM', 'VRICHIGAM'])
    output += f"""Check if Mars is placed in MIDHUNAM RASI or KANNI RASI along with MERCURY 
    or if MERC
    URY is placed in MESHAM RASI or VRICHIGAM RASI along with Mars: {rule_th['3.5.1']}"""
    output += f"\n   Rule 3.5.2 :- \n"
    R1 = check_dynamic_placements(df, ['MERCURY'], ['MESHAM', 'VRICHIGAM'])
    R2 = check_dynamic_placements(df, ['Mars'], ['MIDHUNAM', 'KANNI'])
    rule_th['3.5.2'] = R1 or R2
    output += f"""Check if Mars is placed in MIDHUNAM RASI or KANNI RASI (without MERCURY) 
    or if MERCURY is placed in MESHAM RASI or VRICHIGAM RASI (without Mars): {rule_th['3.5.2']}"""

    output += f"\n   Rule 3.5.3 :- \n"
    rule_th['3.5.3'] = check_dynamic_placements(df, ['Mars', 'Mercury'])
    output += f"""Check if Mars and Mercury is placed with together : {rule_th['3.5.3']}"""
    output += f"\n   Rule 3.5.4 :- \n"
    rule_th['3.5.4'] = check_dynamic_star_placements(placements[0]['star'], dataframes['star'], ['MARS', 'MERCURY'])
    output += f"""Check if Mars is placed in the star of MERCURY or if MERCURY is placed in the star of Mars : {rule_th['3.5.4']}"""

    output += f"\n   Rule 3.5.5 :- \n"
    rule_th['3.5.5'] = check_aspects(df, dataframes['planets_aspects'], ['MARS', 'MERCURY'])
    output += f"""Check if Mars is aspecting (looking at) MERCURY (where ever it is present) 
    or if MERCURY is aspecting (looking at) Mars (where ever it is present) : {rule_th['3.5.5']}"""

    # house_nam = find_houses_names(df, [6, 10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df, [10, 6])
    # rule_t['2.3.1'] = any_match_true(comparison_results)
    # output += f"""Check if 6th house lord is placed in the 10th house along with 10th house lord
    #             or if 10th house lord is placed in the 6th house along with 6th house lord: {rule_t['2.3.1']}"""
    #
    # output += "\n   Rule 2.3.2 :- \n"
    # house_nam = find_houses_names(df, [6, 10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(ruling_planet_name, df, [10, 6])
    # rule_t['2.3.2'] = any_match_true(comparison_results)
    # output += f"""Check if 6th house lord is placed in the 10th house without the 10th house lord
    #     or if 10th house lord is placed in the 6th house without the 6th house lord: {rule_t['2.3.2']}"""
    #
    # output += "\n   Rule 2.3.3 :- \n"
    # house_nam = find_houses_names(df, [6, 10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df)
    # rule_t['2.3.3'] = any_match_true(comparison_results)
    # output += f"""Check if 6th house lord is placed in the 10th house without the 10th house lord
    #         or if 10th house lord is placed in the 6th house without the 6th house lord: {rule_t['2.3.3']}"""
    # output += "\n   Rule 2.3.4 :- \n"
    # house_nam = find_houses_names(df, [6, 10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    # fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    # ch_fy = check_star_in_stars(fy)
    # rule_t['2.3.4'] = ch_fy
    # output += f"""Check if the 6th house Ruling Planet is placed in the star of 10th house lord
    #     or if the 10th house Ruling Planet is placed in the star of 6th house lord: {rule_t['2.3.4']}\n"""
    # output += "\n   Rule 2.3.5  :- \n"
    # house_nam = find_houses_names(df, [6, 10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    # result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # # print(house_nam, ruling_planet_name, result_df)
    # result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    # data1 = check_ketu_in_aspects(result_df, df, 'MERCURY')
    # data2 = check_ketu_in_aspects(result_df, df, 'SATURN')
    # if True in data1['Ketu_Present'] or True in data2['Ketu_Present']:
    #     rule_t['2.3.5'] = True
    # else:
    #     rule_t['2.3.5'] = False
    # output += f"""Check if the 6th house Ruling Planet is aspecting (looking at) the 10th house lord (where ever it is present)
    #     or if the 10th house Ruling Planet is aspecting (looking at) the 6th house lord (where ever it is present)t: {rule_t['2.3.5']}"""
    #
    # output += f"\n   Rule 2.4\n"
    # output += f"\n   Rule 2.4.1 :- \n"
    # house_nam = find_houses_names(df, [10, 12])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df, [12, 10])
    # rule_t['2.4.1'] = any_match_true(comparison_results)
    # output += f"""Check if 10th house lord is placed in the 12th house along with 10th house lord
    #                 or if 12th house lord is placed in the 10th house along with 6th house lord: {rule_t['2.4.1']}"""
    # output += "\n   Rule 2.4.2 :- \n"
    # house_nam = find_houses_names(df, [10, 12])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(ruling_planet_name, df, [12, 10])
    # rule_t['2.4.2'] = any_match_true(comparison_results)
    # output += f"""Check if 10th house lord is placed in the 12th house without the 12th house lord
    #         or if 12th house lord is placed in the 10th house without the 10th house lord: {rule_t['2.4.2']}"""
    # output += "\n   Rule 2.4.3 :- \n"
    # house_nam = find_houses_names(df, [10, 12])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # comparison_results = compare_ruling_planets(combine_ruling_planets(ruling_planet_name), df)
    # rule_t['2.4.3'] = any_match_true(comparison_results)
    # output += f"""Check if 10th house lord is placed in the 12th house without the 12th house lord
    #             or if 12th house lord is placed in the 10th house without the 10th house lord: {rule_t['2.4.3']}"""
    # output += "\n   Rule 2.4.4 :- \n"
    # house_nam = find_houses_names(df, [10, 12])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    # fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    # ch_fy = check_star_in_stars(fy)
    # rule_t['2.4.4'] = ch_fy
    # output += f"""Check if the 10th house Ruling Planet is placed in the star of 12th house lord
    #         or if the 12th house Ruling Planet is placed in the star of 10th house lord: {rule_t['2.4.4']}\n"""
    # output += "\n   Rule 2.4.5  :- \n"
    # house_nam = find_houses_names(df, [10, 12])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # result_df = find_house_aspects(dataframes['planets_aspects'], ruling_planet_name)
    # result_df['house_number'] = result_df.apply(lambda row: find_house_number(row, df), axis=1)
    # # print(house_nam, ruling_planet_name, result_df)
    # result_df['Aspects_looking_house_number'] = result_df.apply(adjust_aspects, axis=1)
    # ten_house_name = house_nam[0]
    # data1 = check_ketu_in_aspects(result_df, df, 'SUN')
    # data2 = check_ketu_in_aspects(result_df, df, 'MERCURY')
    # if True in data1['Ketu_Present'] or True in data2['Ketu_Present']:
    #     rule_t['2.4.5'] = True
    # else:
    #     rule_t['2.4.5'] = False
    # output += f"""Check if the 10th house Ruling Planet is aspecting (looking at) the 12th house lord (where ever it is present)
    #         or if the 12th house Ruling Planet is aspecting (looking at) the 10th house lord (where ever it is present)t: {rule_t['2.4.5']}"""
    #
    # output += "\nRule 2.5 \n"
    # output += "\n   Rule 2.5.1 :- \n"
    # house_nam = find_houses_names(df, [6])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    # fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    # ch_fy = is_user_star_in_planet_stars(fy, {'star_name': ['ASHWINI', 'AYILAM', 'ANUSHAM', 'POORATADHI']})
    # rule_t['2.5.1'] = ch_fy
    # output += f"""Check if the 6th house lord's star is Ashwini, Ayilyam, Anusham, Pooratadhi : {rule_t['2.5.1']} """
    #
    # output += "\n   Rule 2.5.2 :- \n"
    # house_nam = find_houses_names(df, [10])
    # ruling_planet_name = finding_ruling_planet_names(house_nam, dataframes["house_name"])
    # updated_houses = add_star_to_ruling_planet(ruling_planet_name, )
    # fy = add_star_details(updated_houses, dataframes['star'], placements[0]['star'])
    # ch_fy = is_user_star_in_planet_stars(fy, {'star_name': ['ASHWINI', 'AYILAM', 'ANUSHAM', 'POORATADHI']})
    # rule_t['2.5.2'] = ch_fy
    # output += f"""Check if the 10th house lord's star is Ashwini, Ayilyam, Anusham, Pooratadhi : {rule_t['2.5.2']} """

    rules_three.append(rule_th)
    return rules_three, output


def main(df, placements, dataframes):
    all_output = ""

    rules_one, output_one = rule_one(df)
    all_output += output_one

    rules_two, output_two = rule_two(placements, dataframes, df)
    all_output += output_two

    rules_three, output_three = rule_three(placements, dataframes, df)
    all_output += output_three

    save_to_pdf(all_output, "astrological_rules_output.pdf")

# Example usage
# df = ... # Your DataFrame
# dataframes = ... # Your additional dataframes
# main(df, dataframes)
