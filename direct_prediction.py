"""
Direct prediction script.
"""

import sys
import os
import json
import pymongo
from bson import ObjectId
from datetime import datetime

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Find the member profile
member_profile = db["member_profile"].find_one({"user_profile_id": 17, "member_profile_id": 1})

if not member_profile:
    print("Member profile not found for user_profile_id=17 and member_profile_id=1")
    sys.exit(1)

member_id = member_profile["_id"]
print(f"Found member profile with ID: {member_id}")

# Get astro data
astro_data = db["user_member_astro_profile_data"].find_one({"member_profile_id": member_id})
if not astro_data:
    print("Astro data not found for the member")
    sys.exit(1)

print(f"Found astro data with ID: {astro_data['_id']}")

# Extract D1 chart (Rasi chart)
d1_chart = astro_data.get('chart_data', {}).get('D1', {})
if not d1_chart:
    print("D1 chart data not found for the member")
    sys.exit(1)

print("D1 chart data found")

# Extract planet positions and house names
planet_positions = {}
house_names = {}
nakshatra_data = {}

# Extract from houses data
houses = d1_chart.get('houses', [])
for house in houses:
    house_number = house.get('house_number')
    house_name = house.get('house_name', '').upper()
    planets = house.get('planets', [])
    planet_nakshatras = house.get('planet_nakshatras', {})
    
    if house_number and planets:
        planet_positions[str(house_number)] = ', '.join(planets)
    
    if house_number and house_name:
        house_names[f'house_name_{house_number}'] = house_name
    
    for planet in planets:
        if planet in planet_nakshatras:
            nakshatra_data[f'{planet}_star'] = planet_nakshatras[planet]

# Add lagna position
lagna_house = d1_chart.get('lagna', {}).get('house_number')
if lagna_house:
    if str(lagna_house) in planet_positions:
        planet_positions[str(lagna_house)] = f"lagnam, {planet_positions[str(lagna_house)]}"
    else:
        planet_positions[str(lagna_house)] = "lagnam"

# Get dasha data
dasha_data = astro_data.get('chart_data', {}).get('dashas', {})

# Get birth date
birth_date = member_profile.get('birth_date')
if not birth_date:
    print("Birth date not found for the member")
    sys.exit(1)

print(f"Birth date: {birth_date}")

# Create placements object
placements = {
    'planet_house': planet_positions,
    'house_name': house_names,
    'star': nakshatra_data,
    'dhasa': dasha_data
}

print("\nPlanet positions:")
for house_num, planets in planet_positions.items():
    print(f"House {house_num}: {planets}")

print("\nHouse names:")
for key, name in house_names.items():
    print(f"{key}: {name}")

print("\nNakshatra data:")
for planet, star in nakshatra_data.items():
    print(f"{planet}: {star}")

print("\nDasha data:")
for period_type, periods in dasha_data.items():
    print(f"{period_type}:")
    if isinstance(periods, list):
        for period in periods:
            print(f"  {period.get('period_name')}: {period.get('start_date')} to {period.get('end_date')}")
    else:
        print(f"  {periods}")

print("\nThis data can be used to manually run the marriage date prediction function.")
print("The implementation in date_prediction.py should work correctly with this data.")
print("To see the detailed output, use the API endpoint with print_output=true parameter.")
