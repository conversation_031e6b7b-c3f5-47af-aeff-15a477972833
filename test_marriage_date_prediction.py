"""
Test script for marriage date prediction functionality.
"""

import sys
import os
import json
from datetime import datetime
from bson import ObjectId

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# Create a mock member profile and astro data for testing
class MockMongo:
    class DB:
        def __init__(self):
            self.collections = {
                'member_profile': MockCollection('member_profile'),
                'user_member_astro_profile_data': Mo<PERSON><PERSON>ollection('user_member_astro_profile_data')
            }

        def __getitem__(self, name):
            return self.collections.get(name, MockCollection(name))

    def __init__(self):
        self.db = self.DB()

class MockCollection:
    def __init__(self, name):
        self.name = name
        self.data = {}

        # Add mock data
        if name == 'member_profile':
            self.data['123456'] = {
                '_id': '123456',
                'member_profile_id': 1,
                'name': 'Test User',
                'gender': 'male',
                'birth_date': '1990-01-01',
                'birth_time': '12:00:00',
                'birth_place': 'Chennai, India',
                'latitude': 13.0827,
                'longitude': 80.2707
            }
        elif name == 'user_member_astro_profile_data':
            self.data['123456'] = {
                '_id': '123456',
                'member_profile_id': 1,
                'chart_data': {
                    'D1': {
                        'chart_info': {
                            'name': 'Rasi Chart',
                            'description': 'Basic birth chart showing planetary positions at birth',
                            'divisional_factor': 1
                        },
                        'houses': [
                            {
                                'house_number': 1,
                                'house_name': 'MESHAM',
                                'planets': ['SUN', 'MERCURY'],
                                'planet_degrees': {'SUN': '16°30\'', 'MERCURY': '12°15\''},
                                'planet_nakshatras': {'SUN': 'BARANI', 'MERCURY': 'ASHWINI'}
                            },
                            {
                                'house_number': 2,
                                'house_name': 'RISHABAM',
                                'planets': ['VENUS'],
                                'planet_degrees': {'VENUS': '25°45\''},
                                'planet_nakshatras': {'VENUS': 'ROHINI'}
                            },
                            {
                                'house_number': 7,
                                'house_name': 'THULAM',
                                'planets': ['SATURN'],
                                'planet_degrees': {'SATURN': '10°20\''},
                                'planet_nakshatras': {'SATURN': 'CHITHIRAI'}
                            },
                            {
                                'house_number': 4,
                                'house_name': 'KADAGAM',
                                'planets': ['MOON'],
                                'planet_degrees': {'MOON': '25°45\''},
                                'planet_nakshatras': {'MOON': 'ASHLESHA'}
                            }
                        ],
                        'dashas': {
                            'antara_dhasa_period': "[('VENUS-JUPITER', '2023-01-01 00:00:00', '2025-12-31 11:59:59'), ('VENUS-SATURN', '2026-01-01 00:00:00', '2028-12-31 11:59:59')]"
                        }
                    }
                }
            }

    def find_one(self, query=None):
        if not query:
            return list(self.data.values())[0] if self.data else None

        if '_id' in query:
            return self.data.get(query['_id'])

        if 'member_profile_id' in query:
            for item in self.data.values():
                if item.get('member_profile_id') == query['member_profile_id']:
                    return item

        return None

# Import the function to test
from fortune_lens.app.services.marriage_matching.date_prediction import predict_marriage_dates

# Test the function with real database
def test_predict_marriage_dates():
    # Get member profile ID for user_profile_id=1 and member_profile_id=1
    from fortune_lens.extensions import mongo
    member_profile = mongo.db.member_profile.find_one({"user_profile_id": 1, "member_profile_id": 1})

    if not member_profile:
        print("Member profile not found for user_profile_id=1 and member_profile_id=1")
        return

    member_id = str(member_profile["_id"])
    print(f"Found member profile with ID: {member_id}")

    # Run prediction with print_output=True
    result = predict_marriage_dates(member_id, marriage_date='2023-05-15', print_output=True)
    print(json.dumps(result, indent=2, default=str))

if __name__ == "__main__":
    test_predict_marriage_dates()
