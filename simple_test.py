"""
Simplified test for marriage date prediction functionality.
"""

import json
from datetime import datetime, timedelta
import random
import string

# Define zodiac signs
RAASI_LIST = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']

def generate_random_string(length=5):
    """Generate a random string of fixed length."""
    return ''.join(random.choices(string.ascii_lowercase + string.digits, k=length))

def parse_datetime_to_date(date_str):
    """
    Parses a datetime string and returns only the date in 'YYYY-MM-DD' format.
    """
    # Ensure date_str is a string
    date_str = str(date_str).strip()

    # If no time part is provided, add a default time '00:00:00'
    if len(date_str) == 10:  # Only date part (YYYY-MM-DD)
        date_str += " 00:00:00"

    # Remove AM/PM suffix if present
    if "AM" in date_str or "PM" in date_str:
        date_str = date_str.replace(" AM", "").replace(" PM", "")

    try:
        # Parse the datetime string and return only the date part
        date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        return date_obj.date()  # Return the date only in 'YYYY-MM-DD' format
    except ValueError:
        try:
            # Try alternative format
            date_obj = datetime.strptime(date_str, "%d-%m-%Y %H:%M:%S")
            return date_obj.date()
        except ValueError:
            print(f"Error: Unable to parse the datetime string '{date_str}'")
            return None

def find_periods_by_ruling_planet(planetary_periods, house_data, dob, start_age=21, end_age=40):
    """
    Finds planetary periods based on the ruling planet of the house and filters by age range.
    """
    # Convert date of birth to a date object
    try:
        dob_date = datetime.strptime(dob, "%Y-%m-%d").date()
    except ValueError:
        try:
            dob_date = datetime.strptime(dob, "%d-%m-%Y").date()
        except ValueError:
            print(f"Error: Unable to parse the date of birth '{dob}'")
            return [], None, None

    age_21_date = dob_date.replace(year=dob_date.year + 21)
    age_40_date = dob_date.replace(year=dob_date.year + 40)

    # List to store all matching periods
    all_matching_periods = []

    # Iterate through houses and their ruling planets
    for house in house_data:
        ruling_planet_name = house.get("ruling_planet_name", "")

        # Filter planetary periods matching the ruling planet
        matching_periods = [
            period for period in planetary_periods
            if ruling_planet_name in period[0]
        ]

        # Add matching periods to the final list
        all_matching_periods.extend(matching_periods)

    # Sort the matching periods by start date (period[1])
    sorted_matching_periods = sorted(
        all_matching_periods, key=lambda x: datetime.strptime(x[1], "%Y-%m-%d")
    )

    # Handle the case when the period starts before 'age_21_date' and ends after 'age_40_date'
    result = []
    for period in sorted_matching_periods:
        start_date = datetime.strptime(period[1], "%Y-%m-%d").date()
        end_date = datetime.strptime(period[2], "%Y-%m-%d").date()

        # Adjust the period to start at least from the 21st age date if the period starts before
        if start_date < age_21_date:
            start_date = age_21_date

        # Adjust the period to end at the 40th age date if the period ends after
        if end_date > age_40_date:
            end_date = age_40_date

        # Append the adjusted period to the result
        result.append((period[0], start_date.strftime('%d-%m-%Y'), end_date.strftime('%d-%m-%Y')))

    # Return the periods in the required format (name, start_date, end_date)
    return result, age_21_date, age_40_date

def handle_continuity_date(data):
    """Handle continuity in date ranges and format them for display."""
    if not data:
        return []

    # Parse data into datetime objects
    parsed_data = [
        (item[0], datetime.strptime(item[1], '%d-%m-%Y'), datetime.strptime(item[2], '%d-%m-%Y'))
        for item in data
    ]

    # Sort by start date
    parsed_data.sort(key=lambda x: x[1])

    # Generate ranges
    ranges = []
    current_start = parsed_data[0][1]
    current_end = parsed_data[0][2]

    for i in range(1, len(parsed_data)):
        _, next_start, next_end = parsed_data[i]

        # Check for continuity
        if next_start <= current_end:
            current_end = max(current_end, next_end)
        else:
            ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")
            current_start = next_start
            current_end = next_end

    # Add last period
    ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")

    return ranges

def remove_duplicates(data):
    """Remove duplicate entries based on ruling_planet_name."""
    seen = set()
    result = []

    for entry in data:
        ruling_planet = entry.get('ruling_planet_name')
        if ruling_planet and ruling_planet not in seen:
            seen.add(ruling_planet)
            result.append(entry)

    return result

def find_stars_by_houses(houses, house_to_planet_data, stars_data, default_planet="VENUS"):
    """Find stars associated with houses based on their ruling planets."""
    result = []
    planets_in_houses = set()

    # For each house, find its ruling planet and stars
    for house in houses:
        # Find the ruling planet for the house
        planet_row = house_to_planet_data.get(house.upper(), {})
        if planet_row:
            planet = planet_row.get("ruling_planet", "")
            # Add planet to the set to prevent duplication
            if planet not in planets_in_houses:
                planets_in_houses.add(planet)
            # Get the stars associated with the planet
            stars = stars_data.get(planet, {}).get("stars", [])
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": planet,
                "Stars": stars
            })
        else:
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": None,
                "Stars": []
            })

    # Add default planet if it's not in planets_in_houses
    if default_planet not in planets_in_houses:
        stars = stars_data.get(default_planet, {}).get("stars", [])
        result.append({
            "house_name": None,
            "ruling_planet_name": default_planet,
            "Stars": stars
        })

    return result

def test_marriage_date_prediction():
    """Test the marriage date prediction functionality with sample data."""
    # Sample birth date
    birth_date = "1990-01-01"
    
    # Sample marriage houses
    marriage_houses = ["RISHABAM", "THULAM"]
    
    # Sample rasi (Moon sign)
    rasi = "KADAGAM"
    
    # Sample dasha periods
    dasha_periods = [
        ("VENUS-JUPITER", "2023-01-01", "2025-12-31"),
        ("VENUS-SATURN", "2026-01-01", "2028-12-31"),
        ("JUPITER-MERCURY", "2029-01-01", "2031-12-31")
    ]
    
    # Reference data for house-planet mapping
    house_planet_mapping = {
        'MESHAM': {'ruling_planet': 'MARS'},
        'RISHABAM': {'ruling_planet': 'VENUS'},
        'MIDUNAM': {'ruling_planet': 'MERCURY'},
        'KADAGAM': {'ruling_planet': 'MOON'},
        'SIMMAM': {'ruling_planet': 'SUN'},
        'KANNI': {'ruling_planet': 'MERCURY'},
        'THULAM': {'ruling_planet': 'VENUS'},
        'VIRICHIGAM': {'ruling_planet': 'MARS'},
        'DHANUSU': {'ruling_planet': 'JUPITER'},
        'MAGARAM': {'ruling_planet': 'SATURN'},
        'KUMBAM': {'ruling_planet': 'SATURN'},
        'MEENAM': {'ruling_planet': 'JUPITER'}
    }
    
    # Reference data for stars
    stars_data = {
        'SUN': {'stars': ['KARTHIGAI', 'UTHIRAM', 'UTHIRADAM']},
        'MOON': {'stars': ['ROHINI', 'HASTHAM', 'THIRUVONAM']},
        'MARS': {'stars': ['MRIGASIRISHAM', 'CHITHIRAI', 'DHANISHTA']},
        'MERCURY': {'stars': ['ASHLESHA', 'JYESHTA', 'REVATHI']},
        'JUPITER': {'stars': ['PUNARVASU', 'VISAKA', 'POORATTATHI']},
        'VENUS': {'stars': ['BHARANI', 'POORAM', 'POORADAM']},
        'SATURN': {'stars': ['PUSHYAMI', 'ANURADHA', 'UTHIRATTATHI']},
        'RAHU': {'stars': ['ARUDRA', 'SWATHI', 'SATHABISHAM']},
        'KETU': {'stars': ['ASWINI', 'MAGAM', 'MOOLA']}
    }
    
    # Find stars by houses
    result_11 = find_stars_by_houses(marriage_houses, house_planet_mapping, stars_data)
    result_11 = remove_duplicates(result_11)
    
    # Find periods by ruling planet
    matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(
        dasha_periods, result_11, birth_date, start_age=21, end_age=40
    )
    
    # Format the results
    result_data = handle_continuity_date(matching_periods_1)
    
    # Prepare the response
    response = {
        'success': True,
        'member': {
            'id': '123456',
            'name': 'Test User',
            'birth_date': birth_date
        },
        'prediction_age_range': {
            'start_age': 21,
            'end_age': 40,
            'start_date': age_21_date.strftime('%d-%m-%Y') if age_21_date else None,
            'end_date': age_40_date.strftime('%d-%m-%Y') if age_40_date else None
        },
        'marriage_houses': marriage_houses,
        'rasi': rasi,
        'potential_marriage_periods': result_data,
        'generated_at': datetime.utcnow().isoformat()
    }
    
    return response

if __name__ == "__main__":
    result = test_marriage_date_prediction()
    print(json.dumps(result, indent=2, default=str))
