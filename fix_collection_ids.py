#!/usr/bin/env python3
"""
Script to fix _id, user_profile_id, and member_profile_id fields in all collections
"""

from pymongo import MongoClient
import json
from bson import json_util
from bson.objectid import ObjectId
from datetime import datetime

# Connect to MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client.fortune_lens

print('=== FIXING COLLECTION IDs ===')

# Step 1: Find the maximum user_profile_id and member_profile_id
max_user_profile_id = 0
max_member_profile_id = 0

for user in db.user_profile.find():
    if 'user_profile_id' in user and isinstance(user['user_profile_id'], int) and user['user_profile_id'] > max_user_profile_id:
        max_user_profile_id = user['user_profile_id']

for member in db.member_profile.find():
    if 'member_profile_id' in member and isinstance(member['member_profile_id'], int) and member['member_profile_id'] > max_member_profile_id:
        max_member_profile_id = member['member_profile_id']

print(f"Maximum user_profile_id: {max_user_profile_id}")
print(f"Maximum member_profile_id: {max_member_profile_id}")

# Step 2: Fix user with integer _id
int_id_user = db.user_profile.find_one({'_id': 1})
if int_id_user:
    print(f"\nFixing user with integer _id: {int_id_user.get('email')}")
    
    # Create a new user document with ObjectId
    new_user = dict(int_id_user)
    new_user['_id'] = ObjectId()
    new_user['user_profile_id'] = max_user_profile_id + 1
    max_user_profile_id += 1
    
    # Insert the new user
    db.user_profile.insert_one(new_user)
    
    # Update references in member_profile collection
    for member in db.member_profile.find({'user_profile_id': 1}):
        if member.get('_id') != 1:  # Skip the member with integer _id if it exists
            db.member_profile.update_one(
                {'_id': member['_id']},
                {'$set': {'user_profile_id': new_user['user_profile_id']}}
            )
    
    # Update references in user_astro_profile_data collection
    for astro in db.user_astro_profile_data.find({'user_profile_id': 1}):
        db.user_astro_profile_data.update_one(
            {'_id': astro['_id']},
            {'$set': {'user_profile_id': new_user['user_profile_id']}}
        )
    
    # Delete the old user
    db.user_profile.delete_one({'_id': 1})
    
    print(f"User fixed: {int_id_user.get('email')} now has user_profile_id={new_user['user_profile_id']}")

# Step 3: Fix duplicate user_profile_id values
print("\nFixing duplicate user_profile_id values...")

# Get all users sorted by user_profile_id
users = list(db.user_profile.find().sort('user_profile_id', 1))

# Create a dictionary to track used user_profile_id values
used_user_profile_ids = {}

for user in users:
    user_id = user['_id']
    user_profile_id = user.get('user_profile_id')
    
    if user_profile_id in used_user_profile_ids:
        # This user_profile_id is already used, assign a new one
        new_user_profile_id = max_user_profile_id + 1
        max_user_profile_id = new_user_profile_id
        
        print(f"Updating user {user.get('email')} from user_profile_id={user_profile_id} to user_profile_id={new_user_profile_id}")
        
        # Update the user
        db.user_profile.update_one(
            {'_id': user_id},
            {'$set': {'user_profile_id': new_user_profile_id}}
        )
        
        # Update references in member_profile collection
        for member in db.member_profile.find({'user_profile_id': user_profile_id}):
            # Check if this member belongs to this user (we need additional criteria)
            # For simplicity, we'll update all members with this user_profile_id
            # In a real scenario, you might need more complex logic
            db.member_profile.update_one(
                {'_id': member['_id']},
                {'$set': {'user_profile_id': new_user_profile_id}}
            )
        
        # Update references in user_astro_profile_data collection
        for astro in db.user_astro_profile_data.find({'user_profile_id': user_profile_id}):
            # Same caveat as above
            db.user_astro_profile_data.update_one(
                {'_id': astro['_id']},
                {'$set': {'user_profile_id': new_user_profile_id}}
            )
        
        # Add the new user_profile_id to the used set
        used_user_profile_ids[new_user_profile_id] = user_id
    else:
        # This user_profile_id is not used yet
        used_user_profile_ids[user_profile_id] = user_id

# Step 4: Fix duplicate member_profile_id values
print("\nFixing duplicate member_profile_id values...")

# Get all members sorted by user_profile_id and member_profile_id
members = list(db.member_profile.find().sort([('user_profile_id', 1), ('member_profile_id', 1)]))

# Create a dictionary to track used member_profile_id values per user
used_member_profile_ids = {}

for member in members:
    member_id = member['_id']
    user_profile_id = member.get('user_profile_id')
    member_profile_id = member.get('member_profile_id')
    
    # Create a unique key for this user
    user_key = str(user_profile_id)
    
    if user_key not in used_member_profile_ids:
        used_member_profile_ids[user_key] = {}
    
    if member_profile_id in used_member_profile_ids[user_key]:
        # This member_profile_id is already used for this user, assign a new one
        new_member_profile_id = max_member_profile_id + 1
        max_member_profile_id = new_member_profile_id
        
        print(f"Updating member {member.get('name')} (user_profile_id={user_profile_id}) from member_profile_id={member_profile_id} to member_profile_id={new_member_profile_id}")
        
        # Update the member
        db.member_profile.update_one(
            {'_id': member_id},
            {'$set': {'member_profile_id': new_member_profile_id}}
        )
        
        # Update references in user_astro_profile_data collection
        for astro in db.user_astro_profile_data.find({'member_profile_id': member_profile_id, 'user_profile_id': user_profile_id}):
            db.user_astro_profile_data.update_one(
                {'_id': astro['_id']},
                {'$set': {'member_profile_id': new_member_profile_id}}
            )
        
        # Add the new member_profile_id to the used set
        used_member_profile_ids[user_key][new_member_profile_id] = member_id
    else:
        # This member_profile_id is not used yet for this user
        used_member_profile_ids[user_key][member_profile_id] = member_id

print("\nDatabase cleanup completed.")
