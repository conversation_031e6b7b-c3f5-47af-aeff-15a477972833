#!/usr/bin/env python
# -*- coding: UTF-8 -*-
# Copyright (C) Open Astro Technologies, USA.
# Modified by <PERSON><PERSON>, USA. <EMAIL>
# Downloaded from https://github.com/naturalstupid/PyJHora

# This file is part of the "PyJHora" Python library
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU Affero General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU Affero General Public License for more details.
#
# You should have received a copy of the GNU Affero General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.
import numpy as np
import const, utils

planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'lagnam']
raasi_list = ['Mesham', 'Rishabam', 'Mithunam', 'Katakam', 'Simmam', 'Kanni', 'Thulaam', 'Vrichigam', 'Dhanusu',
              'Makaram', 'Kumbam', 'Meenam']
raasi_index = lambda planet, planet_positions_in_chart: [i for i, raasi in enumerate(planet_positions_in_chart) if
                                                         planet != const._ascendant_symbol and planet.lower() in raasi.lower()][
    0]


def get_ashtaka_varga(house_to_planet_list):
    """
        get binna, samudhaya and prastara varga from the given horoscope chart
        @param house_to_planet_list: 1-D array [0..11] with planets in each raasi
            Example: ['','','','','2','7','1/5','0','3/4','L','','6/8']
        @return: 
            binna_ashtaka_varga - 2-D List [0..7][0..7] 0=Sun..7=Lagnam
            samudhaya ashtaka varga - 1D List [0..11] 0=Aries 11=Pisces
            prastara ashtaka varga - 3D List [0..7][0..7][0..11]
    """
    #print('get_ashtaka_varga','house_to_planet_list',house_to_planet_list)
    p_to_h = utils.get_planet_to_house_dict_from_chart(house_to_planet_list)
    #print('get_ashtaka_varga','p_to_h',p_to_h)
    raasi_ashtaka = [[0 for r in range(12)] for p in range(8)]
    prastara_ashtaka_varga = [[[0 for r in range(12)] for p1 in range(10)] for p2 in range(8)]
    for key in const.ashtaka_varga_dict.keys():
        p = int(key)
        #planet = planet_list[p]
        planet_raasi_list = const.ashtaka_varga_dict[key]
        for op, other_planet in enumerate(planet_raasi_list):
            pr = p_to_h[op]
            if op == 7:  #Lagnam
                pr = p_to_h[const._ascendant_symbol]
            for raasi in other_planet:
                r = (raasi - 1 + pr) % 12
                raasi_ashtaka[p][r] += 1
                prastara_ashtaka_varga[p][op][r] = 1
                prastara_ashtaka_varga[p][-1][r] += 1
    binna_ashtaka_varga = raasi_ashtaka[0:8][:]
    prastara_ashtaka_varga = prastara_ashtaka_varga[0:8][0:9][:]
    samudhaya_ashtaka_varga = np.asarray(binna_ashtaka_varga[:-1]).sum(axis=0).tolist()  # [0:-1] to exlcude Lagnam
    return binna_ashtaka_varga, samudhaya_ashtaka_varga, prastara_ashtaka_varga


def _trikona_sodhana(binna_ashtaka_varga):
    bav = binna_ashtaka_varga[:]
    for p in range(7):
        for r in range(4):
            if bav[p][r] == 0 or bav[p][r + 4] == 0 or bav[p][r + 8] == 0:
                #print('Rule 1:If atleast one rasi has zero, no reduction is necessary.',p,r,bav[p][r],bav[p][r+4],bav[p][r+8])
                continue
            elif bav[p][r] == bav[p][r + 4] and bav[p][r + 4] == bav[p][r + 8]:
                #print('Rule (2) If the three rasis have the same value, make them all zero.',p,r,bav[p][r],bav[p][r+4],bav[p][r+8])
                bav[p][r] = 0
                bav[p][r + 4] = 0
                bav[p][r + 8] = 0
            else:
                #print('Rule (3) Take the lowest value out of the three. Subtract it from all the values.',p,r,bav[p][r],bav[p][r+4],bav[p][r+8])
                min_value = min([bav[p][r], bav[p][r + 4], bav[p][r + 8]])
                #print('before',p,r,min_value,bav[p][r],bav[p][r+4],bav[p][r+8])
                bav[p][r] -= min_value
                bav[p][r + 4] -= min_value
                bav[p][r + 8] -= min_value
                #print('after',p,r,min_value,bav[p][r],bav[p][r+4],bav[p][r+8])
    return bav


def _ekadhipatya_sodhana(binna_ashtaka_varga_after_trikona, chart_1d):
    bav = binna_ashtaka_varga_after_trikona[:]
    rasi_owners = [4, 3, (0, 7), (2, 5), (8, 11), (1, 6), (9, 10)]
    for p in range(2, 7):
        r1, r2 = rasi_owners[p]
        r1_occupied = not (chart_1d[r1].strip() == '')
        r2_occupied = not (chart_1d[r2].strip() == '')
        # Rule 1 (either bav is 0. Rule 2: either rasi is occupied by a planet
        if (bav[p][r1] == 0 or bav[p][r2] == 0) or (r1_occupied and r2_occupied):
            continue
        elif (not r1_occupied) and (not r2_occupied):  # Rule 4 - both rasi are empty
            # Rule 4(b) (b) If they have different values, replace the higher value with the lower value.
            if bav[p][r1] != bav[p][r2]:
                min_value = min([bav[p][r1], bav[p][r2]])
                bav[p][r1] = min_value
                bav[p][r2] = min_value
            # Rule 4(a) If both the rasis have the same value, replace both with zero.
            else:
                bav[p][r1] = 0
                bav[p][r2] = 0
        else:
            # Rule (3) If one rasi is occupied by a planet (or planets) and the other is empty
            if (r1_occupied):  #r2 is empty
                # Rule 3(a) If the empty rasi has a lower value, replace the value with a zero.
                if (bav[p][r2] < bav[p][r1]):
                    bav[p][r2] = 0
                # Rule 3(b) If the empty rasi has a higher value, replace the value with the value in the other rasi.
                else:
                    bav[p][r2] = bav[p][r1]
            else:  #r1 is empty
                # Rule 3(a) If the empty rasi has a lower value, replace the value with a zero. 
                if (bav[p][r1] < bav[p][r2]):
                    bav[p][r1] = 0
                # Rule 3(b) If the empty rasi has a higher value, replace the value with the value in the other rasi.
                else:
                    bav[p][r1] = bav[p][r2]
    return bav


def _get_planet_positions(chart_1d):
    planet_houses = [-1 for p in range(7)]
    for p, planet in enumerate(planet_list[0:-1]):  # Excluding Lagnam
        for house, rasi in enumerate(chart_1d):
            if planet.lower() in rasi.lower():
                planet_houses[p] = house
                break
    return planet_houses


def _sodhya_pindas(binna_ashtaka_varga_after_ekadhipatya, chart_1d):
    rasimana_multipliers = [7, 10, 8, 4, 10, 6, 7, 8, 9, 5, 11, 12]
    grahamana_multipliers = [5, 5, 8, 5, 10, 7, 5]
    bav = binna_ashtaka_varga_after_ekadhipatya[:]
    raasi_pindas = [0 for p in range(7)]
    graha_pindas = [0 for p in range(7)]
    sodhya_pindas = [0 for p in range(7)]
    for p in range(7):
        raasi_pindas[p] = sum(np.multiply(bav[p][:], rasimana_multipliers))
    #planet_houses = _get_planet_positions(chart_1d)
    planet_houses = list(utils.get_planet_to_house_dict_from_chart(chart_1d).values())[
                    :7]  # Exclude Rahu, Ketu and Lagnam
    for p in range(7):
        graha_pindas[p] = sum([grahamana_multipliers[i] * bav[p][pr] for i, pr in enumerate(planet_houses)])
        sodhya_pindas[p] = raasi_pindas[p] + graha_pindas[p]
    return raasi_pindas, graha_pindas, sodhya_pindas


def sodhaya_pindas(binna_ashtaka_varga, house_to_planet_chart):
    """
        Get sodhaya pindas from binna ashtaka varga
        @param param:binna_ashtaka_varga - 2-D List [0..7][0..7] 0=Sun..7=Lagnam - of BAV values
        NOTE: To pass binn ashtaka varga as parameter - you need to get it from get_ashtaka_varga function
        @return: raasi_pindas,graha_pindas,sodhya_pindas
                raasi_pindas : raasi pindas of planets 0=Sun to 6=Saturn [0..6]
                graha_pindas : graha pindas of planets 0=Sun to 6=Saturn [0..6]
                sidhaya_pindas : sodhaya pindas of planets 0=Sun to 6=Saturn [0..6]
    """
    #print('bav before trikona',binna_ashtaka_varga)
    binna_ashtaka_varga_after_trikona = _trikona_sodhana(binna_ashtaka_varga)
    #print('bav after trikona',binna_ashtaka_varga_after_trikona)
    binna_ashtaka_varga_after_ekadhipatya = _ekadhipatya_sodhana(binna_ashtaka_varga_after_trikona,
                                                                 house_to_planet_chart)
    """ binna_ashtaka_varga_after_ekadhipatya is called Sodhita Ashtakavarga"""
    #print('Sodhita Ashtakavarga\n',binna_ashtaka_varga_after_ekadhipatya)
    raasi_pindas, graha_pindas, sodhya_pindas = _sodhya_pindas(binna_ashtaka_varga_after_ekadhipatya,
                                                               house_to_planet_chart)
    return raasi_pindas, graha_pindas, sodhya_pindas


if __name__ == "__main__":
    # from tests.pvr_tests import test_example

    # Chart 7 from the book
    chapter = 'Chaper 12.3 ashtaka_varga_tests'
    exercise = 'Exercise 22/Chart 7:'
    planet_positions_in_chart = ['6/1/7', '', '', '', '', '', '8/4', 'L', '3/2', '0', '5', '']
    print(raasi_index(planet_positions_in_chart))
    # bav, sav, pav = get_ashtaka_varga(chart_7)
    # bav_e = [[4, 2, 3, 4, 6, 5, 5, 3, 2, 6, 6, 2],
    #          [6, 3, 5, 3, 5, 5, 6, 3, 3, 4, 4, 2],
    #          [3, 2, 3, 4, 2, 5, 4, 3, 3, 4, 3, 3],
    #          [4, 6, 4, 3, 4, 7, 4, 5, 6, 3, 5, 3],
    #          [4, 4, 3, 5, 6, 5, 6, 4, 6, 4, 3, 6],
    #          [3, 5, 5, 4, 6, 2, 3, 6, 5, 2, 7, 4],
    #          [3, 2, 2, 3, 5, 6, 3, 4, 1, 3, 6, 1]]
    # test_example(chapter + exercise + ' BAV', bav_e, bav[:-1])  #,assert_result=True)
    # sav_e = [27, 24, 25, 26, 34, 35, 31, 28, 26, 26, 34, 21]
    # test_example(chapter + exercise + ' SAV', sav_e, sav)  #,assert_result=True)
    # sp_e_book = [[152, 85, 52, 95, 68, 154, 162], [81, 55, 43, 33, 56, 54, 63], [233, 140, 95, 128, 124, 208, 225]]
    # sp_e = ([155, 92, 55, 99, 93, 154, 166], [81, 55, 43, 33, 56, 54, 63], [236, 147, 98, 132, 149, 208, 229])
    # sp = sodhaya_pindas(bav, chart_7)
    # test_example(chapter + exercise + ' Sodhaya Pindas', sp_e, sp)
    # print(chapter + exercise + ' Sodhaya Pindas:\n NOTE: Not clear why this case SP failed to match the book\n' +
    #       ' Examples 40,41 & 42 based on Chart 12 are matching BAV, SAV and SP.\n So the calculations in this code is thus verified\n' +
    #       'Expected Values from Book:', sp_e_book)
