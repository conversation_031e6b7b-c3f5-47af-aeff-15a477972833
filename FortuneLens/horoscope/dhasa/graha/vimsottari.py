from collections import OrderedDict
from collections import OrderedDict as Dict
import const, utils
from horoscope.chart import charts
from panchanga import drik

# Constants and configurations
year_duration = const.sidereal_year
vimsottari_adhipati = lambda nak, seed_star=3: const.vimsottari_adhipati_list[
    (nak - seed_star + 3) % (len(const.vimsottari_adhipati_list))]
vimsottari_dict = const.vimsottari_dict
human_life_span_for_vimsottari_dhasa = const.human_life_span_for_vimsottari_dhasa


# Functions
def vimsottari_next_adhipati(lord, dir=1):
    """Returns next lord after `lord` in the adhipati_list"""
    current = const.vimsottari_adhipati_list.index(lord)
    next_index = (current + dir) % len(const.vimsottari_adhipati_list)
    return const.vimsottari_adhipati_list[next_index]


def vimsottari_dasha_start_date(jd, place, divisional_chart_factor=1, star_position_from_moon=1, seed_star=3,
                                dhasa_starting_planet=1):
    """Returns the start date of the mahadasa which occurred on or before `jd`"""
    y, m, d, fh = utils.jd_to_gregorian(jd)
    dob = drik.Date(y, m, d)
    tob = (fh, 0, 0)
    one_star = 360 / 27.0
    planet_positions = charts.divisional_chart(jd, place, divisional_chart_factor=divisional_chart_factor)

    if dhasa_starting_planet in range(9):
        planet_long = (planet_positions[dhasa_starting_planet + 1][1][0] * 30 +
                       planet_positions[dhasa_starting_planet + 1][1][1])
    else:
        planet_long = (planet_positions[2][1][0] * 30 +
                       planet_positions[2][1][1])

    if dhasa_starting_planet == 1:
        planet_long += (star_position_from_moon - 1) * one_star

    nak = int(planet_long / one_star)
    rem = (planet_long - nak * one_star)
    lord = vimsottari_adhipati(nak, seed_star)
    period = vimsottari_dict[lord]
    period_elapsed = rem / one_star * period * year_duration
    start_date = jd - period_elapsed
    return [lord, start_date]


def vimsottari_mahadasa(jd, place, divisional_chart_factor=1, star_position_from_moon=1, seed_star=3,
                        dhasa_starting_planet=1):
    """List all mahadashas and their start dates"""
    lord, start_date = vimsottari_dasha_start_date(jd, place, divisional_chart_factor=divisional_chart_factor,
                                                   star_position_from_moon=star_position_from_moon,
                                                   seed_star=seed_star,
                                                   dhasa_starting_planet=dhasa_starting_planet)
    retval = Dict()
    for _ in range(9):
        retval[lord] = start_date
        start_date += vimsottari_dict[lord] * year_duration
        lord = vimsottari_next_adhipati(lord)
    return retval


def _vimsottari_sub_periods(parent_lord, start_date, duration):
    """
    Calculate sub-periods (Bhukti, Antardasha, Sukshma, Prana) ensuring the first period
    starts with the same lord as the parent and handles wrapping around the lord list.
    """
    retval = OrderedDict()
    sub_duration_unit = duration / sum(vimsottari_dict.values())  # Scale the sub-period durations
    first_lord_encountered = False

    # Iterate over all the lords in the Vimsottari list, handling wrapping around
    for i in range(len(const.vimsottari_adhipati_list)):
        sub_lord = const.vimsottari_adhipati_list[
            (const.vimsottari_adhipati_list.index(parent_lord) + i) % len(const.vimsottari_adhipati_list)]

        # Check if we have encountered the parent lord to start the sub-periods
        if not first_lord_encountered and sub_lord == parent_lord:
            first_lord_encountered = True

        if not first_lord_encountered:
            continue  # Skip all lords before the parent lord

        # Calculate the sub-period duration based on the lord's proportion in the system
        sub_period = vimsottari_dict[sub_lord] * sub_duration_unit
        retval[sub_lord] = start_date  # Assign the start date for this sub-period
        start_date += sub_period  # Move the start date forward for the next sub-period

    return retval


#
# def _vimsottari_sub_periods(maha_lord, start_date, maha_duration):
#     # Calculate the Bhukti periods based on the Mahadasha lord and duration
#     total_duration = sum(vimsottari_dict.values())  # Total duration of the Vimsottari system (120 years)
#     bhukti_durations = {}
#
#     # Calculate the Bhukti periods
#     for bhukti_lord, bhukti_duration in vimsottari_dict.items():
#         # The duration of each Bhukti
#         bhukti_duration_scaled = (bhukti_duration * maha_duration) / total_duration
#         bhukti_start_date = start_date
#         bhukti_end_date = start_date + bhukti_duration_scaled
#         bhukti_durations[bhukti_lord] = bhukti_start_date, bhukti_end_date
#         start_date = bhukti_end_date  # Set the start date for the next Bhukti
#
#     return bhukti_durations


# planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']


def get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1, use_tribhagi_variation=False,
                                 include_antardhasa=True, include_sukshma_prana=True,
                                 divisional_chart_factor=1, seed_star=3):
    """
    Provides Vimsottari dhasa bhukthi for a given date in Julian day.
    Includes options for Antardhasa, Sukshma, and Prana.
    Returns all dasas in the format specified as strings.
    """
    planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']
    dashas = vimsottari_mahadasa(jd, place, divisional_chart_factor=divisional_chart_factor,
                                 star_position_from_moon=star_position_from_moon, seed_star=seed_star)

    maha_dasha = []
    bhukti_dasha = []
    antara_dasha = []
    sukshma_dasha = []
    prana_dasha = []

    total_120_years = list(dashas.values())[0] + (120 * year_duration)
    # print(dashas)
    for idx, (maha_lord, start_date) in enumerate(dashas.items()):
        maha_duration = vimsottari_dict[maha_lord] * year_duration
        maha_end_date = start_date + maha_duration
        # print(maha_duration)
        # Adjust the end date for the last Mahadasha
        if idx == len(dashas) - 1:
            maha_end_date = total_120_years

        y_start, m_start, d_start, h_start = utils.jd_to_gregorian(start_date)
        y_end, m_end, d_end, h_end = utils.jd_to_gregorian(maha_end_date)

        maha_dasha.append(f'({planet_list[maha_lord]}-{planet_list[maha_lord]}, '
                          f"{'%04d-%02d-%02d' % (y_start, m_start, d_start)} {utils.to_dms(h_start, as_string=True)}, "
                          f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")

        # print(f"Generating Bhuktis for Mahadasha: {planet_list[maha_lord]}")
        bhuktis = _vimsottari_sub_periods(maha_lord, start_date, maha_duration)
        # print(bhuktis)
        for bhukti_lord, bhukti_start in bhuktis.items():
            bhukti_duration = vimsottari_dict[bhukti_lord] * maha_duration / sum(vimsottari_dict.values())
            bhukti_end = bhukti_start + bhukti_duration

            y, m, d, h = utils.jd_to_gregorian(bhukti_start)
            y_end, m_end, d_end, h_end = utils.jd_to_gregorian(bhukti_end)

            bhukti_dasha.append(f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}, '
                                f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
                                f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")

            # print(f"Generated Bhukti: {planet_list[bhukti_lord]}")

            if include_antardhasa:
                # print(f"Generating Antardashas for Bhukti: {planet_list[bhukti_lord]}")
                antardashas = _vimsottari_sub_periods(bhukti_lord, bhukti_start, bhukti_duration)

                for antara_lord, antara_start in antardashas.items():
                    antara_duration = vimsottari_dict[antara_lord] * bhukti_duration / sum(vimsottari_dict.values())
                    antara_end = antara_start + antara_duration

                    y, m, d, h = utils.jd_to_gregorian(antara_start)
                    y_end, m_end, d_end, h_end = utils.jd_to_gregorian(antara_end)

                    antara_dasha.append(
                        f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}, '
                        f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
                        f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")

                    # print(f"Generated Antardasha: {planet_list[antara_lord]}")

                    if include_sukshma_prana:
                        # print(f"Generating Sukshmas for Antardasha: {planet_list[antara_lord]}")
                        sukshma_dashas = _vimsottari_sub_periods(antara_lord, antara_start, antara_duration)

                        for sukshma_lord, sukshma_start in sukshma_dashas.items():
                            sukshma_duration = vimsottari_dict[sukshma_lord] * antara_duration / sum(
                                vimsottari_dict.values())
                            sukshma_end = sukshma_start + sukshma_duration

                            y, m, d, h = utils.jd_to_gregorian(sukshma_start)
                            y_end, m_end, d_end, h_end = utils.jd_to_gregorian(sukshma_end)

                            sukshma_dasha.append(
                                f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}-{planet_list[sukshma_lord]}, '
                                f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
                                f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")

                            # print(f"Generated Sukshma: {planet_list[sukshma_lord]}")

                            prana_dashas = _vimsottari_sub_periods(sukshma_lord, sukshma_start, sukshma_duration)

                            # print(f"Generating Pranas for Sukshma: {planet_list[sukshma_lord]}")

                            for prana_lord, prana_start in prana_dashas.items():
                                prana_duration = vimsottari_dict[prana_lord] * sukshma_duration / sum(
                                    vimsottari_dict.values())
                                prana_end = prana_start + prana_duration

                                y, m, d, h = utils.jd_to_gregorian(prana_start)
                                y_end, m_end, d_end, h_end = utils.jd_to_gregorian(prana_end)

                                prana_dasha.append(
                                    f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}-{planet_list[sukshma_lord]}-{planet_list[prana_lord]}, '
                                    f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
                                    f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")

                                # print(f"Generated Prana: {planet_list[prana_lord]}")

    # Print the length of each Dasha level
    print(f"Mahadasha records: {len(maha_dasha)}")
    print(f"Bhukti records: {len(bhukti_dasha)}")
    print(f"Antardasha records: {len(antara_dasha)}")
    print(f"Sukshma records: {len(sukshma_dasha)}")
    # for i in sukshma_dasha:
    #     print(i)
    print(f"Prana records: {len(prana_dasha)}")

    # Return all dasha periods
    return maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha


#from horoscope.dhasa.graha import vimsottari

vimsottari_balance = get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1,
                                                  divisional_chart_factor=1)

maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha = vimsottari_balance
#
# def get_vimsottari_dhasa_bhukthi(jd, place, star_position_from_moon=1, use_tribhagi_variation=False,
#                                  include_antardhasa=True, include_sukshma_prana=True,
#                                  divisional_chart_factor=1, seed_star=3):
#     """
#     Provides Vimsottari dhasa bhukthi for a given date in Julian day.
#     Includes options for Antardhasa, Sukshma, and Prana.
#     Returns all dasas in the format specified as strings.
#     """
#     planet_list = ['sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']
#     dashas = vimsottari_mahadasa(jd, place, divisional_chart_factor=divisional_chart_factor,
#                                  star_position_from_moon=star_position_from_moon, seed_star=seed_star)
#
#     maha_dasha = []
#     bhukti_dasha = []
#     antara_dasha = []
#     sukshma_dasha = []
#     prana_dasha = []
#
#     total_120_years = list(dashas.values())[0] + (120 * year_duration)
#
#     for idx, (maha_lord, start_date) in enumerate(dashas.items()):
#         maha_duration = vimsottari_dict[maha_lord] * year_duration
#         maha_end_date = start_date + maha_duration
#
#         # Adjust the end date for the last Mahadasha
#         if idx == len(dashas) - 1:
#             maha_end_date = total_120_years
#
#         y_start, m_start, d_start, h_start = utils.jd_to_gregorian(start_date)
#         y_end, m_end, d_end, h_end = utils.jd_to_gregorian(maha_end_date)
#
#         maha_dasha.append(f'({planet_list[maha_lord]}-{planet_list[maha_lord]}, '
#                           f"{'%04d-%02d-%02d' % (y_start, m_start, d_start)} {utils.to_dms(h_start, as_string=True)}, "
#                           f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")
#
#         bhuktis = _vimsottari_sub_periods(maha_lord, start_date, maha_duration)
#
#         print(f"Generating Bhuktis for Mahadasha: {planet_list[maha_lord]}")
#
#         for bhukti_lord, bhukti_start in bhuktis.items():
#             bhukti_duration = vimsottari_dict[bhukti_lord] * maha_duration / sum(vimsottari_dict.values())
#             bhukti_end = bhukti_start + bhukti_duration
#
#             y, m, d, h = utils.jd_to_gregorian(bhukti_start)
#             y_end, m_end, d_end, h_end = utils.jd_to_gregorian(bhukti_end)
#
#             bhukti_dasha.append(f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}, '
#                                 f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
#                                 f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")
#
#             print(f"Generated Bhukti: {planet_list[bhukti_lord]}")
#
#             if include_antardhasa:
#                 antardashas = _vimsottari_sub_periods(bhukti_lord, bhukti_start, bhukti_duration)
#
#                 print(f"Generating Antardashas for Bhukti: {planet_list[bhukti_lord]}")
#
#                 for antara_lord, antara_start in antardashas.items():
#                     antara_duration = vimsottari_dict[antara_lord] * bhukti_duration / sum(vimsottari_dict.values())
#                     antara_end = antara_start + antara_duration
#
#                     y, m, d, h = utils.jd_to_gregorian(antara_start)
#                     y_end, m_end, d_end, h_end = utils.jd_to_gregorian(antara_end)
#
#                     antara_dasha.append(
#                         f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}, '
#                         f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
#                         f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")
#
#                     print(f"Generated Antardasha: {planet_list[antara_lord]}")
#
#                     if include_sukshma_prana:
#                         sukshma_dashas = _vimsottari_sub_periods(antara_lord, antara_start, antara_duration)
#
#                         print(f"Generating Sukshmas for Antardasha: {planet_list[antara_lord]}")
#
#                         for sukshma_lord, sukshma_start in sukshma_dashas.items():
#                             sukshma_duration = vimsottari_dict[sukshma_lord] * antara_duration / sum(
#                                 vimsottari_dict.values())
#                             sukshma_end = sukshma_start + sukshma_duration
#
#                             y, m, d, h = utils.jd_to_gregorian(sukshma_start)
#                             y_end, m_end, d_end, h_end = utils.jd_to_gregorian(sukshma_end)
#
#                             sukshma_dasha.append(
#                                 f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}-{planet_list[sukshma_lord]}, '
#                                 f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
#                                 f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")
#
#                             print(f"Generated Sukshma: {planet_list[sukshma_lord]}")
#
#                             prana_dashas = _vimsottari_sub_periods(sukshma_lord, sukshma_start, sukshma_duration)
#
#                             print(f"Generating Pranas for Sukshma: {planet_list[sukshma_lord]}")
#
#                             for prana_lord, prana_start in prana_dashas.items():
#                                 prana_duration = vimsottari_dict[prana_lord] * sukshma_duration / sum(
#                                     vimsottari_dict.values())
#                                 prana_end = prana_start + prana_duration
#
#                                 y, m, d, h = utils.jd_to_gregorian(prana_start)
#                                 y_end, m_end, d_end, h_end = utils.jd_to_gregorian(prana_end)
#
#                                 prana_dasha.append(
#                                     f'({planet_list[maha_lord]}-{planet_list[bhukti_lord]}-{planet_list[antara_lord]}-{planet_list[sukshma_lord]}-{planet_list[prana_lord]}, '
#                                     f"{'%04d-%02d-%02d' % (y, m, d)} {utils.to_dms(h, as_string=True)}, "
#                                     f"{'%04d-%02d-%02d' % (y_end, m_end, d_end)} {utils.to_dms(h_end, as_string=True)})")
#
#                                 print(f"Generated Prana: {planet_list[prana_lord]}")
#
#     # Check lengths of all dasha levels
#     print(f"Mahadasha records: {len(maha_dasha)}")
#     print(f"Bhukti records: {len(bhukti_dasha)}")
#     print(f"Antardasha records: {len(antara_dasha)}")
#     print(f"Sukshma records: {len(sukshma_dasha)}")
#     print(f"Prana records: {len(prana_dasha)}")
#
#     # Return all dasha periods
#     return maha_dasha, bhukti_dasha, antara_dasha, sukshma_dasha, prana_dasha
