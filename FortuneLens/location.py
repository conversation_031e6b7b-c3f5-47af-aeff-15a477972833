import requests
from pytz import timezone, utc
from timezonefinder import TimezoneFinder
import json

from geopy.geocoders import Nominatim
from timezonefinder import TimezoneFinder
from datetime import datetime
import pytz


def get_location_info(location_name):
    # print(location_name+",TamilNadu,India")
    geolocator = Nominatim(user_agent="my_custom_user_agent")  # Set a custom user-agent
    location = geolocator.geocode(location_name)

    if location:
        latitude = round(location.latitude, 4)
        longitude = round(location.longitude, 4)

        tf = TimezoneFinder()
        timezone_str = tf.timezone_at(lng=longitude, lat=latitude)

        if timezone_str:
            timezone = pytz.timezone(timezone_str)
            current_time = datetime.now(timezone).strftime('%Y-%m-%d %H:%M:%S')
            utc_offset = timezone.utcoffset(datetime.now()).total_seconds() / 3600
        else:
            timezone_str = 'Unknown'
            current_time = 'Unknown'
            utc_offset = 'Unknown'

        return {
                    'Location_name': location_name,
                    'latitude': latitude,
                    'longitude': longitude,
                    'timezone': timezone_str,
                    'Time_zone_offset': utc_offset,
                    'current_time': current_time
                }
    else:
        return None


# Example usage
# location_name = "Mumbai, Maharashtra, India"
# location_info = get_location_info(location_name)
#
# if location_info:
#     print(f"Latitude: {location_info['latitude']}")
#     print(f"Longitude: {location_info['longitude']}")
#     print(f"Timezone: {location_info['timezone']}")
#     print(f"UTC Offset: {location_info['utc_offset']} hours")
#     print(f"Current Time: {location_info['current_time']}")
# else:
#     print("Location not found.")
#
# import datetime
#
# # google_maps_url = "https://www.google.cl/maps/place/"
#
# from geopy.geocoders import Nominatim
# from timezonefinder import TimezoneFinder
# from datetime import datetime
# import pytz
#
#
# def get_location_info(location_name):
#     geolocator = Nominatim(user_agent="geoapiExercises")
#     location = geolocator.geocode(location_name)
#
#     if location:
#         latitude = round(location.latitude, 4)
#         longitude = round(location.longitude, 4)
#
#         tf = TimezoneFinder()
#         timezone_str = tf.timezone_at(lng=longitude, lat=latitude)
#
#         if timezone_str:
#             timezone = pytz.timezone(timezone_str)
#             current_time = datetime.now(timezone).strftime('%Y-%m-%d %H:%M:%S')
#             utc_offset = timezone.utcoffset(datetime.now()).total_seconds() / 3600
#         else:
#             timezone_str = 'Unknown'
#             current_time = 'Unknown'
#             utc_offset = 'Unknown'
#
#         return {
#             'Location_name': location_name,
#             'latitude': latitude,
#             'longitude': longitude,
#             'timezone': timezone_str,
#             'Time_zone_offset': utc_offset,
#             'current_time': current_time
#         }
#     else:
#         return None
# def get_place_timezone_offset(latitude, longitude):
#     """
#     This function returns a location's time zone offset from UTC in hours using latitude and longitude.
#     """
#     try:
#         tf = TimezoneFinder()
#         today = datetime.datetime.now()
#         tz_target = timezone(tf.timezone_at(lng=longitude, lat=latitude))
#         if tz_target is None:
#             raise ValueError("Timezone not found for the given location.")
#         today_target = tz_target.localize(today)
#         tz_offset = today_target.utcoffset().total_seconds() / 3600.0  # in hours
#         return tz_offset
#     except Exception as err:
#         print('Error in get_place_timezone_offset', err)
#         print('WARNING: Time Zone returned as default +5.0. Need to change it')
#         return 5.0
#
#
# def scrap_google_map_for_latlongtz_from_city_with_state_country(location):
#     """
#     This function scrapes Google Maps to get latitude, longitude, and time zone offset of a city/state/country.
#     """
#     url = google_maps_url + location
#     resp = requests.get(url)
#     txt = resp.text
#     find1 = "window.APP_INITIALIZATION_STATE="
#     find2 = ";window.APP"
#     i1 = txt.find(find1)
#     i2 = txt.find(find2, i1 + 1)
#     js = txt[i1 + len(find1):i2]
#     # print(js)
#     data = json.loads(js)[0][0][1:3]
#     latitude = data[1]
#     longitude = data[0]
#     timezone_offset = get_place_timezone_offset(latitude, longitude)
#     loc = {
#         'Location': location,
#         'Latitude': latitude,
#         'Longitude': longitude,
#         'Time_zone_offset': timezone_offset
#     }
#     return loc
#
#
# # Example usage
# location = "Cuddalore"
# location_info = scrap_google_map_for_latlongtz_from_city_with_state_country(location)
# print(location_info)
