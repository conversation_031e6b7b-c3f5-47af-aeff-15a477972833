from data_loading import *
# from medical_profession import *
from marriage_main_functions import *
from bulit_in_function import *

dataframes = read_excel_sheets(fileName)
import pandas as pd


def filter_by_user_gender(dataframe, user_id_1, user_id_2):
    """
    Retrieves and compares the genders of two users specified by their user IDs.

    Parameters:
        dataframe (pd.DataFrame): The dataframe containing user data. It should include columns 'user_id' and 'user_gender'.
        user_id_1 (int or str): The ID of the first user.
        user_id_2 (int or str): The ID of the second user.

    Returns:
        list or None: A list containing user IDs if genders match the specified criteria, otherwise None.
    """
    user_data_1 = dataframe[dataframe['user_id'] == user_id_1]
    user_data_2 = dataframe[dataframe['user_id'] == user_id_2]

    if user_data_1.empty or user_data_2.empty:
        print("One or both users are not found in the dataframe.")
        return None

    gender_1 = user_data_1['user_gender'].iloc[0]
    gender_2 = user_data_2['user_gender'].iloc[0]

    if gender_1 == "Female" and gender_2 == "Male":
        return [user_id_1, user_id_2]
    elif gender_1 == "Male" and gender_2 == "Female":
        return [user_id_2, user_id_1]
    else:
        print(f"Genders do not match the specified criteria.")
        return None


def dina_porutham(bride_nakshatra, groom_nakshatra, nakshatras):
    print(bride_star, groom_star)
    # Get indices of Nakshatras
    bride_index = nakshatras.index(bride_nakshatra) + 1
    groom_index = nakshatras.index(groom_nakshatra) + 1
    # print(bride_index, groom_index)
    # Calculate the distance
    distance = ((groom_index - bride_index) % 27) + 1
    print(distance)
    if distance == 0:
        distance = 27  # To handle cyclic case

    # Check Dina Porutham rules
    if distance % 9 == 0:
        return "No Match"
    elif distance % 2 == 0:
        return "Excellent Match"
    else:
        return "No Match"


def gana_porutham(bride_nakshatra, groom_nakshatra):
    # Gana classifications using the provided list
    deva_gana = ["ASHWINI", "MIRIGASIRISHAM", "PUNARPOOSAM", "POOSAM", "HASTHAM", "SWATHI", "ANUSHAM", "THIRUVONAM",
                 "REVATHI"]
    manushya_gana = ["BARANI", "ROHINI", "THIRUVADIRAI", "POORAM", "UTHIRAM", "CHITHIRAI", "VISAGAM", "POORADAM",
                     "UTHIRADAM"]
    rakshasa_gana = ["KARTHIKAI", "AYILYAM", "MAGAM", "POORATADHI", "UTHIRATTADHI", "MOOLAM", "AVITTAM", "SADAYAM",
                     "KETTAI"]

    # Convert inputs to uppercase for matching
    bride_nakshatra = bride_nakshatra.upper()
    groom_nakshatra = groom_nakshatra.upper()

    # Determine the Gana of the bride and groom
    if bride_nakshatra in deva_gana:
        bride_gana = "Deva"
    elif bride_nakshatra in manushya_gana:
        bride_gana = "Manushya"
    elif bride_nakshatra in rakshasa_gana:
        bride_gana = "Rakshasa"
    else:
        return "Invalid Bride Nakshatra"

    if groom_nakshatra in deva_gana:
        groom_gana = "Deva"
    elif groom_nakshatra in manushya_gana:
        groom_gana = "Manushya"
    elif groom_nakshatra in rakshasa_gana:
        groom_gana = "Rakshasa"
    else:
        return "Invalid Groom Nakshatra"

    if bride_gana == "Rakshasa" and groom_gana == "Rakshasa":
        return "Partial Match"
    # Gana Porutham Rules
    elif bride_gana == groom_gana:
        return "Excellent Match"
    elif (bride_gana == "Deva" and groom_gana == "Manushya") or (bride_gana == "Manushya" and groom_gana == "Deva"):
        return "Excellent Match"
    else:
        return "No Match"


def mahendra_porutham(bride_nakshatra, groom_nakshatra, nakshatras):
    # Positions considered favorable for Mahendra Porutham
    favorable_positions = [4, 7, 10, 13, 16, 19, 22, 25]

    # Find the index of bride's and groom's Nakshatras
    if bride_nakshatra in nakshatras and groom_nakshatra in nakshatras:
        bride_index = nakshatras.index(bride_nakshatra) + 1  # 1-based index
        groom_index = nakshatras.index(groom_nakshatra) + 1  # 1-based index
    else:
        return "Invalid Nakshatra Input"
    # Calculate the relative position of bride's Nakshatra from groom's Nakshatra
    relative_position = (abs(bride_index - groom_index) % 27) + 1
    if relative_position == 0:  # Handle case where both Nakshatras are the same
        relative_position = 27

    # Check if the relative position is favorable
    if relative_position in favorable_positions:
        return "Excellent Match"
    else:
        return "No Match"


def nadi_porutham(bride_nakshatra, groom_nakshatra):
    # Updated Nadi classification based on the provided list of Nakshatras
    nadi_groups = {
        "Aadi Nadi": ['ASHWINI', 'BARANI', 'KARTHIKAI', 'ROHINI', 'MIRIGASIRISHAM', 'THIRUVADIRAI', 'PUNARPOOSAM',
                      'POOSAM', 'AYILYAM', 'MAGAM', 'POORAM', 'UTHIRAM', 'HASTHAM', 'CHITHIRAI', 'SWATHI', 'VISAGAM',
                      'ANUSHAM', 'KETTAI', 'MOOLAM', 'POORADAM', 'UTHIRADAM', 'THIRUVONAM', 'AVITTAM', 'SADAYAM'],
        "Madhya Nadi": ['AVITTAM', 'SADAYAM', 'POORATADHI', 'UTHIRATTADHI', 'REVATHI'],
        "Antya Nadi": ['UTHIRAM', 'HASTHAM', 'CHITHIRAI', 'SWATHI', 'VISAGAM', 'ANUSHAM', 'KETTAI', 'MOOLAM',
                       'POORADAM', 'UTHIRADAM', 'THIRUVONAM']
    }

    # Find the Nadi of bride and groom
    bride_nadi = None
    groom_nadi = None

    for nadi, stars in nadi_groups.items():
        if bride_nakshatra in stars:
            bride_nadi = nadi
        if groom_nakshatra in stars:
            groom_nadi = nadi

    if not bride_nadi or not groom_nadi:
        return "Invalid Nakshatra Input"

    # Compare the Nadis
    if bride_nadi == groom_nadi:
        return "No Match"
    else:
        return "Excellent Match"


def rasi_porutham(bride_rasi, groom_rasi):
    rasi_data = {
        "MESHAM": {
            "FRIENDLY": ["MIDUNAM", "SIMMAM", "DHANUSU", "KUMBAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "THULAM", "MAGARAM", "MEENAM"]
        },
        "RISHABAM": {
            "FRIENDLY": ["KADAGAM", "KANNI", "MAGARAM", "MEENAM"],
            "ENEMY": ["DHANUSU"],
            "NEUTRAL": ["MIDUNAM", "SIMMAM", "THULAM", "VRICHIGAM", "KUMBAM", "MESHAM"]
        },
        "MIDUNAM": {
            "FRIENDLY": ["MESHAM", "SIMMAM", "THULAM", "KUMBAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "THULAM", "MAGARAM", "MEENAM"]
        },
        "KADAGAM": {
            "FRIENDLY": ["RISHABAM", "KANNI", "VRICHIGAM", "MEENAM"],
            "ENEMY": ["KUMBAM"],
            "NEUTRAL": ["SIMMAM", "THULAM", "DHANUSU", "MAGARAM", "MESHAM", "MIDUNAM"]
        },
        "SIMMAM": {
            "FRIENDLY": ["MESHAM", "MIDUNAM", "THULAM", "DHANUSU"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "MAGARAM", "MEENAM"]
        },
        "KANNI": {
            "FRIENDLY": ["RISHABAM", "KADAGAM", "MAGARAM", "MEENAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["MIDUNAM", "SIMMAM", "THULAM", "KUMBAM", "MESHAM"]
        },
        "THULAM": {
            "FRIENDLY": ["MIDUNAM", "SIMMAM", "DHANUSU", "KUMBAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "MAGARAM", "MEENAM"]
        },
        "VIRICHIGAM": {
            "FRIENDLY": ["KADAGAM", "KANNI", "MAGARAM", "MEENAM"],
            "ENEMY": ["MESHAM", "MIDUNAM", "SIMMAM", "THULAM", "KUMBAM"],
            "NEUTRAL": ["DHANUSU"]
        },
        "DHANUSU": {
            "FRIENDLY": ["MESHAM", "SIMMAM", "THULAM", "KUMBAM"],
            "ENEMY": ["KADAGAM"],
            "NEUTRAL": ["MAGARAM", "MEENAM", "RISHABAM", "MIDUNAM", "KANNI", "VRICHIGAM"]
        },
        "MAGARAM": {
            "FRIENDLY": ["RISHABAM", "KANNI", "VRICHIGAM", "MEENAM"],
            "ENEMY": ["KUMBAM"],
            "NEUTRAL": ["SIMMAM", "MIDUNAM", "THULAM", "DHANUSU", "MESHAM", "KADAGAM"]
        },
        "KUMBAM": {
            "FRIENDLY": ["MESHAM", "MIDUNAM", "SIMMAM", "THULAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["RISHABAM", "KADAGAM", "KANNI", "MAGARAM", "MEENAM"]
        },
        "MEENAM": {
            "FRIENDLY": ["RISHABAM", "KADAGAM", "MAGARAM", "MEENAM"],
            "ENEMY": ["VRICHIGAM"],
            "NEUTRAL": ["MIDUNAM", "SIMMAM", "THULAM", "KUMBAM", "MESHAM"]
        }
    }

    if bride_rasi not in rasi_data:
        return "Invalid Bride Rasi"

    if groom_rasi in rasi_data[bride_rasi]["FRIENDLY"]:
        return "Excellent Match"
    elif groom_rasi in rasi_data[bride_rasi]["NEUTRAL"]:
        return "Partial Match"
    elif groom_rasi in rasi_data[bride_rasi]["ENEMY"]:
        return "No Match"
    else:
        return "Invalid Groom Rasi"


def rasyadhipathi_porutham(bride_rasi, groom_rasi, rasi_rulers_df, planet_relationships_df):
    bride_ruler = rasi_rulers_df.loc[rasi_rulers_df['House Name'] == bride_rasi, 'Ruling Planets'].values
    groom_ruler = rasi_rulers_df.loc[rasi_rulers_df['House Name'] == groom_rasi, 'Ruling Planets'].values
    # Validate if the ruling planets exist for both bride and groom
    if len(bride_ruler) == 0 or len(groom_ruler) == 0:
        return "Invalid Rasi input."

    bride_ruler = bride_ruler[0]  # Assuming single match
    groom_ruler = groom_ruler[0]  # Assuming single match

    # Convert planet relationships to a dictionary for easy lookup
    planet_relationships = planet_relationships_df.set_index('Planet').to_dict('index')

    # Check the relationship between the ruling planets
    if groom_ruler in planet_relationships[bride_ruler]["Friends"]:
        return "Excellent Match"
    elif groom_ruler in planet_relationships[bride_ruler]["Enemies"]:
        return "No Match"
    else:
        return "Partial Match"  # Neutral relationship means Match is acceptable


def vasya_porutham(bride_rasi, groom_rasi):
    # Updated Vasya relationships
    vasya_relationships = {
        "MESHAM": ["SIMMAM", "RISHABAM"],
        "RISHABAM": ["MESHAM", "KADAGAM"],
        "MIDUNAM": ["KANNI"],
        "KADAGAM": ["RISHABAM", "MEENAM"],
        "SIMMAM": ["MESHAM", "THULAM"],
        "KANNI": ["MIDUNAM", "DHANUSU"],
        "THULAM": ["SIMMAM", "VRICHIGAM"],
        "VRICHIGAM": ["THULAM", "KUMBAM"],
        "DHANUSU": ["MEENAM", "KANNI"],
        "MAGARAM": ["KUMBAM", "MEENAM"],
        "KUMBAM": ["VRICHIGAM", "MAGARAM"],
        "MEENAM": ["KADAGAM", "DHANUSU"]
    }

    # Check compatibility
    if groom_rasi in vasya_relationships[bride_rasi] or bride_rasi in vasya_relationships[groom_rasi]:
        return "Excellent Match"
    else:
        return "No Match"


def yoni_porutham_with_points(bride_star, groom_star):
    yoni_data_with_enemies = {
        "ASHWINI": {"Animal": "Horse", "Gender": "Male", "Enemies": ["Buffalo"]},
        "BARANI": {"Animal": "Elephant", "Gender": "Male", "Enemies": ["Lion"]},
        "KARTHIKAI": {"Animal": "Goat", "Gender": "Female", "Enemies": ["Monkey"]},
        "ROHINI": {"Animal": "Snake", "Gender": "Male", "Enemies": ["Rat", "Mongoose"]},
        "MIRIGASIRISHAM": {"Animal": "Snake", "Gender": "Female", "Enemies": ["Rat", "Mongoose"]},
        "THIRUVADIRAI": {"Animal": "Dog", "Gender": "Male", "Enemies": ["Deer"]},
        "PUNARPOOSAM": {"Animal": "Cat", "Gender": "Female", "Enemies": ["Rat"]},
        "POOSAM": {"Animal": "Goat", "Gender": "Male", "Enemies": ["Monkey"]},
        "AYILYAM": {"Animal": "Cat", "Gender": "Male", "Enemies": ["Rat"]},
        "MAGAM": {"Animal": "Rat", "Gender": "Male", "Enemies": ["Snake", "Cat"]},
        "POORAM": {"Animal": "Rat", "Gender": "Female", "Enemies": ["Snake", "Cat"]},
        "UTHIRAM": {"Animal": "Bull", "Gender": "Male", "Enemies": ["Tiger"]},
        "HASTHAM": {"Animal": "Buffalo", "Gender": "Female", "Enemies": ["Horse"]},
        "CHITHIRAI": {"Animal": "Tiger", "Gender": "Male", "Enemies": ["Cow"]},
        "SWATHI": {"Animal": "Buffalo", "Gender": "Male", "Enemies": ["Horse"]},
        "VISAGAM": {"Animal": "Tiger", "Gender": "Female", "Enemies": ["Cow"]},
        "ANUSHAM": {"Animal": "Deer", "Gender": "Female", "Enemies": ["Dog"]},
        "KETTAI": {"Animal": "Deer", "Gender": "Male", "Enemies": ["Dog"]},
        "MOOLAM": {"Animal": "Dog", "Gender": "Female", "Enemies": ["Deer"]},
        "POORADAM": {"Animal": "Monkey", "Gender": "Male", "Enemies": ["Goat"]},
        "UTHIRADAM": {"Animal": "Mongoose", "Gender": "Male", "Enemies": ["Snake"]},
        "THIRUVONAM": {"Animal": "Monkey", "Gender": "Female", "Enemies": ["Goat"]},
        "AVITTAM": {"Animal": "Lion", "Gender": "Female", "Enemies": ["Elephant"]},
        "SADAYAM": {"Animal": "Horse", "Gender": "Female", "Enemies": ["Buffalo"]},
        "POORATADHI": {"Animal": "Lion", "Gender": "Male", "Enemies": ["Elephant"]},
        "UTHIRATTADHI": {"Animal": "Cow", "Gender": "Female", "Enemies": ["Tiger"]},
        "REVATHI": {"Animal": "Elephant", "Gender": "Female", "Enemies": ["Lion"]},
    }

    bride = yoni_data_with_enemies[bride_star]
    groom = yoni_data_with_enemies[groom_star]
    # print(bride, groom)
    if groom["Animal"] in bride["Enemies"] or bride["Animal"] in groom["Enemies"]:
        return "No Match"

    if bride["Animal"] == groom["Animal"]:
        if bride["Gender"] == "Female" and groom["Gender"] == "Male":
            return "Excellent Match"
    elif groom["Animal"] != bride["Animal"]:
        if bride["Gender"] == "Female" and groom["Gender"] == "Male":
            return "Good Match"
        if groom["Gender"] == "Male" and bride["Gender"] == "Male" or groom["Gender"] == "Female" and bride[
            "Gender"] == "Female":
            return "Partial Match"
        elif groom["Gender"] == "Female" and bride["Gender"] == "Male":
            return "Poor Match"
        else:
            return "No Match"


def vedhai_mismatch(bride_star, groom_star):
    # Vedhai mismatch stars mapping
    vedhai_mismatch_map = {
        "Ashwini": ["Kettai"],
        "Barani": ["Anusham"],
        "Karthikai": ["Visagam"],
        "Rohini": ["Swathi"],
        "Mirigasirisham": ["Chithirai", "Avittam"],
        "Thiruvadirai": ["Thiruvonam"],
        "Punarpoosam": ["Uthiradam"],
        "Poosam": ["Pooradam"],
        "Ayilyam": ["Moolam"],
        "Magam": ["Revathi"],
        "Pooram": ["Uthirattadhi"],
        "Uthiram": ["Pooratadhi"],
        "Hastham": ["Sadayam"],
        "Chithirai": ["Avittam", "Mirigasirisham"],
        "Swathi": ["Rohini"],
        "Visagam": ["Karthikai"],
        "Anusham": ["Barani"],
        "Kettai": ["Ashwini"],
        "Moolam": ["Ayilyam"],
        "Pooradam": ["Poosam"],
        "Uthiradam": ["Punarpoosam"],
        "Thiruvonam": ["Thiruvadirai"],
        "Avittam": ["Chithirai", "Mirigasirisham"],
        "Sadayam": ["Hastham"],
        "Pooratadhi": ["Uthiram"],
        "Uthirattadhi": ["Pooram"],
        "Revathi": ["Magam"]
    }

    # Check if the groom's star is in the bride's mismatch list
    if groom_star in vedhai_mismatch_map.get(bride_star, []):
        return "No Match"
    else:
        return "Excellent Match"


rajju_mapping = {
    "Head": {"None": ["Mrigasirisham", "Chithirai", "Avittam"]},
    "Neck": {
        "Rising": ["Rohini", "Hastham", "Thiruvonam"],
        "Descending": ["Thiruvadirai", "Swathi", "Sadayam"],
    },
    "Stomach": {
        "Rising": ["Karthikai", "Uthiram", "Uthiradam"],
        "Descending": ["Punarpoosam", "Visagam", "Pooratadhi"],
    },
    "Thigh": {
        "Rising": ["Barani", "Pooram", "Pooradam"],
        "Descending": ["Poosam", "Anusham", "Uthirattadhi"],
    },
    "Leg": {
        "Rising": ["Ashwini", "Magam", "Moolam"],
        "Descending": ["Ayilyam", "Kettai", "Revathi"],
    },
}
rasi_mapping = {
    "MESHAM": 1,  # Mesha
    "VRISHABHAM": 2,  # Vrishabha
    "MITHUNAM": 3,  # Mithuna
    "KARKATAKAM": 4,  # Karka
    "SIMMAM": 5,  # Simha
    "KANNI": 6,  # Kanya
    "THULAM": 7,  # Thula
    "VIRUCHIKAM": 8,  # Vrichika
    "DHANUSU": 9,  # Dhanus
    "MAKARAM": 10,  # Makara
    "KUMBHAM": 11,  # Kumbha
    "MEENAM": 12,  # Meena
}


def find_rajju_and_type(star):
    # Iterate through the rajju_mapping dictionary with uppercase
    for rajju_part, rajju_data in rajju_mapping.items():
        rajju_part_upper = rajju_part.upper()  # Convert rajju_part to uppercase
        for star_type, stars in rajju_data.items():
            star_type_upper = star_type.upper()  # Convert star_type to uppercase
            stars_upper = [star.upper() for star in stars]  # Convert each star to uppercase
            if star.upper() in stars_upper:
                return rajju_part_upper, star_type_upper
    return None, None


def rajju_porutham_with_mapping(
        bride_star, groom_star, bride_rasi, groom_rasi, bride_rasi_lord, groom_rasi_lord,
        bride_star_lord, groom_star_lord, planet_friendship_df):
    # Convert rasi names to numeric values using rasi_mapping
    global result
    # print(bride_rasi, groom_rasi)
    bride_rasi_numeric = rasi_mapping.get(bride_rasi, None)
    groom_rasi_numeric = rasi_mapping.get(groom_rasi, None)
    # print(bride_rasi_numeric, groom_rasi_numeric, (abs(bride_rasi_numeric - groom_rasi_numeric)+1) % 12)
    # Handle invalid rasi input
    if bride_rasi_numeric is None or groom_rasi_numeric is None:
        return "Invalid Rasi input."
    # print(bride_star, groom_star)
    # print(bride_rasi_numeric, groom_rasi_numeric,  18 % 12)
    # Determine Rajju and Rising/Descending type for bride and groom
    bride_rajju, bride_type = find_rajju_and_type(bride_star)
    groom_rajju, groom_type = find_rajju_and_type(groom_star)
    # print(bride_rajju, bride_type)
    # print(groom_rajju, groom_type)
    # Handle invalid stars
    if bride_rajju is None or groom_rajju is None:
        return "Invalid Nakshatra input."

    # Check for same Rajju
    if bride_rajju == groom_rajju:
        if bride_rajju == "Head":
            dec, result = "GROOM will have Short Life", "No Match"
        elif bride_rajju == "Neck":
            dec, result = "BRIDE will have Short Life", "No Match"
        elif bride_rajju == "Stomach":
            dec, result = "Difficulty in having Child", "Poor Match"
        elif bride_rajju == "Thigh":
            dec, result = "Affects Economic Prosperity", "Poor Match"
        elif bride_rajju == "Leg":
            dec, result = "Husband and Wife will live separately", "Poor Match"
        print(bride_rasi_numeric, groom_rasi_numeric, bride_rasi_numeric - groom_rasi_numeric)
        # Apply exceptions for same Rajju
        if bride_rasi_numeric == groom_rasi_numeric:
            return "Partial Match"
        elif bride_rasi_lord == groom_rasi_lord:
            return "Partial Match"

        elif (abs(bride_rasi_numeric - groom_rasi_numeric)+1) % 12 == 7:
            return "Partial Match"

        # Check if the Star Lords are friends using the planet_friendship_df DataFrame
        star_lord_row = planet_friendship_df.loc[planet_friendship_df["Planet"] == bride_star_lord]
        if not star_lord_row.empty:
            friends = star_lord_row["Friends"].values[0]
            if groom_star_lord in friends:
                return "Partial Match"

        if bride_type != groom_type:
            return "Partial Match"
        else:
            return result

    # If bride and groom's Rajjus are different
    return "Good Match"


print(dataframes['star']['Stars (Tamil)'].tolist())

user_input_1 = int(input("Enter the User ID for a female: "))

# Prompt the user to enter a user ID for a male
user_input_2 = int(input("Enter the User ID for a male: "))
rule_check = {}
# Retrieve filtered user IDs based on gender
all_placements = filter_by_user_gender(dataframes['user_astro_Basic'], user_input_1, user_input_2)

# If placements are found, proceed to find planet placements
if len(all_placements) == 2:
    placements_1 = find_planet_placements(dataframes['user_birth_chart'], all_placements[0])
    placements_2 = find_planet_placements(dataframes['user_birth_chart'], all_placements[1])
    bride_rasi = find_rasi(placements_1[0]['planet_house'], placements_1[0]['house_name'])
    groom_rasi = find_rasi(placements_2[0]['planet_house'], placements_2[0]['house_name'])
    print(bride_rasi, groom_rasi)
    # print(placements_1[0]['star']['moon_star'])
    nakshatras = dataframes['star']['Stars (Tamil)'].tolist()
    bride_star = placements_1[0]['star']['moon_star']
    groom_star = placements_2[0]['star']['moon_star']
    rule_check["1.1"] = dina_porutham(bride_star, groom_star, nakshatras)
    rule_check["1.2"] = gana_porutham(bride_star, groom_star)
    rule_check["1.3"] = mahendra_porutham(bride_star, groom_star, nakshatras)
    rule_check["1.4"] = nadi_porutham(bride_star, groom_star)
    rule_check["1.5"] = rasi_porutham(bride_rasi[0], groom_rasi[0])
    rule_check["1.6"] = rasyadhipathi_porutham(bride_rasi[0], groom_rasi[0], dataframes['house_name'],
                                               dataframes['planets_friends_neutral_enemies'])
    rule_check["1.7"] = vasya_porutham(bride_rasi[0], groom_rasi[0])
    rule_check["1.8"] = yoni_porutham_with_points(bride_star, groom_star)
    bride_rasi_lord = finding_ruling_planet_names(bride_rasi, dataframes["house_name"])
    groom_rasi_lord = finding_ruling_planet_names(groom_rasi, dataframes["house_name"])
    # print(bride_rasi_lord[0]['ruling_planet_name'],groom_rasi_lord[0]['ruling_planet_name'])
    rule_check["1.9"] = rajju_porutham_with_mapping(
        bride_star, groom_star, bride_rasi[0], groom_rasi[0], bride_rasi_lord[0]['ruling_planet_name'],
        groom_rasi_lord[0]['ruling_planet_name'],
        "moon", "moon", dataframes['planets_friends_neutral_enemies']
    )
    rule_check["1.10"] = vedhai_mismatch(bride_star, groom_star)
    print(rule_check)
    scores = {
        "Excellent Match": 10,
        "Good Match": 8,
        "Partial Match": 5,
        "Poor Match": 2,
        "No Match": 0

    }
    df = pd.DataFrame({
        "Key": rule_check.keys(),
        "Rule": [
            "Dina Porutham - Health and well-being",
            "Gana Porutham - Temperament compatibility",
            "Mahendra Porutham - Progeny and children’s well-being",
            "Nadi_porutham - Longevity of the wife and financial prosperity",
            "rasi_porutham - Physical compatibility and intimacy",
            "rasyadhipathi_porutham - Emotional and zodiac compatibility",
            "vasya_porutham - Relationship between the ruling planets of their zodiac signs",
            "yoni_porutham - Attraction and mutual respect",
            "Rajju Porutham - Longevity of the husband and prevention of widowhood",
            "Vedha Porutham - Protection from negative influences or doshas"
        ],
        "Result": rule_check.values(),
        "Score": [scores[match] for match in rule_check.values()]
    })

    # Define the output file path
    file_path = "Marriage_matching_10_rule_prediction.xlsx"

    # Write the DataFrame to an Excel file
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Result_Data', index=False)

    print(f"Excel file created successfully at {file_path}.")
