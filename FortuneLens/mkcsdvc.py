# # import pandas as pd
# #
# # # Your data
# # data = [
# #     {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': <PERSON>alse, '2.3.1': <PERSON>als<PERSON>, '2.3.2': <PERSON>als<PERSON>, '2.3.3': <PERSON>als<PERSON>, '2.3.4': <PERSON>als<PERSON>, '2.3.5': <PERSON>als<PERSON>, '2.4.1': <PERSON>alse, '2.4.2': <PERSON>alse, '2.4.3': <PERSON>alse, '2.4.4': <PERSON>alse, '2.4.5': <PERSON>als<PERSON>, '2.5.1': <PERSON>als<PERSON>, '2.5.2': Fals<PERSON>}], 'Rule 3': [{'3.1.1': False, '3.1.2': <PERSON>als<PERSON>, '3.2.1': <PERSON>als<PERSON>, '3.2.2': <PERSON>, '3.2.3': False, '3.2.4': False, '3.3.1': Fals<PERSON>, '3.3.2': False, '3.3.3': <PERSON>als<PERSON>, '3.3.4': False, '3.3.5': False, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': <PERSON>alse, '3.5.2': True, '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
# #     # Add the rest of your data here
# # ]
# #
# # # Points table
# # points_table = {
# #     '1.1': 10, '2.1.1': 10, '2.1.2': 9, '2.2.1': 8, '2.2.2': 8, '2.2.3': 8, '2.2.4': 7,
# #     '2.3.1': 9, '2.3.2': 7, '2.3.3': 7, '2.3.4': 7, '2.3.5': 6, '2.4.1': 9, '2.4.2': 7,
# #     '2.4.3': 7, '2.4.4': 7, '2.4.5': 6, '2.5.1': 8, '2.5.2': 8, '3.1.1': 10, '3.1.2': 9,
# #     '3.2.1': 8, '3.2.2': 8, '3.2.3': 8, '3.2.4': 7, '3.3.1': 10, '3.3.2': 9, '3.3.3': 8,
# #     '3.3.4': 8, '3.3.5': 7, '3.4.1': 10, '3.4.2': 9, '3.4.3': 8, '3.4.4': 8, '3.4.5': 7,
# #     '3.5.1': 10, '3.5.2': 9, '3.5.3': 8, '3.5.4': 8, '3.5.5': 7
# # }
# #
# # # Flatten the data with rule_name as 1.1 or 2.1.2
# # flattened_data = []
# # for item in data:
# #     user_id = item['user_id']
# #     for rule_name, sub_rules in item.items():
# #         if rule_name == 'user_id':
# #             continue
# #         if isinstance(sub_rules, dict):
# #             for condition, result in sub_rules.items():
# #                 points = points_table.get(condition, 0)
# #                 flattened_data.append({
# #                     'user_id': user_id,
# #                     'rule_name': condition,
# #                     'result': result,
# #                     'points': points if result else 0
# #                 })
# #         elif isinstance(sub_rules, list):
# #             for sub_rule in sub_rules:
# #                 for condition, result in sub_rule.items():
# #                     points = points_table.get(condition, 0)
# #                     flattened_data.append({
# #                         'user_id': user_id,
# #                         'rule_name': condition,
# #                         'result': result,
# #                         'points': points if result else 0
# #                     })
# #
# # # Convert to DataFrame
# # df = pd.DataFrame(flattened_data)
# # print(df)
# #
# # # Aggregate points for each user
# # total_points = df.groupby('user_id')['points'].sum().reset_index()
# # total_points.columns = ['user_id', 'total_points']
# #
# # # Write the results to an Excel file
# # with pd.ExcelWriter('user_data1.xlsx') as writer:
# #     df.to_excel(writer, sheet_name='Detailed_Results', index=False)
# #     total_points.to_excel(writer, sheet_name='Total_Points', index=False)
# import pandas as pd
#
# # Your data
# data = [
#     {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': False, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': True, '3.2.3': False, '3.2.4': False, '3.3.1': False, '3.3.2': False, '3.3.3': False, '3.3.4': False, '3.3.5': False, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': True, '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
#     # Add the rest of your data here
# ]
#
# # Points table
# points_table = {
#     '1.1': 10, '2.1.1': 10, '2.1.2': 9, '2.2.1': 8, '2.2.2': 8, '2.2.3': 8, '2.2.4': 7,
#     '2.3.1': 9, '2.3.2': 7, '2.3.3': 7, '2.3.4': 7, '2.3.5': 6, '2.4.1': 9, '2.4.2': 7,
#     '2.4.3': 7, '2.4.4': 7, '2.4.5': 6, '2.5.1': 8, '2.5.2': 8, '3.1.1': 10, '3.1.2': 9,
#     '3.2.1': 8, '3.2.2': 8, '3.2.3': 8, '3.2.4': 7, '3.3.1': 10, '3.3.2': 9, '3.3.3': 8,
#     '3.3.4': 8, '3.3.5': 7, '3.4.1': 10, '3.4.2': 9, '3.4.3': 8, '3.4.4': 8, '3.4.5': 7,
#     '3.5.1': 10, '3.5.2': 9, '3.5.3': 8, '3.5.4': 8, '3.5.5': 7
# }
#
# # Flatten the data with rule_name as 1.1 or 2.1.2
# flattened_data = []
# for item in data:
#     user_id = item['user_id']
#     row = {'user_id': user_id}
#     for rule_name, sub_rules in item.items():
#         if rule_name == 'user_id':
#             continue
#         if isinstance(sub_rules, dict):
#             for condition, result in sub_rules.items():
#                 row[condition] = result
#                 row[condition + '_points'] = points_table.get(condition, 0) if result else 0
#         elif isinstance(sub_rules, list):
#             for sub_rule in sub_rules:
#                 for condition, result in sub_rule.items():
#                     row[condition] = result
#                     row[condition + '_points'] = points_table.get(condition, 0) if result else 0
#     flattened_data.append(row)
#
# # Convert to DataFrame
# df = pd.DataFrame(flattened_data)
# print(df)
#
# # Write the results to an Excel file
# with pd.ExcelWriter('user_data_single_row1.xlsx') as writer:
#     df.to_excel(writer, index=False)
import pandas as pd

# # Your multiple datasets
# datasets = [
#     # First dataset
#     [
#         {'Rule 1': {'1.1': False}, 'Rule 2': [
#             {'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': False,
#              '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False,
#              '2.4.2': False, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}],
#          'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': True, '3.2.3': False, '3.2.4': False,
#                      '3.3.1': False, '3.3.2': False, '3.3.3': False, '3.3.4': False, '3.3.5': False, '3.4.1': False,
#                      '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': True,
#                      '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
#         # Add more data as needed
#     ],
#     # Second dataset
#     [
#         {'Rule 1': {'1.1': True}, 'Rule 2': [
#             {'2.1.1': False, '2.1.2': True, '2.2.1': False, '2.2.2': True, '2.2.3': False, '2.2.4': False,
#              '2.3.1': False, '2.3.2': True, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False,
#              '2.4.2': False, '2.4.3': True, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}],
#          'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': True, '3.2.2': False, '3.2.3': True, '3.2.4': False,
#                      '3.3.1': False, '3.3.2': False, '3.3.3': True, '3.3.4': False, '3.3.5': False, '3.4.1': False,
#                      '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': False,
#                      '3.5.3': False, '3.5.4': False, '3.5.5': True}], 'user_id': 100002},
#         # Add more data as needed
#     ]
# ]
#

import pandas as pd

import pandas as pd
import pandas as pd

def point_table(datasets, file_name):
    file_path = 'user_data_' + file_name + '.xlsx'
    # Points table
    points_table = {
        '1.1': 10, '2.1.1': 10, '2.1.2': 9, '2.2.1': 8, '2.2.2': 8, '2.2.3': 8, '2.2.4': 7,
        '2.3.1': 9, '2.3.2': 7, '2.3.3': 7, '2.3.4': 7, '2.3.5': 6, '2.4.1': 9, '2.4.2': 7,
        '2.4.3': 7, '2.4.4': 7, '2.4.5': 6, '2.5.1': 8, '2.5.2': 8, '3.1.1': 10, '3.1.2': 9,
        '3.2.1': 8, '3.2.2': 8, '3.2.3': 8, '3.2.4': 7, '3.3.1': 10, '3.3.2': 9, '3.3.3': 8,
        '3.3.4': 8, '3.3.5': 7, '3.4.1': 10, '3.4.2': 9, '3.4.3': 8, '3.4.4': 8, '3.4.5': 7,
        '3.5.1': 10, '3.5.2': 9, '3.5.3': 8, '3.5.4': 8, '3.5.5': 7
    }

    # Flatten and combine data
    flattened_data = []
    for item in datasets:  # This should iterate over datasets directly
        user_id = item['user_id']
        row = {'user_id': user_id}
        for rule_name, sub_rules in item.items():
            if rule_name == 'user_id':
                continue
            if isinstance(sub_rules, dict):
                for condition, result in sub_rules.items():
                    row[condition] = result
                    row[condition + '_points'] = points_table.get(condition, 0) if result else 0
            elif isinstance(sub_rules, list):
                for sub_rule in sub_rules:
                    for condition, result in sub_rule.items():
                        row[condition] = result
                        row[condition + '_points'] = points_table.get(condition, 0) if result else 0
        flattened_data.append(row)

    # Convert to DataFrame
    df = pd.DataFrame(flattened_data)

    # Reorder columns to have user_id first, then booleans, then points
    columns = ['user_id'] + sorted(
        [col for col in df.columns if col != 'user_id' and not col.endswith('_points')]) + sorted(
        [col for col in df.columns if col.endswith('_points')])
    df = df[columns]

    # Calculate total points for each user, only adding the points columns
    total_points = df[['user_id'] + [col for col in df.columns if col.endswith('_points')]].groupby(
        'user_id').sum().reset_index()
    total_points['total_points'] = total_points[[col for col in total_points.columns if col != 'user_id']].sum(axis=1)

    # Print the DataFrames to check
    # print(df)
    # print(total_points[['user_id', 'total_points']])

    # Write the results to an Excel file
    with pd.ExcelWriter(file_path) as writer:
        df.to_excel(writer, sheet_name='Ordered_Data', index=False)
        total_points[['user_id', 'total_points']].to_excel(writer, sheet_name='Total_Points', index=False)

# Example data
datasets = [
    {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': False, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': True, '3.2.3': False, '3.2.4': False, '3.3.1': False, '3.3.2': False, '3.3.3': False, '3.3.4': False, '3.3.5': False, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': True, '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
    {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': False, '2.2.3': False, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': True, '2.4.3': False, '2.4.4': True, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': False, '3.2.3': True, '3.2.4': False, '3.3.1': False, '3.3.2': True, '3.3.3': False, '3.3.4': False, '3.3.5': True, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': True, '3.5.1': False, '3.5.2': False, '3.5.3': False, '3.5.4': False, '3.5.5': False}], 'user_id': 100002},
    {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': False, '2.2.3': False, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': True, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3':

# Example data
datasets = [
    [{'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': False, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': True, '3.2.3': False, '3.2.4': False, '3.3.1': False, '3.3.2': False, '3.3.3': False, '3.3.4': False, '3.3.5': False, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': True, '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
     {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': False, '2.2.3': False, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': True, '2.4.3': False, '2.4.4': True, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': False, '3.2.3': True, '3.2.4': False, '3.3.1': False, '3.3.2': True, '3.3.3': False, '3.3.4': False, '3.3.5': True, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': True, '3.5.1': False, '3.5.2': False, '3.5.3': False, '3.5.4': False, '3.5.5': False}], 'user_id': 100002},
     # Add more data as needed
    ]
]

# Example usage
point_table(datasets, 'example')

# Example data
datasets = [
    [{'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': True, '2.2.3': True, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': False, '2.4.3': False, '2.4.4': False, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': True, '3.2.3': False, '3.2.4': False, '3.3.1': False, '3.3.2': False, '3.3.3': False, '3.3.4': False, '3.3.5': False, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': False, '3.5.1': False, '3.5.2': True, '3.5.3': False, '3.5.4': True, '3.5.5': False}], 'user_id': 100001},
     {'Rule 1': {'1.1': False}, 'Rule 2': [{'2.1.1': False, '2.1.2': False, '2.2.1': False, '2.2.2': False, '2.2.3': False, '2.2.4': False, '2.3.1': False, '2.3.2': False, '2.3.3': False, '2.3.4': False, '2.3.5': False, '2.4.1': False, '2.4.2': True, '2.4.3': False, '2.4.4': True, '2.4.5': False, '2.5.1': False, '2.5.2': False}], 'Rule 3': [{'3.1.1': False, '3.1.2': False, '3.2.1': False, '3.2.2': False, '3.2.3': True, '3.2.4': False, '3.3.1': False, '3.3.2': True, '3.3.3': False, '3.3.4': False, '3.3.5': True, '3.4.1': False, '3.4.2': False, '3.4.3': False, '3.4.4': False, '3.4.5': True, '3.5.1': False, '3.5.2': False, '3.5.3': False, '3.5.4': False, '3.5.5': False}], 'user_id': 100002},
     # Add more data as needed
    ]
]

# Example usage
point_table(datasets, 'example')
