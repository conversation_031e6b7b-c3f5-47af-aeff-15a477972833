import pandas as pd
from datetime import datetime

# Vimsottari Dasha planetary periods (in years) and sequence
vimsottari_sequence = ["Ketu", "Venus", "Sun", "Moon", "Mars", "Rahu", "Jupiter", "Saturn", "Mercury"]
vimsottari_periods = {
    "Ketu": 7, "Venus": 20, "Sun": 6, "Moon": 10,
    "Mars": 7, "Rahu": 18, "Jupiter": 16, "Saturn": 19, "Mercury": 17
}
total_vimsottari_years = 120  # Total cycle duration in years
days_per_year = 365.25  # Approximation for a year in days


def julian_to_datetime(jd):
    """Convert Julian Day to a readable datetime format."""
    J = jd + 0.5
    Z = int(J)
    F = J - Z
    if Z < 2299161:
        A = Z
    else:
        alpha = int((Z - 1867216.25) / 36524.25)
        A = Z + 1 + alpha - int(alpha / 4)
    B = A + 1524
    C = int((B - 122.1) / 365.25)
    D = int(365.25 * C)
    E = int((B - D) / 30.6001)
    day = B - D - int(30.6001 * E) + F
    month = E - 1 if E < 14 else E - 13
    year = C - 4716 if month > 2 else C - 4715

    # Extract hour, minute, second
    frac_day = day - int(day)
    hours = int(frac_day * 24)
    minutes = int((frac_day * 24 - hours) * 60)
    seconds = int((((frac_day * 24 - hours) * 60 - minutes) * 60))

    # Convert to readable format
    date_time = datetime(year, month, int(day), hours, minutes, seconds)
    return date_time.strftime("%Y-%m-%d %I:%M:%S %p")


def get_next_lord(current_lord):
    """Get the next lord in the Vimsottari sequence."""
    idx = vimsottari_sequence.index(current_lord)
    return vimsottari_sequence[(idx + 1) % len(vimsottari_sequence)]


def calculate_dasha(jd, lord, level_duration, depth, max_depth, levels, lord_sequence):
    """
    Recursive function to calculate dasha levels up to the specified depth.

    :param jd: Julian start date of the period.
    :param lord: Current planetary lord.
    :param level_duration: Total duration for the current level in days.
    :param depth: Current depth in the dasha calculation hierarchy.
    :param max_depth: Maximum depth to calculate (e.g., Mahadasha, Bhukti, etc.).
    :return: Nested dictionary structure for this level.
    """
    if depth >= max_depth:  # Stop if the required depth is reached
        return None

    level_name = levels[depth]

    # Initialize the list for the current depth (if not already initialized)
    if level_name not in lord_sequence:
        lord_sequence[level_name] = []

    # Append the data for the current level to the list
    for _ in range(len(vimsottari_sequence)):
        # Duration of the current sub-period in days
        sub_period_years = vimsottari_periods[lord]
        sub_period_days = (sub_period_years / total_vimsottari_years) * level_duration

        # Collect the data for the dasha period in a dictionary
        lord_sequence[level_name].append({
            "Level": level_name,
            "Lord": lord,
            "Start Time": julian_to_datetime(jd),
            "End Time": julian_to_datetime(jd + sub_period_days),
        })

        # Move start date forward and get the next lord
        jd += sub_period_days
        lord = get_next_lord(lord)


def find_starting_lord(jd):
    """Determine the starting Mahadasha lord and elapsed period at the given JD."""
    elapsed_years = ((jd % total_vimsottari_years) / total_vimsottari_years) * 120
    cumulative_years = 0

    for lord in vimsottari_sequence:
        cumulative_years += vimsottari_periods[lord]
        if elapsed_years <= cumulative_years:
            remaining_years = cumulative_years - elapsed_years
            return lord, remaining_years * days_per_year  # Return lord and remaining period in days

    return "Ketu", 0  # Default fallback


def date_to_julian_day(date_string):
    """Convert a date to Julian Day (simplified for example)."""
    dt = datetime.strptime(date_string, "%Y-%m-%d")
    jd = 367 * dt.year - int((7 * (dt.year + ((dt.month + 9) // 12))) // 4) + int(
        (275 * dt.month) // 9) + dt.day + 1721013.5
    return jd


def calculate_dasha_stepwise(jd, levels=("Mahadasha", "Bhukti", "Antara", "Sukshma", "Prana")):
    """
    Stepwise calculation of Mahadasha, Bhukti, and Antara levels.

    :param jd: Julian Day for calculation.
    :param levels: Levels of Dasha to calculate (default: Mahadasha to Prana).
    :return: List of nested dictionaries containing dasha data for each step.
    """
    starting_lord, remaining_days = find_starting_lord(jd)
    total_duration = 365.25  # Default to 1 year for Varsha Vimsottari

    # Initialize empty dictionary for each level
    lord_sequence = {level: [] for level in levels}

    # Generate results for each level (Mahadasha, Bhukti, Antara, etc.)
    for depth in range(len(levels)):
        calculate_dasha(jd, starting_lord, total_duration, depth, len(levels), levels, lord_sequence)
        # After each level, update the starting lord to the next level's first lord
        starting_lord = get_next_lord(starting_lord)

    return lord_sequence


def display_dasha_dataframe(lord_sequence):
    """Display dasha levels in a tabular format using pandas."""
    # Flatten the structure and convert to DataFrame
    all_data = []
    for level, records in lord_sequence.items():
        for record in records:
            all_data.append(record)

    df = pd.DataFrame(all_data)

    # Print the DataFrame
    print(df)


# Example Usage:
jd = date_to_julian_day("2000-01-01")  # Example starting date
result = calculate_dasha_stepwise(jd)

# Display the dasha periods in tabular form
display_dasha_dataframe(result)
