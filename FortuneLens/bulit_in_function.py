import matplotlib.pyplot as plt
import pandas as pd
from pandas import DataFrame
from functools import reduce
import itertools
import random
import string


def generate_random_string(length):
    # Define the characters to choose from
    characters = string.ascii_letters + string.digits
    # Generate a random string
    random_string = ''.join(random.choice(characters) for _ in range(length))
    return random_string


def merge_dataframes(dfs):
    """
    Merge a list of DataFrames on the 'Planet' column.

    Parameters:
    dfs (list): List of DataFrames to merge.

    Returns:
    DataFrame: The merged DataFrame.
    """
    merged_df = reduce(lambda left, right: pd.merge(left, right, on='Planet', how='inner'), dfs)
    merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]
    return merged_df


def get_house_names_with_numbers(house_name, ascendant):
    """
    Get house names with their corresponding numbers, starting from the ascendant.

    Parameters:
    house_name (DataFrame): DataFrame containing house names.
    ascendant (str): The ascendant house name.

    Returns:
    tuple: A tuple containing the list of house names with numbers and the index of the ascendant.
    """
    house_names = house_name['House Name'].tolist()

    if ascendant not in house_names:
        raise ValueError(f"Ascendant '{ascendant}' is not a valid house name.")

    ascendant_index = house_names.index(ascendant)
    rotated_house_names = house_names[ascendant_index:] + house_names[:ascendant_index]
    house_names_with_numbers = [(i + 1, name) for i, name in enumerate(rotated_house_names)]

    return house_names_with_numbers, ascendant_index


def generate_box_positions(ascendant_index):
    """
    Generate the positions of the boxes in the South Indian chart.

    Parameters:
    ascendant_index (int): The index of the ascendant house.

    Returns:
    dict: A dictionary mapping house numbers to their positions.
    """
    base_positions = [
        (1, 3), (2, 3), (3, 3), (3, 2),
        (3, 1), (3, 0), (2, 0), (1, 0),
        (0, 0), (0, 1), (0, 2), (0, 3)
    ]

    rotated_positions = base_positions[ascendant_index:] + base_positions[:ascendant_index]
    box_positions = {i + 1: pos for i, pos in enumerate(rotated_positions)}

    return box_positions


def plot_south_indian_chart(file_path, house_names_with_numbers, ascendant_index, house_planet_mapping):
    """
    Plot the South Indian style horoscope chart.

    Parameters:
    house_names_with_numbers (list): List of house names with their numbers.
    ascendant_index (int): The index of the ascendant house.
    house_planet_mapping (dict): Dictionary mapping house numbers to planet names.

    Returns:
    pd.DataFrame: DataFrame containing the horoscope data.
    """
    data = []
    fig, ax = plt.subplots(figsize=(8, 8))
    ax.set_xlim(0, 4)
    ax.set_ylim(0, 4)
    ax.set_xticks([])
    ax.set_yticks([])

    for i in range(5):
        ax.plot([i, i], [0, 4], color='black')
        ax.plot([0, 4], [i, i], color='black')

    box_positions = generate_box_positions(ascendant_index)

    for house_number, house_name in house_names_with_numbers:
        x, y = box_positions[house_number]
        planet_name = house_planet_mapping.get(house_number, "")
        data.append({
            'planet_name': planet_name,
            'house_number': house_number,
            'house_name': house_name
        })
        ax.text(x + 0.5, y + 0.5, f'{house_number}\n{house_name}\n{planet_name}', ha='center', va='center', fontsize=12,
                bbox=dict(facecolor='white', edgecolor='black'))

    df = pd.DataFrame(data)
    # print(df)

    plt.title('South Indian Style Horoscope Chart')
    plt.savefig(file_path)
    # plt.show()
    return df


def find_placements_in_houses(df, planet, house_numbers_to_include=None):
    if house_numbers_to_include is None:
        house_numbers_to_include = df['house_number'].unique().tolist()
    """
    Find the placements of a specific planet in given houses.

    Parameters:
    df (pd.DataFrame): DataFrame containing the horoscope data.
    planet (str): The name of the planet to find.
    houses (list): List of house numbers to search for the planet.

    Returns:
    pd.DataFrame: DataFrame containing the placements of the planet in the specified houses.
    """
    placements = df[
        df['house_number'].isin(house_numbers_to_include) & df['planet_name'].str.contains(planet, case=False)]
    return not placements.empty


def check_ruling_planet_connections(dft, planet, house_name_df, house_numbers):
    # print(dft, planet, house_name_df)
    connections = {}
    for index, row in house_name_df.iterrows():
        house_number = index + 1
        ruling_planet = row['Ruling Planets']
        if ruling_planet and house_number in house_numbers:
            if planet in dft[dft['planet_name'].str.contains(ruling_planet, case=False)]['planet_name'].values:
                connections[house_number] = ruling_planet
                # print(connections)
    # print(connections)
    return connections


def find_rasi(planets_in_houses, house_names):
    """
    Find the Rasi (zodiac sign) based on the Moon's position.

    :param planets_in_houses: Dictionary mapping house numbers to planets.
    :param house_names: Dictionary mapping house names to zodiac signs.
    :return: A list of Rasi names where the Moon is located.
    """
    moon_rasi = []

    for house, planets in planets_in_houses.items():
        if isinstance(planets, str) and 'moon' in planets.lower():
            rasi_name = house_names.get(f'house_name_{house}', 'Unknown')
            moon_rasi.append(rasi_name)

    return moon_rasi


def find_planet_placements(df, user_id):
    user_data = df[df['user_id'] == user_id]
    if user_data.empty:
        print(f"No data found for user_id: {user_id}")
        return None

    placements = []
    for index, row in user_data.iterrows():
        house_names = {f'house_name_{i}': row[f'house_name_{i}'].upper() for i in range(1, 13)}
        planet_houses = {i: row[f'house_{i}_planets'] for i in range(1, 13)}
        planet_house_degrees = {i: row[f'house_{i}_planets_degree'] for i in range(1, 13)}
        stars = {f'{planet}_star': row[f'{planet}_star'].upper() for planet in
                 ['lagnam', 'sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']}
        padas = {f'{planet}_pada': row[f'{planet}_pada'] for planet in
                 ['lagnam', 'sun', 'moon', 'mars', 'mercury', 'jupiter', 'venus', 'saturn', 'rahu', 'ketu']}

        placement = {
            'user_id': row['user_id'],
            'house_name': house_names,
            'planet_house': planet_houses,
            'planet_house_degress': planet_house_degrees,
            'star': stars,
            'pada': padas,
            'dhasa': {'maha_dhasa_period': row['maha_dasha'].upper(),
                      'bhukti_dhasa_period': row['bhukti_dasha'].upper(),
                      'antara_dhasa_period': row['antara_dasha'].upper(),
                      'sukshma_dhasa_period': row['sukshma_dasha'].upper(),
                      'prana_dhasa_period': row['prana_dasha'].upper(),
                      }
        }
        # print(placements)
        placements.append(placement)

    return placements


def get_dob(df, user_id, para):
    # Search for the row with the given user_id
    user_data = df[df['user_id'] == user_id]

    # If the user exists in the DataFrame, return the DOB
    if not user_data.empty:
        return user_data[para].dt.strftime('%Y-%m-%d').values[0]
    else:
        return "User ID not found"


# def find_planet_placements(df, user_id):
#     user_data = df[df['user_id'] == user_id]
#     if user_data.empty:
#         print(f"No data found for user_id: {user_id}")
#         return None
#     placements = []
#     for index, row in user_data.iterrows():
#         placement = {
#             'user_id': row['user_id'],
#             'house_name': {
#                 'house_name_1': row['house_name_1'],
#                 'house_name_2': row['house_name_2'],
#                 'house_name_3': row['house_name_3'],
#                 'house_name_4': row['house_name_4'],
#                 'house_name_5': row['house_name_5'],
#                 'house_name_6': row['house_name_6'],
#                 'house_name_7': row['house_name_7'],
#                 'house_name_8': row['house_name_8'],
#                 'house_name_9': row['house_name_9'],
#                 'house_name_10': row['house_name_10'],
#                 'house_name_11': row['house_name_11'],
#                 'house_name_12': row['house_name_12']
#             },
#             'planet_house': {
#                 1: row['house_1_planets'],
#                 2: row['house_2_planets'],
#                 3: row['house_3_planets'],
#                 4: row['house_4_planets'],
#                 5: row['house_5_planets'],
#                 6: row['house_6_planets'],
#                 7: row['house_7_planets'],
#                 8: row['house_8_planets'],
#                 9: row['house_9_planets'],
#                 10: row['house_10_planets'],
#                 11: row['house_11_planets'],
#                 12: row['house_12_planets']
#             },
#             'planet_house_degress':{
#                 1: row['house_1_planets_degree'],
#                 2: row['house_2_planets_degree'],
#                 3: row['house_3_planets_degree'],
#                 4: row['house_4_planets_degree'],
#                 5: row['house_5_planets_degree'],
#                 6: row['house_6_planets_degree'],
#                 7: row['house_7_planets_degree'],
#                 8: row['house_8_planets_degree'],
#                 9: row['house_9_planets_degree'],
#                 10: row['house_10_planets_degree'],
#                 11: row['house_11_planets_degree'],
#                 12: row['house_12_planets_degree']
#             },
#             'star':
#                 {
#                     "lagna_star": row['lagnam_star'],
#                     "sun_star": row['sun_star'],
#                     "moon_star": row['moon_star'],
#                     "mars_star": row['mars_star'],
#                     "mercury_star": row['mercury_star'],
#                     "jupiter_star": row['jupiter_star'],
#                     "venus_star": row['venus_star'],
#                     "saturn_star": row['saturn_star'],
#                     "rahu_star": row['rahu_star'],
#                     "ketu_star": row['ketu_star']
#                 },
#             'pada':
#                 {
#                     "lagna_pada": row['lagnam_pada'],
#                     "sun_pada": row['sun_pada'],
#                     "moon_pada": row['moon_pada'],
#                     "mars_pada": row['mars_pada'],
#                     "mercury_pada": row['mercury_pada'],
#                     "jupiter_pada": row['jupiter_pada'],
#                     "venus_pada": row['venus_pada'],
#                     "saturn_pada": row['saturn_pada'],
#                     "rahu_pada": row['rahu_pada'],
#                     "ketu_pada": row['ketu_pada']
#                 },
#             'dhasa':
#                 {
#                     "dhasa_period": row['dhasa'],
#                 }
#         }
#         placements.append(placement)
#     return placements


def find_houses_names(df, house_numbers):
    house_names = []
    for house_number in house_numbers:
        result = df[df['house_number'] == house_number]
        if not result.empty:
            house_names.append(result['house_name'].values[0])
        else:
            house_names.append(None)
    return house_names


# print(df)
def finding_ruling_planet_names(house_names, df):
    if isinstance(house_names, list):
        results = []
        for house_name in house_names:
            result = df[df['House Name'] == house_name.upper()]
            if not result.empty:
                ruling_planet_name = result['Ruling Planets'].values[0]
                results.append({"house_name": house_name, "ruling_planet_name": ruling_planet_name})
            else:
                results.append({"house_name": house_name, "ruling_planet_name": "House name not found"})
        return results
    else:
        return "Invalid input: house_names should be a list"


def add_planet_to_ruling_planets(planet, results):
    for result in results:
        if result['ruling_planet_name'] != "House name not found":
            if result['ruling_planet_name']:
                result['ruling_planet_name'] = planet.upper() + ',' + result['ruling_planet_name']
            else:
                result['ruling_planet_name'] = 'KETU'
    return results


def combine_ruling_planets(houses):
    from itertools import combinations
    from random import choice

    # Extract all unique ruling planets
    all_planets = set()
    for house in houses:
        all_planets.update(house['ruling_planet_name'].split(','))

    # Generate all possible combinations of two planets
    planet_combinations = list(combinations(all_planets, 2))
    # print(planet_combinations)

    # Assign a random combination to each house
    for i, house in enumerate(houses):
        current_planets = set(house['ruling_planet_name'].split(','))
        if not any(current_planets == set(comb) for comb in planet_combinations):
            chosen_combination = choice(planet_combinations)
            if i == 1:  # Reverse the order for the second house
                house['ruling_planet_name'] = ','.join(reversed(chosen_combination))
            else:
                house['ruling_planet_name'] = ','.join(chosen_combination)

    return houses


def compare_ruling_planets(updated_results, df, house_numbers_to_include=None):
    if house_numbers_to_include is None:
        house_numbers_to_include = df['house_number'].unique().tolist()

    comparison_results = []
    df['planet_name'] = df['planet_name'].str.upper()  # Ensure all planet names in df are uppercase

    for result in updated_results:
        ruling_planet_name = result['ruling_planet_name'].upper()  # Keep ruling_planet_name as a single string

        # Check if ruling_planet_name is in any of the planet names in df
        df_result = df[df['planet_name'].str.contains(ruling_planet_name, na=False)]

        if not df_result.empty:
            for _, row in df_result.iterrows():
                house_number = int(row['house_number'])  # Ensure house_number is an integer
                if house_number in house_numbers_to_include:
                    df_planet_name = row['planet_name']
                    match = ruling_planet_name in df_planet_name
                    comparison_results.append({
                        "house_number": house_number,
                        "ruling_planet_name": ruling_planet_name,
                        "df_planet_name": df_planet_name,
                        "match": match
                    })
        else:
            for house_number in house_numbers_to_include:
                comparison_results.append({
                    "house_number": house_number,
                    "ruling_planet_name": ruling_planet_name,
                    "df_planet_name": "House name not found",
                    "match": False
                })

    return comparison_results


def add_star_details(houses, star_data, additional_stars):
    # print(houses, star_data, additional_stars)
    for house in houses:
        ruling_planet = house['ruling_planet_name'].lower().replace(' ', '_') + '_star'
        stars = star_data[star_data['Planet'].str.lower() == house['ruling_planet_name'].lower()][
            'Stars (Tamil)'].tolist()
        house['stars'] = stars
        house['user_star_name'] = additional_stars.get(ruling_planet, '')
    return houses


def check_star_in_stars(houses, star_name=None):
    if star_name is None:
        for house in houses:
            if house['user_star_name'] in house.get('stars', []):
                return True
        return False
    else:
        for house in houses:
            if star_name in house.get('stars', []):
                return True
        return False


def check_ruling_planet_star_in_stars(houses, star_name=None):
    if star_name is None:
        for house in houses:
            if house['user_star_name'] in house.get('stars', []):
                return True
        return False
    else:
        for house in houses:
            if star_name in house.get('stars', []):
                return True
        return False


def add_star_to_ruling_planet(houses):
    updated_houses = []
    for house in houses:
        ruling_planet = house['ruling_planet_name'].lower().replace(' ', '_') + '_star'
        house['star'] = ruling_planet  # Adding the star key and value
        updated_houses.append(house)
    return updated_houses


def get_stars_by_planets(dataframe, target_planets):
    stars_dict = {planet: [] for planet in target_planets}
    for planet in target_planets:
        stars_dict[planet] = dataframe[dataframe['Planet'] == planet]['Stars (Tamil)'].tolist()
    return stars_dict


#
def is_user_star_in_planet_stars(houses, planet_stars):
    # print(planet_stars)
    for house in houses:
        user_star_name = house['user_star_name']
        for stars in planet_stars.values():
            if user_star_name in map(str.upper, stars):
                return True
    return False


# def add_star_to_ruling_planet(houses):
#     updated_houses = []
#     for house in houses:
#         ruling_planet = house['ruling_planet_name'].lower().replace(' ', '_') + '_star'
#         house['star_name'] = ruling_planet
#         updated_houses.append(house)
#     return updated_houses


def check_rule_status_with_percentage(rule_dict):
    """
    Check if at least one value in the rule dictionary or list of dictionaries is True.
    If at least one value is True, return 'pass', otherwise return 'fail'.
    Also, calculate the percentage of True values.
    """
    total_values = 0
    true_values = 0

    def check_values(item):
        nonlocal total_values, true_values
        if isinstance(item, dict):
            for key, value in item.items():
                if isinstance(value, dict) or isinstance(value, list):
                    check_values(value)
                else:
                    total_values += 1
                    if value:
                        true_values += 1
        elif isinstance(item, list):
            for sub_item in item:
                check_values(sub_item)

    check_values(rule_dict)
    status = 'pass' if true_values > 0 else 'fail'
    percentage = (true_values / total_values) * 100 if total_values > 0 else 0
    return status, percentage


# def check_rule_status(rule_dict):
#     """
#     Check if at least one value in the rule dictionary or list of dictionaries is True.
#     If at least one value is True, return 'pass', otherwise return 'fail'.
#     """
#     if isinstance(rule_dict, dict):
#         for key, value in rule_dict.items():
#             if isinstance(value, dict):
#                 if check_rule_status(value) == 'PASS':
#                     return 'PASS'
#             elif isinstance(value, list):
#                 for item in value:
#                     if check_rule_status(item) == 'PASS':
#                         return 'PASS'
#             elif value:
#                 return 'PASS'
#     elif isinstance(rule_dict, list):
#         for item in rule_dict:
#             if check_rule_status(item) == 'PASS':
#                 return 'PASS'
#     return 'FAIL'


#
def find_house_aspects(planets_data, houses_data):
    # Create DataFrame for planets
    planets_df = pd.DataFrame(planets_data)

    # Create DataFrame for houses
    houses_df = pd.DataFrame(houses_data)

    # Function to find aspects for a given planet
    def find_aspects(planet, aspects_df):
        aspects = aspects_df[aspects_df['Planet'] == planet]['Aspect Houses'].values
        return aspects[0] if len(aspects) > 0 else "No aspects found"

    # Find aspects for the ruling planets in houses_df
    houses_df['Aspects'] = houses_df['ruling_planet_name'].apply(lambda x: find_aspects(x, planets_df))

    return houses_df


# def find_house_number(df, row):
#     print(df)
#     planets = row['ruling_planet_name']  # Assuming this is a single planet name
#     # Use isin to check if the planet name is in the planet_name column
#     house_numbers = df[df['planet_name'] == planets]['house_number']
#     # Return the first house number if there are any matches, otherwise return None
#     return house_numbers.iloc[0] if not house_numbers.empty else None
def find_house_number(row, df):
    ruling_planet = row['ruling_planet_name']
    for planet_names in df['planet_name'].dropna():
        if ruling_planet in planet_names.split(', '):
            house_number = df[df['planet_name'] == planet_names]['house_number']
            return house_number.iloc[0] if not house_number.empty else None
    return None


def calculate_rule_pass_percentages(data):
    rule_totals = {}
    user_count = len(data)

    for user in data:
        for rule in user['rules']:
            rule_name = rule['rule_name']
            if rule_name not in rule_totals:
                rule_totals[rule_name] = {'pass_count': 0, 'user_count': 0}
            rule_totals[rule_name]['user_count'] += 1
            if rule['status'] == 'pass':
                rule_totals[rule_name]['pass_count'] += 1

    overall_pass_percentages = {rule: round((totals['pass_count'] / user_count) * 100, 2) for rule, totals in
                                rule_totals.items()}

    return overall_pass_percentages


# def calculate_overall_percentages(data):
#     rule_totals = {}
#     num_users = len(data)
#
#     for user in data:
#         for rule in user['rules']:
#             rule_name = rule['rule_name']
#             if rule_name not in rule_totals:
#                 rule_totals[rule_name] = 0
#             rule_totals[rule_name] += rule['percentage']
#
#     overall_percentages = {rule: round(total / num_users, 2) for rule, total in rule_totals.items()}
#
#     return overall_percentages


def adjust_aspects(row):
    house_number = row['house_number']
    aspects = row['Aspects'].split(', ')
    adjusted_aspects = []
    for aspect in aspects:
        aspect_value = int(aspect[:-2])  # Remove 'th' and convert to int
        new_value = (house_number - 1) + aspect_value
        if new_value > 12:
            new_value -= 12
        adjusted_aspects.append(new_value)
    return adjusted_aspects


def check_ketu_in_aspects(data1, data2, planet_name):
    ketu_house_number = data2[data2['planet_name'].str.contains(planet_name, na=False)]['house_number'].values[0]
    data1['Ketu_Present'] = data1['Aspects_looking_house_number'].apply(lambda x: ketu_house_number in x)
    return data1


def any_match_true(data):
    return any(entry.get('match') for entry in data)


def check_dynamic_placements(df, planets, rasis=None):
    houses = df.to_dict('records')
    if rasis is not None:
        for house in houses:
            if house['house_name'].upper() in [rasi.upper() for rasi in rasis]:
                if house['planet_name'] and not pd.isna(house['planet_name']):
                    if all(planet.upper() in house['planet_name'].upper() for planet in planets):
                        return True
        return False
    else:
        houses = df.to_dict('records')
        for house in houses:
            if house['planet_name'] and not pd.isna(house['planet_name']):
                if all(planet.upper() in house['planet_name'].upper() for planet in planets):
                    return True
        return False


# def check_dynamic_star_placements(planets_stars, star_data, planet1, planet2):
#     planet1_star = planets_stars[f'{planet1.lower()}_star']
#     planet2_star = planets_stars[f'{planet2.lower()}_star']
#
#     star_of_planet2 = star_data[star_data['Planet'].str.upper() == planet2.upper()]['Stars (Tamil)'].values
#     star_of_planet1 = star_data[star_data['Planet'].str.upper() == planet1.upper()]['Stars (Tamil)'].values
#
#     return planet1_star in star_of_planet2 or planet2_star in star_of_planet1
def check_dynamic_star_placements(planets_stars, star_data, planets):
    for planet1 in planets:
        planet1_star = planets_stars[f'{planet1.lower()}_star']
        star_of_planet1 = star_data[star_data['Planet'].str.upper() == planet1.upper()]['Stars (Tamil)'].values

        for planet2 in planets:
            if planet1 == planet2:  # Skip if comparing the same planet
                continue

            planet2_star = planets_stars[f'{planet2.lower()}_star']
            star_of_planet2 = star_data[star_data['Planet'].str.upper() == planet2.upper()]['Stars (Tamil)'].values

            if planet1_star in star_of_planet2 or planet2_star in star_of_planet1:
                return True
    return False


# def parse_aspects(aspect_str):
#     return list(map(int, aspect_str.replace('th', '').split(', ')))


# def parse_aspects(aspect_str):
#     return list(map(int, [aspect.strip()[:-2] for aspect in aspect_str.split(', ')]))


def parse_aspects(aspect_str):
    return list(map(int, [aspect.strip()[:-2] for aspect in aspect_str]))


def check_aspects(df, aspect_data, planets):
    # Parse aspect rules from DataFrame
    aspect_df = pd.DataFrame(aspect_data)
    aspect_df['Aspect Houses'] = aspect_df['Aspect Houses'].apply(lambda x: x.split(', '))
    aspect_df['Aspect Houses'] = aspect_df['Aspect Houses'].apply(parse_aspects)
    aspect_rules = {row['Planet']: row['Aspect Houses'] for _, row in aspect_df.iterrows()}

    # print(aspect_rules,aspect_df)

    def get_house_positions(df, planet):
        return df[df['planet_name'].str.contains(planet, na=False)]['house_number'].values

    for planet1 in planets:
        house_positions1 = get_house_positions(df, planet1)
        for planet2 in planets:
            if planet1 == planet2:
                continue
            house_positions2 = get_house_positions(df, planet2)
            for pos1 in house_positions1:
                for aspect in aspect_rules[planet1.upper()]:
                    aspect_position = pos1 + aspect - 1
                    if aspect_position > 12:
                        aspect_position -= 12
                    if aspect_position in house_positions2:
                        return True
    return False


def find_house_names(placements, planets):
    planet_house = placements[0]['planet_house']
    house_names = placements[0]['house_name']
    results = {}

    for planet in planets:
        for house, planets_in_house in planet_house.items():
            if planets_in_house and isinstance(planets_in_house, str) and planet in planets_in_house:
                house_name_key = f'house_name_{house}'
                results[planet] = house_names.get(house_name_key, 'House not found')
                break
    return results


# Function to find the index of the house where a specific planet is located
def find_house_index(df, planet):
    planet_row = df[df['planet_name'].str.contains(planet, na=False, case=False)]
    if not planet_row.empty:
        return planet_row.index[0]
    return None


# Function to rotate houses starting from the house where a specific planet is located
def rotate_houses_from_planet(df, planet):
    house_index = find_house_index(df, planet)
    if house_index is not None:
        df_rotated = pd.concat([df.iloc[house_index:], df.iloc[:house_index]], ignore_index=True)
        df_rotated['house_number'] = range(1, 13)
        return df_rotated
    return df
