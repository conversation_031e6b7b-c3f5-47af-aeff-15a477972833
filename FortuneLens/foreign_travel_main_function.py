from datetime import datetime

from bulit_in_function import *

from datetime import datetime, timedelta
import swisseph as swe

# Define zodiac signs
raasi_list = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
              '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']


def find_dasha_periods(dasha_periods, house_data):
    # print(list(eval(dasha_periods)))

    ruling_planets = [house.get('ruling_planet_name', '').lower() for house in house_data]

    matching_periods = []
    for period_data in dasha_periods:
        print(period_data)
        if not isinstance(period_data, tuple) or len(period_data) != 2:
            print(f"Skipping invalid data: {period_data}")
            continue
        period, date = period_data
        for planet in ruling_planets:
            if planet in period:
                matching_periods.append((period, date))

    return matching_periods


#
# from datetime import datetime


def parse_datetime_to_date(date_str):
    date_str = str(date_str).strip()
    if len(date_str) == 10:
        date_str += " 00:00:00"
    date_str = date_str.replace(" AM", "").replace(" PM", "")
    try:
        return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S").date()
    except ValueError:
        try:
            return datetime.strptime(date_str, "%d-%m-%Y %H:%M:%S").date()
        except ValueError:
            print(f"Error: Unable to parse the datetime string '{date_str}'")
            return None


def check_period_within_age_range(start_date_str, end_date_str, dob_date_str, start_age, end_age):
    start_date = parse_datetime_to_date(start_date_str)
    end_date = parse_datetime_to_date(end_date_str)
    dob_date = parse_datetime_to_date(dob_date_str) if isinstance(dob_date_str, str) else dob_date_str
    if not start_date or not end_date or not dob_date:
        print(f"Invalid date(s): start_date={start_date}, end_date={end_date}, dob_date={dob_date}")
        return False
    start_age_at_period_start = (start_date - dob_date).days // 365
    end_age_at_period_end = (end_date - dob_date).days // 365
    return not (end_age_at_period_end < start_age or start_age_at_period_start > end_age)


def find_periods_by_ruling_planet(planetary_periods, house_data, dob, start_age=0, end_age=80):
    dob_date = datetime.strptime(dob, "%Y-%m-%d").date()
    age_start_date = dob_date.replace(year=dob_date.year + start_age)
    age_end_date = dob_date.replace(year=dob_date.year + end_age)

    all_matching_periods = []

    for house in house_data:
        ruling_planet_name = house.get("ruling_planet_name", "").lower()
        matching_periods = [
            period for period in planetary_periods
            if ruling_planet_name in period[0].lower()
               and check_period_within_age_range(period[1], period[2], dob_date, start_age, end_age)
        ]
        all_matching_periods.extend(matching_periods)

    # Sort by start date
    sorted_matching_periods = sorted(all_matching_periods, key=lambda x: parse_datetime_to_date(x[1]))

    # Remove duplicates using a set
    unique_result = set()
    result = []
    for period in sorted_matching_periods:
        start_date = parse_datetime_to_date(period[1])
        end_date = parse_datetime_to_date(period[2])
        if start_date < age_start_date:
            start_date = age_start_date
        if end_date > age_end_date:
            end_date = age_end_date
        period_tuple = (period[0], start_date.strftime('%d-%m-%Y'), end_date.strftime('%d-%m-%Y'))

        # Add to result only if not a duplicate
        if period_tuple not in unique_result:
            unique_result.add(period_tuple)
            result.append(period_tuple)

    return result, age_start_date, age_end_date


# from datetime import datetime


# def parse_datetime_to_date(date_str):
#     """
#     Parses a datetime string and returns only the date in 'YYYY-MM-DD' format.
#     Handles both AM/PM and 24-hour formats, and cases where the time part is missing.
#     """
#     # Ensure date_str is a string
#     date_str = str(date_str).strip()
#
#     # If no time part is provided, add a default time '00:00:00'
#     if len(date_str) == 10:  # Only date part (YYYY-MM-DD)
#         date_str += " 00:00:00"
#
#     # Remove AM/PM suffix if present
#     if "AM" in date_str or "PM" in date_str:
#         date_str = date_str.replace(" AM", "").replace(" PM", "")
#
#     try:
#         # Parse the datetime string and return only the date part
#         date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
#         return date_obj.date()  # Return the date only in 'YYYY-MM-DD' format
#     except ValueError:
#         print(f"Error: Unable to parse the datetime string '{date_str}'")
#         return None
#
#
# def check_period_within_age_range(start_date_str, end_date_str, dob_date_str, start_age, end_age):
#     """
#     Checks if the period falls within the specified age range based on the date of birth.
#     """
#     # Parse the start and end dates
#     start_date = parse_datetime_to_date(start_date_str)
#     end_date = parse_datetime_to_date(end_date_str)
#
#     # Parse the date of birth
#     if isinstance(dob_date_str, str):
#         dob_date = parse_datetime_to_date(dob_date_str)
#     else:
#         dob_date = dob_date_str
#
#     # Validate parsed dates
#     if not start_date or not end_date or not dob_date:
#         print(f"Invalid date(s): start_date={start_date}, end_date={end_date}, dob_date={dob_date}")
#         return False
#
#     # Calculate the age at the start and end of the period
#     start_age_at_period_start = (start_date - dob_date).days // 365
#     end_age_at_period_end = (end_date - dob_date).days // 365
#
#     # Check if the age is within the range at both the start and end of the period
#     return start_age <= start_age_at_period_start <= end_age and start_age <= end_age_at_period_end <= end_age
#
#
# def find_periods_by_ruling_planet(planetary_periods, house_data, dob, start_age=0, end_age=80):
#     """
#     Finds planetary periods based on the ruling planet of the house and filters by age range,
#     then returns matching periods with only start and end dates, formatted as original tuples.
#     The results are sorted by the start date.
#     """
#     # Convert date of birth to a date object
#     dob_date = datetime.strptime(dob, "%Y-%m-%d").date()
#
#     age_21_date = dob_date.replace(year=dob_date.year + 0)
#     age_40_date = dob_date.replace(year=dob_date.year + 80)
#
#     # List to store all matching periods
#     all_matching_periods = []
#
#     # Iterate through houses and their ruling planets
#     for house in house_data:
#         ruling_planet_name = house.get("ruling_planet_name", "")
#
#         # Filter planetary periods matching the ruling planet
#         # for period in planetary_periods:
#         #     print(period[0], period[1], period[2])
#         matching_periods = [
#             period for period in planetary_periods
#             if ruling_planet_name in period[0]
#                and check_period_within_age_range(
#                 parse_datetime_to_date(period[1]), parse_datetime_to_date(period[2]), dob_date, start_age, end_age
#             )
#         ]
#
#         # Add matching periods to the final list
#         all_matching_periods.extend(matching_periods)
#
#     # Sort the matching periods by start date (period[1])
#     sorted_matching_periods = sorted(
#         all_matching_periods, key=lambda x: parse_datetime_to_date(x[1])
#     )
#
#     # Handle the case when the period starts before 'age_21_date' and ends after 'age_40_date'
#     result = []
#     for period in sorted_matching_periods:
#         start_date = parse_datetime_to_date(period[1])
#         end_date = parse_datetime_to_date(period[2])
#
#         # Adjust the period to start at least from the 21st age date if the period starts before
#         if start_date < age_21_date:
#             start_date = age_21_date
#
#         # Adjust the period to end at the 40th age date if the period ends after
#         if end_date > age_40_date:
#             end_date = age_40_date
#
#         # Append the adjusted period to the result
#         result.append((period[0], start_date.strftime('%d-%m-%Y'), end_date.strftime('%d-%m-%Y')))
#
#     # Return the periods in the required format (name, start_date, end_date)
#     return result, age_21_date, age_40_date


def find_key_and_planet(data):
    # List of stars associated with Venus
    venus_stars = {"BARANI", "POORAM", "POORADAM"}  # Use a set for faster lookup

    # List to store matching keys and planets
    matches = []

    # Iterate through the dictionary to find keys with Venus stars
    for key, star in data.items():
        if star.upper() in venus_stars and key.upper() != "LAGNAM_STAR":  # Skip LAGNAM_STAR
            matches.append((key.upper(), "VENUS"))  # Append matching key and planet

    # Return all matches if found, otherwise an empty list
    return matches


def parse_to_tuples(input_string):
    """
    Converts a string of tuples into a list of tuples.

    Args:
        input_string (str): The input string containing tuples as text.

    Returns:
        list: A list of tuples, where each tuple contains three elements (event, start time, end time).
    """
    import re

    # Remove the surrounding brackets and split into individual entries
    input_cleaned = input_string.strip("[]")
    tuple_strings = re.findall(r"\(([^)]+)\)", input_cleaned)

    # Convert each tuple string into a Python tuple
    return [tuple(map(str.strip, entry.split(','))) for entry in tuple_strings]


def add_venus_if_missing(houses):
    # Remove duplicates by `ruling_planet_name`, keeping the first occurrence
    seen_planets = set()
    unique_houses = []
    for house in houses:
        if house['ruling_planet_name'] not in seen_planets:
            unique_houses.append(house)
            seen_planets.add(house['ruling_planet_name'])

    # Check if Venus is already present in the list
    venus_exists = any(house['ruling_planet_name'] == 'VENUS' for house in unique_houses)

    if not venus_exists:
        # Add Venus with the house name "SYSTEM"
        unique_houses.append({'house_name': 'SYSTEM', 'ruling_planet_name': 'VENUS'})

    return unique_houses


def find_stars_by_houses(houses, house_to_planet_df, stars_df, default_planet):
    result = []
    planets_in_houses = set()

    # For each house, find its ruling planet and stars
    for house in houses:
        # Find the ruling planet for the house
        planet_row = house_to_planet_df[house_to_planet_df["House Name"] == house.upper()]
        if not planet_row.empty:
            planet = planet_row.iloc[0]["Ruling Planets"]
            # Add planet to the set to prevent duplication
            if planet not in planets_in_houses:
                planets_in_houses.add(planet)
            # Get the stars associated with the planet
            stars = stars_df[stars_df["Planet"] == planet]["Stars (Tamil)"].tolist()
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": planet,
                "Stars": stars
            })
        else:
            result.append({
                "house_name": house.upper(),
                "ruling_planet_name": None,
                "Stars": []
            })

    # Add default planet if it's not in planets_in_houses
    if default_planet not in planets_in_houses:
        stars = stars_df[stars_df["Planet"] == default_planet]["Stars (Tamil)"].tolist()
        result.append({
            "house_name": None,
            "ruling_planet_name": default_planet,
            "Stars": stars
        })

    return result


def get_all_stars_for_indices(df, planet_dict, indices):
    """
    Retrieve Tamil stars for specified indices based on ruling planets and include a house_name.

    Args:
        df (pd.DataFrame): DataFrame containing 'Planet' and 'Stars (Tamil)' columns.
        planet_dict (dict): Dictionary mapping indices to planet names (comma-separated).
        indices (list): List of indices to process.

    Returns:
        list: A list of dictionaries containing house name, ruling planets, and their corresponding Tamil stars.
    """
    result = []
    default_entry = {'house_name': None, 'ruling_planet_name': 'VENUS', 'Stars': ['BARANI', 'POORAM', 'POORADAM']}

    for idx in indices:
        planet_names = planet_dict.get(idx, None)

        if isinstance(planet_names, str):  # Check if valid string
            planet_names = [planet.strip() for planet in planet_names.split(',')]

            for planet_name in planet_names:
                # Filter DataFrame to find Tamil stars for the planet
                stars_for_planet = df.loc[df['Planet'].str.lower() == planet_name.lower(), 'Stars (Tamil)'].tolist()

                if stars_for_planet:  # Only process if there are matching stars
                    # Split stars into individual star names, uppercase, and clean whitespace
                    star_list = [star.strip().upper() for stars in stars_for_planet for star in stars.split(',')]

                    # Append the structured dictionary
                    result.append({
                        'house_name': f"{idx}_stay",
                        'ruling_planet_name': planet_name.upper(),
                        'Stars': star_list
                    })
                else:
                    # Append default entry if no stars are found for the planet
                    result.append(default_entry)
        else:
            # Append default entry if no valid planet names are found
            result.append(default_entry)

    # Ensure default 'VENUS' entry is present in the result
    if not any(entry.get('ruling_planet_name') == 'VENUS' for entry in result):
        result.append(default_entry)

    return result


def find_star_in_data(first_data, second_data):
    """
    Finds if any star from the second dataset exists in the first dataset and includes only the matching results.

    Args:
        first_data (dict): The dictionary containing star names in the first dataset.
        second_data (list): The list of dictionaries describing houses and their stars.

    Returns:
        dict: A dictionary with the key 'matches' containing a list of matched data from second_data,
              with ruling planet information from first_data.
    """
    matches = []

    # Get all stars from the first dataset and their corresponding house
    first_data_stars = {star: house for house, star in first_data.items()}

    # Iterate over second_data to find matches
    for house in second_data:
        house_name = house.get('house_name', 'Unknown')
        ruling_planet_name = house.get('ruling_planet_name', 'Unknown')
        stars = house.get('Stars', [])

        # Iterate through stars and match with first_data
        for star in stars:
            if star in first_data_stars:
                # If the star exists in first_data, get the corresponding house and ruling planet
                matched_house = first_data_stars[star]
                matched_ruling_planet = matched_house.split('_')[0].capitalize()
                matches.append({
                    "star": star,
                    "house_name": matched_house,
                    "ruling_planet_name": matched_ruling_planet.upper(),
                })

    return matches


def remove_duplicates(data):
    seen = set()
    result = []

    for entry in data:
        ruling_planet = entry.get('ruling_planet_name')
        if ruling_planet and ruling_planet not in seen:
            seen.add(ruling_planet)
            result.append(entry)

    return result


def is_date_in_ranges(date_to_check, date_ranges):
    """
    Check if the given date is within any of the specified date ranges.

    Parameters:
        date_to_check (str): The date to check in 'dd-mm-yyyy' or 'yyyy-mm-dd' format.
        date_ranges (list): A list of tuples, each containing a period name and start and end dates.

    Returns:
        bool: True if the date is in any range, otherwise False.
    """
    # Detect and convert the input date to a datetime object
    try:
        date_to_check = datetime.strptime(date_to_check, "%d-%m-%Y")
    except ValueError:
        try:
            date_to_check = datetime.strptime(date_to_check, "%Y-%m-%d")
        except ValueError:
            raise ValueError("date_to_check must be in 'dd-mm-yyyy' or 'yyyy-mm-dd' format")
        # print(len(date_ranges))
        # Extract year and month
        check_year, check_month = date_to_check.year, date_to_check.month

        # Check if the month and year fall within any range
        for _, start_date, end_date in date_ranges:
            start_date = datetime.strptime(start_date, "%d-%m-%Y")
            end_date = datetime.strptime(end_date, "%d-%m-%Y")

            start_year, start_month = start_date.year, start_date.month
            end_year, end_month = end_date.year, end_date.month

            # Check if the given month-year falls within the range
            if (start_year < check_year < end_year) or \
                    (start_year == check_year and start_month <= check_month) or \
                    (end_year == check_year and check_month <= end_month):
                return True, len(date_ranges)

    return False, len(date_ranges)


#
#
# def is_date_in_ranges(date_to_check, date_ranges):
#     """
#     Check if the given date is within any of the specified date ranges.
#
#     Parameters:
#         date_to_check (str): The date to check in 'dd-mm-yyyy' or 'yyyy-mm-dd' format.
#         date_ranges (list): A list of tuples, each containing a period name and start and end dates.
#
#     Returns:
#         bool: True if the date is in any range, otherwise False.
#     """
#     # Detect and convert the input date to a datetime object
#     try:
#         date_to_check = datetime.strptime(date_to_check, "%d-%m-%Y")
#     except ValueError:
#         try:
#             date_to_check = datetime.strptime(date_to_check, "%Y-%m-%d")
#         except ValueError:
#             raise ValueError("date_to_check must be in 'dd-mm-yyyy' or 'yyyy-mm-dd' format")
#     # print(len(date_ranges))
#     for period_name, start_date, end_date in date_ranges:
#         # Convert start and end dates to datetime objects
#         start_date = datetime.strptime(start_date, "%d-%m-%Y")
#         end_date = datetime.strptime(end_date, "%d-%m-%Y")
#
#         # Check if the date is within the range
#         if start_date <= date_to_check <= end_date:
#             return True, len(date_ranges)
#
#     return False, len(date_ranges)


PLANET_CONSTANTS = {
    "SUN": swe.SUN,
    "MOON": swe.MOON,
    "MERCURY": swe.MERCURY,
    "VENUS": swe.VENUS,
    "MARS": swe.MARS,
    "JUPITER": swe.JUPITER,
    "SATURN": swe.SATURN,
    "URANUS": swe.URANUS,
    "NEPTUNE": swe.NEPTUNE,
    "PLUTO": swe.PLUTO,
}


def get_aspect_sign(current_sign_index, aspect, total_signs=12):
    aspect_index = (current_sign_index + (aspect - 1)) % total_signs
    return raasi_list[aspect_index]


def get_planet_sign(date, planet_name):
    planet = PLANET_CONSTANTS.get(planet_name.upper())
    if not planet:
        raise ValueError(f"Invalid planet name: {planet_name}")

    jd = swe.julday(date.year, date.month, date.day, 0)
    swe.set_sid_mode(swe.SIDM_LAHIRI)
    planet_longitude = swe.calc_ut(jd, planet, swe.FLG_SIDEREAL)[0][0]
    planet_sign_index = int(planet_longitude // 30)
    return raasi_list[planet_sign_index]


def get_planet_aspects(date, planet_name, aspects):
    planet = PLANET_CONSTANTS.get(planet_name.upper())
    if not planet:
        raise ValueError(f"Invalid planet name: {planet_name}")

    jd = swe.julday(date.year, date.month, date.day, 0)
    swe.set_sid_mode(swe.SIDM_LAHIRI)
    planet_longitude = swe.calc_ut(jd, planet, swe.FLG_SIDEREAL)[0][0]
    planet_sign_index = int(planet_longitude // 30)

    return {
        aspect: get_aspect_sign(planet_sign_index, aspect) for aspect in aspects
    }


# Function to find grouped periods for aspects and stays
def find_periods_grouped(dasha_periods, target_signs, planet_name, aspects):
    grouped_results = {aspect: [] for aspect in aspects}
    grouped_results['stay'] = []  # Adding the 'stay' key for planet stays in target_signs

    for dasha, start_date_str, end_date_str in dasha_periods:
        start_date = datetime.strptime(start_date_str, '%d-%m-%Y')
        end_date = datetime.strptime(end_date_str, '%d-%m-%Y')
        current_date = start_date
        active_periods = {}
        stay_period = {"start_date": None, "end_date": None, "sign": None}

        while current_date <= end_date:
            aspects_result = get_planet_aspects(current_date, planet_name, aspects)
            planet_sign = get_planet_sign(current_date, planet_name)

            # Check for matching aspects
            for aspect, sign in aspects_result.items():
                if sign.upper() in [ts.upper() for ts in target_signs]:
                    if aspect not in active_periods:
                        active_periods[aspect] = {"start_date": current_date, "end_date": current_date}
                    else:
                        active_periods[aspect]["end_date"] = current_date
                elif aspect in active_periods:
                    grouped_results[aspect].append((
                        dasha,
                        active_periods[aspect]["start_date"].strftime('%d-%m-%Y'),
                        active_periods[aspect]["end_date"].strftime('%d-%m-%Y')
                    ))
                    del active_periods[aspect]

            # Check for stays
            if planet_sign.upper() in [ts.upper() for ts in target_signs]:
                if stay_period["start_date"] is None:
                    stay_period["start_date"] = current_date
                    stay_period["sign"] = planet_sign
                stay_period["end_date"] = current_date
            else:
                if stay_period["start_date"] is not None:
                    grouped_results['stay'].append((
                        dasha,
                        stay_period["start_date"].strftime('%d-%m-%Y'),
                        stay_period["end_date"].strftime('%d-%m-%Y'),
                        stay_period["sign"]
                    ))
                    stay_period = {"start_date": None, "end_date": None, "sign": None}

            current_date += timedelta(days=1)

        # Close active and stay periods at the end of the range
        for aspect, period in active_periods.items():
            grouped_results[aspect].append((
                dasha,
                period["start_date"].strftime('%d-%m-%Y'),
                period["end_date"].strftime('%d-%m-%Y')
            ))

        if stay_period["start_date"] is not None:
            grouped_results['stay'].append((
                dasha,
                stay_period["start_date"].strftime('%d-%m-%Y'),
                stay_period["end_date"].strftime('%d-%m-%Y'),
                stay_period["sign"]
            ))

    return grouped_results


from datetime import datetime


def check_date_in_periods(result_data, check_date, fi_key):
    """
    Checks if a given date (month & year only) falls within any period in result_data.

    :param result_data: Dictionary with period data.
    :param check_date: Date to check (format: 'YYYY-MM-DD').
    :param fi_key: Filter key identifier.
    :return: Dictionary with filter results.
    """
    # Convert check_date to a datetime object (extract only month & year)
    check_date_obj = datetime.strptime(check_date, '%Y-%m-%d')
    check_month_year = (check_date_obj.year, check_date_obj.month)

    # Dictionary to store results
    filter_results = {}

    for count, periods in result_data.items():
        filter_key = f"filter {fi_key}_{count}"
        filter_count = f"filter {fi_key}_{count}_count"

        # Ensure periods exist for this count
        if not periods:
            filter_results[filter_key] = False
            filter_results[filter_count] = 0
            continue

        # Check if any period contains the check_date (only month & year)
        for period_data in periods:
            period, start_date, end_date, *_ = period_data  # Unpack safely
            start_date_obj = datetime.strptime(start_date, '%d-%m-%Y')
            end_date_obj = datetime.strptime(end_date, '%d-%m-%Y')

            # Extract (year, month) tuples
            start_month_year = (start_date_obj.year, start_date_obj.month)
            end_month_year = (end_date_obj.year, end_date_obj.month)

            # Check if check_date falls within the period based on month & year only
            if start_month_year <= check_month_year <= end_month_year:
                filter_results[filter_key] = True
                filter_results[filter_count] = len(periods)
                break
        else:
            filter_results[filter_key] = False
            filter_results[filter_count] = len(periods)

    return filter_results


# def check_date_in_periods(result_data, check_date, fi_key):
#     # Convert the check_date to a datetime object
#     check_date_obj = datetime.strptime(check_date, '%Y-%m-%d')
#
#     # Dictionary to hold the filter results
#     filter_results = {}
#     # total_count = sum(len(values) for values in result_data.values())
#     # Loop through the result data and check if the date falls within any period
#     for count in result_data:
#         filter_key = f"filter {fi_key}_{count}"
#         # Create the filter key (e.g., "filter 2.1_3")
#         filter_count = f"filter {fi_key}_{count}_count"
#         for period_data in result_data[count]:
#             period, start_date, end_date, _ = period_data
#             start_date_obj = datetime.strptime(start_date, '%d-%m-%Y')
#             end_date_obj = datetime.strptime(end_date, '%d-%m-%Y')
#
#             # Check if the check_date is between start_date and end_date
#             if start_date_obj <= check_date_obj <= end_date_obj:
#                 filter_results[filter_key] = True
#                 filter_results[filter_count] = len(result_data[count])
#                 break  # If found, no need to check further for this count
#         else:
#             filter_results[filter_key] = False  # If not found, set as False
#             filter_results[filter_count] = len(result_data[count])
#
#     return filter_results


def check_date_in_period(date_to_check, periods):
    date_obj = datetime.strptime(date_to_check, "%Y-%m-%d")
    # print(periods)
    total_count = sum(len(values) for values in periods.values())
    for key in periods.values():
        for period in key:
            start_date = datetime.strptime(period[1], "%d-%m-%Y")
            end_date = datetime.strptime(period[2], "%d-%m-%Y")
            if start_date <= date_obj <= end_date:
                return True, total_count
    return False, total_count


def check_planets_in_periods(period_data, planets_to_check):
    result = {}

    # Extract the planet names from the planets_to_check list
    planets_to_check_names = [item['ruling_planet_name'] for item in planets_to_check]

    # Loop through each period and count the occurrence of planets
    for period, start_date, end_date in period_data:
        # Extract the planets in the period string (split by '-')
        planets_in_period = period.split('-')

        # Count how many of the planets_to_check_names are present in the period
        found_planets = [planet for planet in planets_in_period if planet in planets_to_check_names]

        # If any planets are found, determine how many counts to add
        if found_planets:
            # The count is based on the number of planets in the period string
            count = len(found_planets)

            # Add the result to the dictionary, grouped by count
            if count not in result:
                result[count] = []
            result[count].append((period, start_date, end_date, count))

    # Sort the dictionary keys in descending order (largest to smallest)
    sorted_result = {key: result[key] for key in sorted(result.keys(), reverse=True)}

    return sorted_result


def handle_continuity_date(data):
    if not data:
        return []

    # Parse data into datetime objects
    parsed_data = [
        (item[0], datetime.strptime(item[1], '%d-%m-%Y'), datetime.strptime(item[2], '%d-%m-%Y'))
        for item in data
    ]

    # Sort by start date
    parsed_data.sort(key=lambda x: x[1])

    # Generate ranges
    ranges = []
    current_start = parsed_data[0][1]
    current_end = parsed_data[0][2]

    for i in range(1, len(parsed_data)):
        _, next_start, next_end = parsed_data[i]

        # Check for continuity
        if next_start <= current_end:
            current_end = max(current_end, next_end)
        else:
            ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")
            current_start = next_start
            current_end = next_end

    # Add last period
    ranges.append(f"{current_start.strftime('%m-%Y')} to {current_end.strftime('%m-%Y')}")

    return ranges


# Main rule function
def rule_one(placements, dataframes, df, dob, foreign_travel_date1, user_id):
    print("foreign_travel_date1: ", foreign_travel_date1)
    m_rule = {}
    rasi = find_rasi(placements[0]['planet_house'], placements[0]['house_name'])
    print("The Rasi where the Moon is located:", rasi)
    star = placements[0]['star']
    dhasa = placements[0]['dhasa']
    # # bhukti_dhasa_period
    # # antara_dhasa_period
    dh = parse_to_tuples(dhasa['antara_dhasa_period'])
    m_rule['user_id'] = user_id

    house_names = find_houses_names(df, [9, 12])
    print("house_names: ", house_names)
    result_11 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    result_11_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    result_11.extend(result_11_k)
    result_11 = remove_duplicates(result_11)
    print("rule_3.1.1 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_11, dob)
    # print(matching_periods_1)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 3.1.1'] = is_in_range
    # m_rule['Rule 3.1.1_count'] = count
    # filter_results = check_date_in_periods(matching_periods_1, foreign_travel_date1)
    #
    # # Output the result and filter check
    # print("Filter Results:", filter_results)

    result_12 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [9, 12])
    result_12_R = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    result_12_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    result_12.extend(result_12_R)
    result_12.extend(result_12_k)
    result_12.extend(result_11)
    result_12 = remove_duplicates(result_12)
    print("result_3.1.2 data: ", result_12)
    # matching_periods_2, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_12, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_2)
    # m_rule['Rule 3.1.2'] = is_in_range
    # m_rule['Rule 3.1.2_count'] = count
    result_13 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [9, 12])
    result_13.extend(result_12)
    result_13 = remove_duplicates(result_13)
    matching_periods_31, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
    is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_31)
    mk = check_planets_in_periods(matching_periods_31, result_13)
    print(mk)
    filter_results_31 = check_date_in_periods(mk, foreign_travel_date1, "3.1")

    # Output the result and filter check
    print("Filter Results 3 .1 : ", filter_results_31)
    # combined = {**m_rule, **filter_results}
    m_rule['Rule 3.1'] = is_in_range
    m_rule['Rule 3.1_count'] = count

    #rule 3.2
    house_names = find_houses_names(df, [8, 12])
    print("house_names: ", house_names)
    result_11 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    result_11_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    result_11.extend(result_11_k)
    result_11 = remove_duplicates(result_11)
    print("rule_3.2.1 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_11, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 3.2.1'] = is_in_range
    # m_rule['Rule 3.2.1_count'] = count

    result_12 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [8, 12])
    result_12_R = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    result_12_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    result_12.extend(result_12_R)
    result_12.extend(result_12_k)
    result_12.extend(result_11)
    result_12 = remove_duplicates(result_12)
    print("result_3.2.2 data: ", result_12)
    # matching_periods_2, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_12, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_2)
    # m_rule['Rule 3.2.2'] = is_in_range
    # m_rule['Rule 3.2.2_count'] = count
    # combined_32 = {**m_rule, **filter_results}
    # m_rule['Rule 3.2'] = is_in_range
    # m_rule['Rule 3.2'] = count

    result_13 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [8, 12])
    result_13.extend(result_12)
    result_13 = remove_duplicates(result_13)
    mk = check_planets_in_periods(matching_periods_31, result_13)
    print(mk)
    filter_results_32 = check_date_in_periods(mk, foreign_travel_date1, "3.2")

    # Output the result and filter check
    print("Filter Results 3 .2 : ", filter_results_32)
    matching_periods_3, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
    is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_3)
    m_rule['Rule 3.2'] = is_in_range
    m_rule['Rule 3.2_count'] = count

    # Rule 3.3
    house_names = find_houses_names(df, [6, 7, 12])
    print("house_names: ", house_names)
    result_11 = finding_ruling_planet_names(house_names, dataframes["house_name"])
    result_11 = remove_duplicates(result_11)
    print("rule_3.3.1 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_11, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 3.3.1'] = is_in_range
    # m_rule['Rule 3.3.1_count'] = count

    result_12 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [6, 7, 12])
    result_12.extend(result_11)
    result_12 = remove_duplicates(result_12)
    print("result_3.3.2 data: ", result_12)
    # matching_periods_2, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_12, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_2)
    # m_rule['Rule 3.3.2'] = is_in_range
    # m_rule['Rule 3.3.2_count'] = count

    result_13 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [6, 7, 12])
    result_13.extend(result_12)
    result_13 = remove_duplicates(result_13)
    matching_periods_3, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
    is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_3)
    m_rule['Rule 3.3.3'] = is_in_range
    m_rule['Rule 3.3.3_count'] = count
    mk = check_planets_in_periods(matching_periods_31, result_13)
    print(mk)
    filter_results_33 = check_date_in_periods(mk, foreign_travel_date1, "3.3")

    # Output the result and filter check
    print("Filter Results 3 .3 : ", filter_results_32)
    combined = {**m_rule, **filter_results_31, **filter_results_32, **filter_results_33}
    return m_rule, combined
    # #rasi = find_rasi(placements[0]['planet_house'], placements[0]['house_name'])
    # print("The Rasi where the Moon is located:", rasi)
    # star = placements[0]['star']
    # dhasa = placements[0]['dhasa']
    # # bhukti_dhasa_period
    # # antara_dhasa_period
    # dh = parse_to_tuples(dhasa['bhukti_dhasa_period'])
    # # print(len(dh))
    # m_rule['user_id'] = user_id
    # house_names = find_houses_names(df, [9, 12])
    # print("house_names: ", house_names)
    # result_11 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    # # print(result_11)
    # result_11_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    # result_11.extend(result_11_k)
    # # print(result_11)
    # result_11 = remove_duplicates(result_11)
    # print("rule_1.1 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_11, dob)
    # print(matching_periods_1)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 1.1'] = is_in_range
    # m_rule['Rule 1.1_count'] = count
    # house_names = find_houses_names(df, [8, 12])
    # print("house_names: ", house_names)
    # result_12 = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "RAHU")
    # # print(result_11)
    # result_12_k = find_stars_by_houses(house_names, dataframes['house_name'], dataframes['star'], "KETU")
    # result_12.extend(result_12_k)
    # # print(result_11)
    # result_12 = remove_duplicates(result_12)
    # print("rule_1.2 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_12, dob)
    # print(matching_periods_1)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 1.2'] = is_in_range
    # m_rule['Rule 1.2_count'] = count
    #
    # house_names = find_houses_names(df, [6, 7, 12])
    # print("house_names: ", house_names)
    # result_12 = remove_duplicates(result_12)
    # print("rule_1.2 data: ", result_11)
    # matching_periods_1, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_12, dob)
    # print(matching_periods_1)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_1)
    # m_rule['Rule 1.2'] = is_in_range
    # m_rule['Rule 1.2_count'] = count
    #
    # result_13 = get_all_stars_for_indices(dataframes['star'], placements[0]['planet_house'], [6, 7, 12])
    # result_13 = remove_duplicates(result_13)
    # print(result_13)
    # matching_periods_3, age_21_date, age_40_date = find_periods_by_ruling_planet(dh, result_13, dob)
    # is_in_range, count = is_date_in_ranges(foreign_travel_date1, matching_periods_3)
    # m_rule['Rule 1.3'] = is_in_range
    # m_rule['Rule 1.3_count'] = count
    # df = pd.DataFrame(matching_periods_1, columns=['Period', 'Start Date', 'End Date'])
    # df_cleaned = df.drop_duplicates()
    # cleaned_data = list(df_cleaned.itertuples(index=False, name=None))
    # print(f"21st Age Date: {age_21_date.strftime('%d-%m-%Y')}")
    # print(f"40th Age Date: {age_40_date.strftime('%d-%m-%Y')}")
    # # print(cleaned_data)
    # grouped_aspect_periods_11 = find_periods_grouped(cleaned_data, house_names, "JUPITER", [5, 7, 9])
    # # print(grouped_aspect_periods_11)
    # grouped_aspect_periods_12 = find_periods_grouped(cleaned_data, rasi, "JUPITER", [5, 7, 9])
    # grouped_aspect_periods_13 = find_periods_grouped(cleaned_data, [], "VENUS", [7])
    # grouped_aspect_periods_14 = find_periods_grouped(cleaned_data, rasi, "VENUS", [7])
    # m_rule['marriage_dasa_jup_asp_27'], m_rule['marriage_dasa_jup_asp_27_count'] = check_date_in_period(marriage_date,
    #                                                                                                     grouped_aspect_periods_11)
    # m_rule['marriage_das_jup_asp_ras'], m_rule['marriage_das_jup_asp_ras_count'] = check_date_in_period(marriage_date,
    #                                                                                                     grouped_aspect_periods_12)
    # m_rule['marriage_das_ven_in_7'], m_rule['marriage_das_ven_in_7_count'] = check_date_in_period(marriage_date,
    #                                                                                               grouped_aspect_periods_13)
    # m_rule['marriage_das_ven_in_ras'], m_rule['marriage_das_ven_in_ras_count'] = check_date_in_period(marriage_date,
    #                                                                                                   grouped_aspect_periods_14)
    # # print(len(matching_periods_1), len(matching_periods_2), len(matching_periods_3))
    # mk = check_planets_in_periods(cleaned_data, result_13)
    #
    # filter_results = check_date_in_periods(mk, marriage_date)
    #
    # # Output the result and filter check
    # print("Filter Results:", filter_results)
    # # print(mk)
    # combined = {**m_rule, **filter_results}
    # for i in matching_periods_1:
    #     print(i)
    # result_data = handle_continuity_date(matching_periods_1)
    # print(result_data)
    # return combined, grouped_aspect_periods_11, grouped_aspect_periods_12, grouped_aspect_periods_13, grouped_aspect_periods_14, result_data
