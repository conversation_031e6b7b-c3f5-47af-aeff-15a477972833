import pandas as pd
from pandas import DataFrame


fileName = '/Users/<USER>/PycharmProjects/fortune_lens/FortuneLens/FL Medicine Use Case v1.xlsx'


def read_excel_sheets(fileName):
    try:
        house_name = pd.read_excel(fileName, sheet_name="Master Lookup", usecols='C:D', skiprows=5, nrows=13)

        Planets_exalt_debilitate: DataFrame = pd.read_excel(
            fileName,
            sheet_name="Master Lookup",
            usecols='F:H',
            skiprows=6,
            nrows=10
        )

        star: DataFrame = pd.read_excel(
            fileName,
            sheet_name="Master Lookup",
            usecols='B:D',
            skiprows=19,
            nrows=27
        )

        planets_friends_neutral_enemies: DataFrame = pd.read_excel(
            fileName,
            sheet_name="Master Lookup",
            usecols='F:I',
            skiprows=20,
            nrows=9
        )

        planets_aspects: DataFrame = pd.read_excel(
            fileName,
            sheet_name="Master Lookup",
            usecols='F:G',
            skiprows=32,
            nrows=7
        )
        user_astro_Basic: DataFrame = pd.read_excel(
            "/Users/<USER>/PycharmProjects/fortune_lens/FortuneLens/user_data.xlsx",
            sheet_name="User Astro Data Master",
            usecols='A:AL',
            # skiprows=2,
            # nrows=4
        )
        user_birth_chart: DataFrame = pd.read_excel(
            "/Users/<USER>/PycharmProjects/fortune_lens/FortuneLens/user_data.xlsx",
            sheet_name="Astro Results",
            usecols='A:BJ',
            # skiprows=,
            # nrows=4
        )

        return {
            "house_name": house_name,
            "Planets_exalt_debilitate": Planets_exalt_debilitate,
            "star": star,
            "planets_friends_neutral_enemies": planets_friends_neutral_enemies,
            "planets_aspects": planets_aspects,
            "user_astro_Basic": user_astro_Basic,
            "user_birth_chart": user_birth_chart
        }
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return {}
