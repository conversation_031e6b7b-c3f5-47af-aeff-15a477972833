import requests
import json

# Base URL
base_url = "http://localhost:5003/api"

# Step 1: Register a test user
def register_user(email, password, name):
    url = f"{base_url}/auth/register"
    data = {
        "email": email,
        "password": password,
        "name": name
    }
    response = requests.post(url, json=data)
    print(f"Register response: {response.status_code}")
    print(response.json())
    return response.json()

# Step 2: Login to get access token
def login(email, password):
    url = f"{base_url}/auth/login"
    data = {
        "email": email,
        "password": password
    }
    response = requests.post(url, json=data)
    print(f"Login response: {response.status_code}")
    print(response.json())
    return response.json().get("access_token")

# Step 3: Create member profiles
def create_member_profile(token, name, gender, relation, birth_date, birth_time, birth_place):
    url = f"{base_url}/member-profiles"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    data = {
        "name": name,
        "gender": gender,
        "relation": relation,
        "birth_date": birth_date,
        "birth_time": birth_time,
        "birth_place": birth_place
    }
    response = requests.post(url, json=data, headers=headers)
    print(f"Create member profile response: {response.status_code}")
    print(response.json())
    return response.json().get("_id")

# Step 4: Test marriage matching
def test_marriage_matching(token, bride_id, groom_id):
    url = f"{base_url}/marriage-matching/lagna"
    headers = {
        "Authorization": f"Bearer {token}"
    }
    data = {
        "bride_id": bride_id,
        "groom_id": groom_id
    }
    response = requests.post(url, json=data, headers=headers)
    print(f"Marriage matching response: {response.status_code}")
    print(json.dumps(response.json(), indent=2))
    return response.json()

# Main function
def main():
    # Register users
    register_user("<EMAIL>", "password123", "Test User")
    
    # Login
    token = login("<EMAIL>", "password123")
    
    if not token:
        print("Failed to get token. Exiting.")
        return
    
    # Create member profiles
    bride_id = create_member_profile(
        token,
        "Jane Doe",
        "female",
        "spouse",
        "1990-05-15",
        "14:30",
        "Chennai, India"
    )
    
    groom_id = create_member_profile(
        token,
        "John Doe",
        "male",
        "self",
        "1988-10-20",
        "10:15",
        "Chennai, India"
    )
    
    if not bride_id or not groom_id:
        print("Failed to create member profiles. Exiting.")
        return
    
    # Test marriage matching
    test_marriage_matching(token, bride_id, groom_id)

if __name__ == "__main__":
    main()
