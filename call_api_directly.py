"""
Call the marriage date prediction API directly.
"""

import sys
import requests
import json
import pymongo
from bson import ObjectId

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Find the member profile
member_profile = db["member_profile"].find_one({"user_profile_id": 17, "member_profile_id": 1})

if not member_profile:
    print("Member profile not found for user_profile_id=17 and member_profile_id=1")
    sys.exit(1)

member_id = str(member_profile["_id"])
print(f"Found member profile with ID: {member_id}")

# Get a token from the token.txt file if it exists
token = None
try:
    with open("token.txt", "r") as f:
        token = f.read().strip()
    print("Found token in token.txt")
except:
    print("No token found in token.txt")
    print("Please login to get a token and save it to token.txt")
    sys.exit(1)

# Call the API
url = "http://localhost:5000/api/marriage-date-prediction"
headers = {
    "Authorization": f"Bearer {token}",
    "Content-Type": "application/json"
}
data = {
    "member_id": member_id,
    "marriage_date": "2023-05-15",
    "print_output": True
}

try:
    response = requests.post(url, headers=headers, json=data)
    
    if response.status_code == 200:
        result = response.json()
        print("\nAPI Response:")
        print(json.dumps(result, indent=2))
    else:
        print(f"API Error: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Error calling API: {str(e)}")
