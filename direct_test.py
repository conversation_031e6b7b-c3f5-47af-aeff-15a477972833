"""
Direct test script for marriage date prediction functionality.
"""

import sys
import os
import json
from datetime import datetime
from bson import ObjectId
import pymongo

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Find the member profile
member_profile = db["member_profile"].find_one({"user_profile_id": 1, "member_profile_id": 1})

if not member_profile:
    print("Member profile not found for user_profile_id=1 and member_profile_id=1")
    sys.exit(1)

member_id = member_profile["_id"]
print(f"Found member profile with ID: {member_id}")

# Get astro data
astro_data = db["user_member_astro_profile_data"].find_one({"member_profile_id": member_id})
if not astro_data:
    print("Astro data not found for the member")
    sys.exit(1)

print(f"Found astro data with ID: {astro_data['_id']}")

# Extract D1 chart (Rasi chart)
d1_chart = astro_data.get('chart_data', {}).get('D1', {})
if not d1_chart:
    print("D1 chart data not found for the member")
    sys.exit(1)

print("D1 chart data found")

# Extract planet positions and house names
planet_positions = {}
house_names = {}
nakshatra_data = {}

# Extract from houses data
houses = d1_chart.get('houses', [])
for house in houses:
    house_number = house.get('house_number')
    house_name = house.get('house_name', '').upper()
    planets = house.get('planets', [])
    planet_nakshatras = house.get('planet_nakshatras', {})

    if house_number and planets:
        planet_positions[str(house_number)] = ', '.join(planets)

    if house_number and house_name:
        house_names[f'house_name_{house_number}'] = house_name

    for planet in planets:
        if planet in planet_nakshatras:
            nakshatra_data[f'{planet}_star'] = planet_nakshatras[planet]

# Add lagna position
lagna_house = d1_chart.get('lagna', {}).get('house_number')
if lagna_house:
    if str(lagna_house) in planet_positions:
        planet_positions[str(lagna_house)] = f"lagnam, {planet_positions[str(lagna_house)]}"
    else:
        planet_positions[str(lagna_house)] = "lagnam"

# Get dasha data
dasha_data = astro_data.get('chart_data', {}).get('D1', {}).get('dashas', {})
print(f"Dasha data: {dasha_data}")

# Get birth date
birth_date = member_profile.get('birth_date')
if not birth_date:
    print("Birth date not found for the member")
    sys.exit(1)

print(f"Birth date: {birth_date}")

# Create placements object
placements = {
    'planet_house': planet_positions,
    'house_name': house_names,
    'star': nakshatra_data,
    'dhasa': dasha_data
}

# Print planet positions
print("\nPlanet positions:")
for house_num, planets in planet_positions.items():
    print(f"House {house_num}: {planets}")

# Print house names
print("\nHouse names:")
for key, name in house_names.items():
    print(f"{key}: {name}")

# Print nakshatra data
print("\nNakshatra data:")
for planet, star in nakshatra_data.items():
    print(f"{planet}: {star}")

# Import the m_rule_one function from date_prediction.py
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
from fortune_lens.app.services.marriage_matching.date_prediction import m_rule_one

# Call m_rule_one directly
print("\nCalling m_rule_one function...")
marriage_date = '2023-05-15'

try:
    rule_results, jupiter_aspects_houses, jupiter_aspects_rasi, venus_aspects_houses, venus_aspects_rasi, potential_periods = m_rule_one(
        placements, birth_date, marriage_date, str(member_id), d1_chart
    )

    # Print the detailed output
    print("\nDetailed Output:")
    print(rule_results)
    print("Matching Dasha Periods:")
    print("\nrule 1.1\n")
    print("Jupiter 5th Aspect Periods:")
    for period in jupiter_aspects_houses[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in jupiter_aspects_houses[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in jupiter_aspects_houses[9]:
        print(period)

    print("\nJupiter Staying with 2th,7th house Periods:")
    for period in jupiter_aspects_houses['stay']:
        print(period)

    print("\nrule 1.2\n")
    print("Jupiter 5th Aspect Periods:")
    for period in jupiter_aspects_rasi[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in jupiter_aspects_rasi[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in jupiter_aspects_rasi[9]:
        print(period)

    print("\nJupiter Staying with rasi Periods:")
    for period in jupiter_aspects_rasi['stay']:
        print(period)

    print("\nrule 1.3\n")

    print("\nVenus 7th Aspect Periods:")
    for period in venus_aspects_houses[7]:
        print(period)
    print("\nVenus Staying with 7th house Periods:")
    for period in venus_aspects_houses['stay']:
        print(period)

    print("\nrule 1.4\n")

    print("\nVenus 7th Aspect Periods:")
    for period in venus_aspects_rasi[7]:
        print(period)

    print("\nVenus Staying with rasi Periods:")
    for period in venus_aspects_rasi['stay']:
        print(period)

    print("\nCustomer Output")
    for r in potential_periods:
        print(r)
except Exception as e:
    import traceback
    print(f"Error calling m_rule_one: {str(e)}")
    print(traceback.format_exc())
