{"id": "fortune-lens-environment", "name": "Fortune Lens Environment", "values": [{"key": "base_url", "value": "http://localhost:5000/api", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "user_profile_id", "value": "", "type": "default", "enabled": true}, {"key": "member_profile_id", "value": "", "type": "default", "enabled": true}, {"key": "bride_profile_id", "value": "", "type": "default", "enabled": true}, {"key": "groom_profile_id", "value": "", "type": "default", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2023-06-01T12:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}