from data_loading import *
# from medical_profession import *
from foreign_travel_main_function import *
import pandas as pd

dataframes = read_excel_sheets(fileName)


def filter_by_start_foreign(dataframe):
    """
    Filters the dataframe to include only rows where the user_profession matches the specified profession.

    Parameters:
        dataframe (pd.DataFrame): The dataframe containing user data.
        profession (str): The profession to filter by.

    Returns:

        pd.DataFrame: A filtered dataframe with only the specified profession.
    """

    filtered_df = dataframe[dataframe['User_Data_Label_3 (Foreign / Domestic)'].str.startswith('Foreign', na=False)]
    # print(filtered_df)

    return filtered_df

    # Example usage


user_input = input("Enter the user_id or 'ALL': ")

if user_input.upper() == "ALL":
    # print(dataframes)
    all_placements = filter_by_start_foreign(dataframes['user_astro_Basic'])
    user_data = []
    over_all_data = []
    rule_all_details = []

    for index, row in all_placements.iterrows():
        user_id = row['user_id']
        rule_check = {}
        # ru_ch = []
        placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
        # all_placements = dataframes['user_birth_chart']

        print("Lagna style")
        dob = get_dob(dataframes['user_astro_Basic'], user_id, "user_birthdate")
        foreign_travel_date1 = get_dob(dataframes['user_astro_Basic'], user_id,
                                       'User_Data_Label_4 (Foreign Travel Date1)')
        # print(marriage_date)
        # print(dob)
        # print("Lagna style")
        ascendant = placements[0]['house_name']['house_name_1']
        house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
        house_planet_mapping = placements[0]['planet_house']
        file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
        df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
        re, rule_fi = rule_one(placements, dataframes, df, dob, foreign_travel_date1, user_id)
        user_data.append(rule_fi)
        # print(matching_periods)
        # print(user_data)

    df_ordered = pd.DataFrame(user_data)
    print(df_ordered)
    # rule_points = {'Rule 1.1': 10, 'Rule 1.2': 5, 'Rule 1.3': 5}
    #
    # # Calculate total points for each user
    # df_ordered['total_points'] = (
    #         df_ordered['Rule 1.1'] * rule_points['Rule 1.1'] +
    #         df_ordered['Rule 1.2'] * rule_points['Rule 1.2'] +
    #         df_ordered['Rule 1.3'] * rule_points['Rule 1.3']
    # )
    # Specify the output Excel file path
    file_path = "Foreign_rule_prediction.xlsx"

    # Write only ordered_data to Excel
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df_ordered.to_excel(writer, sheet_name='Result_Data', index=False)

else:
    rule_check = {}
    user_id = int(user_input)
    placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
    # all_placements = dataframes['user_birth_chart']

    user_data = []
    over_all_data = []
    rule_all_details = []

    print("Lagna style")
    dob = get_dob(dataframes['user_astro_Basic'], user_id, "user_birthdate")
    # user_data = dataframes['user_astro_Basic'][dataframes['user_astro_Basic']['user_id'] == user_id]
    # print(user_data)
    foreign_travel_date1 = get_dob(dataframes['user_astro_Basic'], user_id, 'User_Data_Label_4 (Foreign Travel Date1)')
    # print(marriage_date)
    # print(dob)
    # print("Lagna style")
    ascendant = placements[0]['house_name']['house_name_1']
    house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
    house_planet_mapping = placements[0]['planet_house']
    file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
    df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
    re, fi = rule_one(placements, dataframes, df, dob, foreign_travel_date1, user_id)
    user_data.append(re)
    # print(matching_periods)
    # print(user_data)

    df_ordered = pd.DataFrame(user_data)
    rule_points = {'Rule 1.1': 10, 'Rule 1.2': 5, 'Rule 1.3': 5}

    # Calculate total points for ea
