# Fortune Lens Postman Collection Usage Guide

This guide explains how to use the Fortune Lens Postman collection to test all API endpoints in the application.

## Setup

1. **Import the Collection and Environment**
   - Open Postman
   - Click "Import" in the top left corner
   - Select the `fortune_lens_postman_collection.json` and `fortune_lens_postman_environment.json` files
   - Both the collection and environment should now be available in Postman

2. **Select the Environment**
   - In the top right corner of Postman, select "Fortune Lens Environment" from the dropdown

3. **Configure the Base URL**
   - By default, the base URL is set to `http://localhost:5000/api`
   - If your API is running on a different host or port, update the `base_url` variable in the environment settings

## Testing Workflow

Follow this workflow to test the API endpoints in a logical sequence:

### 1. Authentication

1. **Register a User**
   - Use the "Register User" request
   - Fill in the required fields (email, password, name, mobile)
   - If OTP verification is enabled, you'll receive an OTP

2. **Verify OTP (if required)**
   - Use the "Verify OTP" request
   - Enter the email and OTP received

3. **Login**
   - Use the "Login" request
   - Enter the email and password
   - The access and refresh tokens will be automatically saved to environment variables

4. **Get Current User**
   - Use the "Get Current User" request to verify authentication
   - This will show your user profile information

### 2. Member Profiles

1. **Create Member Profiles**
   - Create at least two member profiles (one male, one female) for testing marriage matching
   - Use the "Create Member Profile" or "Create Member Profile with Charts" request
   - Save the returned profile IDs to environment variables:
     - Set `member_profile_id` for general testing
     - Set `bride_profile_id` and `groom_profile_id` for marriage matching

2. **Get Member Profiles**
   - Use the "Get All Member Profiles" request to see all your profiles
   - Use the "Get Member Profile by ID" request to view a specific profile

### 3. Charts

1. **Get Available Chart Types**
   - Use the "Get Available Chart Types" request to see all available chart types

2. **Generate Charts**
   - Use the "Generate All Charts" request to generate all 23 divisional charts
   - Use the "Generate Divisional Chart" request to generate a specific chart (e.g., D9 Navamsa)

### 4. Marriage Matching

1. **Analyze Marriage Compatibility**
   - Use the "Analyze Marriage Compatibility" request
   - Make sure `bride_profile_id` and `groom_profile_id` environment variables are set

2. **Get Marriage Compatibility**
   - Use the "Get Marriage Compatibility" request to retrieve compatibility analysis

### 5. Marriage Date Prediction

1. **Predict Marriage Date**
   - Use the "Predict Marriage Date" request
   - Make sure `member_profile_id` environment variable is set

2. **Get Marriage Date Prediction**
   - Use the "Get Marriage Date Prediction" request to retrieve prediction results

### 6. Daily Panchanga

1. **Get Daily Panchanga**
   - Use the "Get Daily Panchanga" request
   - Adjust date and location parameters as needed

2. **Get Monthly Panchanga**
   - Use the "Get Monthly Panchanga" request
   - Adjust year, month, and location parameters as needed

3. **Get Festival Dates**
   - Use the "Get Festival Dates" request
   - Adjust year and location parameters as needed

### 7. Career Prediction

1. **Predict Medical Profession**
   - Use the "Predict Medical Profession" request
   - Make sure `user_profile_id` and `member_profile_id` environment variables are set

## Automated Testing

The collection includes test scripts that automatically:

1. Extract and save tokens from login responses
2. Validate response formats
3. Check for expected status codes

## Environment Variables

The following environment variables are used:

| Variable | Description |
|----------|-------------|
| `base_url` | Base URL of the API |
| `access_token` | JWT access token (set automatically after login) |
| `refresh_token` | JWT refresh token (set automatically after login) |
| `user_id` | User ID for testing |
| `user_profile_id` | User profile ID for testing |
| `member_profile_id` | Member profile ID for testing |
| `bride_profile_id` | Bride's profile ID for marriage matching |
| `groom_profile_id` | Groom's profile ID for marriage matching |

## Tips for Effective Testing

1. **Clear Database Before Testing**
   - For clean testing, consider clearing your database collections before starting
   - Create fresh test data through the API

2. **Test Complete Workflows**
   - Test complete workflows from user registration to chart generation to marriage matching
   - This ensures all components work together correctly

3. **Save Response Data**
   - Use the "Tests" tab in Postman to save important data from responses
   - Example script to save a profile ID:
     ```javascript
     var jsonData = pm.response.json();
     if (jsonData.profile && jsonData.profile._id) {
         pm.environment.set("member_profile_id", jsonData.profile._id);
     }
     ```

4. **Validate Responses**
   - Check that responses match expected formats
   - Verify that error handling works correctly by testing invalid inputs

## Troubleshooting

- **Authentication Issues**: If you get 401 errors, your token may have expired. Try logging in again.
- **Missing Data**: If endpoints return 404 errors, check that you've created the necessary data first.
- **Invalid Input**: If you get 400 errors, check your request body against the API documentation.
