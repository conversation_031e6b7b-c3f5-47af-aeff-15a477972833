# Fortune Lens API Endpoints

This document provides a comprehensive list of all API endpoints available in the Fortune Lens application.

## Table of Contents

- [Authentication](#authentication)
- [User Management](#user-management)
- [Member Profiles](#member-profiles)
- [Charts](#charts)
- [Marriage Matching](#marriage-matching)
- [Tamil Panchanga](#tamil-panchanga)

## Authentication

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| POST | `/auth/register` | Register a new user (with OTP verification) | No |
| POST | `/auth/login` | Login and get access token | No |
| POST | `/auth/refresh` | Refresh access token | Yes (Refresh Token) |
| GET | `/auth/me` | Get current user information | Yes |

## User Management

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| GET | `/users` | Get list of users | Yes |
| GET | `/users/<user_id>` | Get user by ID | Yes |
| PUT | `/users/<user_id>` | Update user | Yes |
| DELETE | `/users/<user_id>` | Delete user | Yes |

## Member Profiles

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| POST | `/member-profiles` | Create a member profile | Yes |
| GET | `/member-profiles` | Get current user's member profiles | Yes |
| GET | `/member-profiles/<profile_id>` | Get member profile by ID | Yes |
| PUT | `/member-profiles/<profile_id>` | Update member profile | Yes |
| DELETE | `/member-profiles/<profile_id>` | Delete member profile | Yes |
| GET | `/member-profiles/self` | Get or create user's own profile as a member profile | Yes |
| POST | `/member-profiles/with-charts` | Create a member profile with automatic chart generation | Yes |
| GET | `/member-profiles/<profile_id>/charts` | Get member profile with all chart data | Yes |

## Charts

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| GET | `/charts/types` | Get available chart types | No |
| POST | `/charts/generate` | Generate all 23 astrological divisional charts | Yes |
| POST | `/charts/divisional` | Generate a specific divisional chart | Yes |

## Marriage Matching

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| POST | `/marriage-matching` | Analyze marriage compatibility between two members | Yes |
| GET | `/marriage-matching/<bride_id>/<groom_id>` | Get marriage compatibility analysis between two members | Yes |

---

# API Request and Response Examples

## Authentication

### Register a User

**Step 1: Send Registration Data and Receive OTP**

**Request:**
```json
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "John Doe",
  "mobile": "1234567890"
}
```

**Response:**
```json
{
  "message": "OTP sent successfully",
  "email": "<EMAIL>",
  "expires_at": "2023-06-01T12:10:00Z",
  "registration_data": {
    "email": "<EMAIL>",
    "name": "John Doe",
    "mobile": "1234567890"
  }
}
```

**Step 2: Verify OTP and Complete Registration**

**Request:**
```json
POST /auth/register
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "name": "John Doe",
  "mobile": "1234567890",
  "otp": "123456"
}
```

**Response:**
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token"
}
```

### Login

**Request:**
```json
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "John Doe"
  },
  "access_token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token"
}
```

### Get Current User

**Request:**
```
GET /auth/me
Authorization: Bearer jwt_access_token
```

**Response:**
```json
{
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "name": "John Doe",
    "mobile": "1234567890"
  }
}
```

## Member Profiles

### Create Member Profile

**Request:**
```json
POST /member-profiles
Authorization: Bearer jwt_access_token
{
  "name": "Jane Doe",
  "relation": "spouse",
  "birth_date": "1990-01-01",
  "birth_time": "10:30:00",
  "birth_place": "Chennai",
  "latitude": 13.0827,
  "longitude": 80.2707,
  "gender": "female"
}
```

**Response:**
```json
{
  "message": "Member profile created successfully",
  "profile": {
    "_id": "profile_id",
    "user_id": "user_id",
    "name": "Jane Doe",
    "relation": "spouse",
    "birth_date": "1990-01-01",
    "birth_time": "10:30:00",
    "birth_place": "Chennai",
    "latitude": 13.0827,
    "longitude": 80.2707,
    "gender": "female",
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  }
}
```

### Create Member Profile with Charts

**Request:**
```json
POST /member-profiles/with-charts
Authorization: Bearer jwt_access_token
{
  "member_name": "Jane Doe",
  "member_gender": "female",
  "member_relation": "spouse",
  "user_birthdate": "01-01-1990",
  "user_birthtime": "10:30:00 AM",
  "user_birthplace": "Chennai",
  "user_state": "Tamil Nadu",
  "user_country": "India"
}
```

**Response:**
```json
{
  "message": "Member profile and astrological data created successfully",
  "profile": {
    "_id": "profile_id",
    "user_id": "user_id",
    "member_name": "Jane Doe",
    "member_gender": "female",
    "member_relation": "spouse",
    "user_birthdate": "01-01-1990",
    "user_birthtime": "10:30:00 AM",
    "user_birthplace": "Chennai",
    "user_state": "Tamil Nadu",
    "user_country": "India",
    "created_at": "2023-06-01T12:00:00Z",
    "updated_at": "2023-06-01T12:00:00Z"
  },
  "profile_id": "profile_id"
}
```

### Get Member Profile with Charts

**Request:**
```
GET /member-profiles/profile_id/charts
Authorization: Bearer jwt_access_token
```

**Response:**
```json
{
  "message": "Member profile and astrological data retrieved successfully",
  "data": {
    "member_profile": {
      "_id": "profile_id",
      "user_id": "user_id",
      "member_name": "Jane Doe",
      "member_gender": "female",
      "member_relation": "spouse",
      "user_birthdate": "01-01-1990",
      "user_birthtime": "10:30:00 AM",
      "user_birthplace": "Chennai",
      "user_state": "Tamil Nadu",
      "user_country": "India",
      "created_at": "2023-06-01T12:00:00Z",
      "updated_at": "2023-06-01T12:00:00Z"
    },
    "chart_data": {
      "D1": {
        "chart_info": {
          "name": "Rasi Chart",
          "description": "Basic birth chart showing planetary positions at birth",
          "divisional_factor": 1
        },
        "houses": [...],
        "lagna": {
          "sign": "Thulam",
          "degree": 27.8639,
          "position_precise": "27 Li 51' 50.28\"",
          "minutes_seconds": "51' 50.28\""
        },
        "planets_precise": {...},
        "dashas": {...}
      },
      "D2": {...},
      // Other divisional charts...
    }
  }
}
```

## Charts

### Get Chart Types

**Request:**
```
GET /charts/types
```

**Response:**
```json
{
  "success": true,
  "chart_types": {
    "D1": "Rasi Chart",
    "D2": "Hora Chart",
    "D3": "Drekkana Chart",
    // All 23 chart types
  },
  "chart_formats": [
    "json",
    "svg",
    "png"
  ]
}
```

### Generate All Charts

**Request:**
```json
POST /charts/generate
Authorization: Bearer jwt_access_token
{
  "user_birthdate": "01-01-1990",
  "user_birthtime": "10:30:00 AM",
  "user_birthplace": "Chennai",
  "user_state": "Tamil Nadu",
  "user_country": "India"
}
```

**Response:**
```json
{
  "success": true,
  "chart_data": {
    "D1": {...},
    "D2": {...},
    // All 23 divisional charts
  },
  "chart_names": {
    "D1": "Rasi Chart",
    "D2": "Hora Chart",
    // Names of all 23 divisional charts
  }
}
```

### Generate Specific Divisional Chart

**Request:**
```json
POST /charts/divisional
Authorization: Bearer jwt_access_token
{
  "user_birthdate": "01-01-1990",
  "user_birthtime": "10:30:00 AM",
  "user_birthplace": "Chennai",
  "user_state": "Tamil Nadu",
  "user_country": "India",
  "chart_type": 9
}
```

**Response:**
```json
{
  "success": true,
  "chart_data": {
    "chart_info": {
      "name": "Navamsa Chart",
      "description": "Chart of spouse and general fortune",
      "divisional_factor": 9
    },
    "houses": [...],
    "lagna": {...},
    "planets_precise": {...}
  }
}
```

## Tamil Panchanga

| Method | Endpoint | Description | Authentication Required |
|--------|----------|-------------|------------------------|
| GET | `/panchanga/daily` | Get daily panchanga for a specific date | No |
| GET | `/panchanga/monthly` | Get panchanga for an entire month | No |
| GET | `/panchanga/festivals` | Get Tamil festival dates for a year | No |
| GET | `/panchanga/tamil-date` | Convert Gregorian date to Tamil date | No |

### Get Daily Panchanga

**Request:**
```
GET /panchanga/daily?date=2023-06-01&lat=13.0878&lon=80.2785&tz=5.5
```

**Response:**
```json
{
  "success": true,
  "data": {
    "date": {
      "gregorian": "2023-06-01",
      "tamil_month": 1,
      "tamil_month_name": "Vaikasi",
      "tamil_day": 18,
      "weekday": 4,
      "weekday_name": "Viyazhan"
    },
    "timings": {
      "sunrise": "05:45:12",
      "sunset": "18:32:45",
      "raahu_kaalam": {
        "start": "13:30:00",
        "end": "15:00:00"
      },
      "yamagandam": {
        "start": "06:00:00",
        "end": "07:30:00"
      },
      "gulikai": {
        "start": "09:00:00",
        "end": "10:30:00"
      },
      "abhijit_muhurta": {
        "start": "11:45:00",
        "end": "12:30:00"
      },
      "durmuhurtam": [
        {
          "start": "08:15:00",
          "end": "09:00:00"
        },
        {
          "start": "12:30:00",
          "end": "13:15:00"
        }
      ]
    },
    "panchanga": {
      "tithi": {
        "number": 12,
        "name": "Dvadasi",
        "end_time": "20:15:30"
      },
      "nakshatra": {
        "number": 7,
        "name": "Punarpoosam",
        "pada": 2,
        "end_time": "22:30:15"
      },
      "yoga": {
        "number": 15,
        "name": "Vajra",
        "end_time": "18:45:20"
      },
      "karana": {
        "number": 23,
        "name": "Bava"
      }
    },
    "special_observances": [
      {
        "name": "Ekadashi",
        "type": "fasting",
        "description": "Fasting day dedicated to Lord Vishnu"
      }
    ]
  }
}
```

### Get Monthly Panchanga

**Request:**
```
GET /panchanga/monthly?year=2023&month=6&lat=13.0878&lon=80.2785&tz=5.5
```

**Response:**
```json
{
  "success": true,
  "data": {
    "year": 2023,
    "month": 6,
    "days": [
      // Daily panchanga for each day in the month
      // (Same format as daily panchanga response)
    ]
  }
}
```

### Get Festival Dates

**Request:**
```
GET /panchanga/festivals?year=2023&lat=13.0878&lon=80.2785&tz=5.5
```

**Response:**
```json
{
  "success": true,
  "data": {
    "year": 2023,
    "festivals": [
      {
        "name": "Tamil New Year (Puthandu)",
        "date": "2023-04-14",
        "description": "First day of Tamil month Chithirai",
        "tamil_month": 0,
        "tamil_day": 1
      },
      {
        "name": "Pongal",
        "date": "2023-01-14",
        "description": "Harvest festival celebrated on the first day of Thai",
        "tamil_month": 9,
        "tamil_day": 1
      },
      // Other festivals
    ]
  }
}
```

### Get Tamil Date

**Request:**
```
GET /panchanga/tamil-date?date=2023-06-01&lat=13.0878&lon=80.2785&tz=5.5
```

**Response:**
```json
{
  "success": true,
  "data": {
    "tamil_month": 1,
    "tamil_month_name": "Vaikasi",
    "tamil_day": 18,
    "days_in_month": 31,
    "gregorian_date": "2023-06-01"
  }
}
```