"""
Check user_profile_id = 1 and member_profile_id = 1 in MongoDB.
"""

import sys
import pymongo
from bson import ObjectId
import json

# Connect directly to MongoDB
client = pymongo.MongoClient("mongodb://localhost:27017/")
db = client["fortune_lens"]

# Find the member profile
member_profile = db["member_profile"].find_one({"user_profile_id": 1, "member_profile_id": 1})

if not member_profile:
    print("Member profile not found for user_profile_id=1 and member_profile_id=1")
    sys.exit(1)

member_id = member_profile["_id"]
print(f"Found member profile with ID: {member_id}")
print(f"Member profile data: {json.dumps(member_profile, default=str, indent=2)}")

# Get astro data
astro_data = db["user_member_astro_profile_data"].find_one({"member_profile_id": member_id})
if not astro_data:
    print("\nAstro data not found for the member")
    
    # Check if there's any astro data for user_profile_id = 1
    user_profile = db["user_profile"].find_one({"user_profile_id": 1})
    if user_profile:
        user_id = user_profile["_id"]
        print(f"Found user profile with ID: {user_id}")
        
        # Find all members for this user
        members = list(db["member_profile"].find({"user_profile_id": 1}))
        print(f"Found {len(members)} members for user_profile_id=1")
        
        for member in members:
            print(f"Member ID: {member['_id']}, member_profile_id: {member.get('member_profile_id')}")
            
            # Check if there's astro data for this member
            astro = db["user_member_astro_profile_data"].find_one({"member_profile_id": member["_id"]})
            if astro:
                print(f"  Found astro data with ID: {astro['_id']}")
                
                # Check if D1 chart and dasha data exist
                d1_chart = astro.get('chart_data', {}).get('D1', {})
                dasha_data = astro.get('chart_data', {}).get('dashas', {})
                
                if d1_chart:
                    print("  D1 chart data found")
                else:
                    print("  D1 chart data not found")
                    
                if dasha_data:
                    print("  Dasha data found")
                else:
                    print("  Dasha data not found")
            else:
                print("  No astro data found for this member")
    
    # Create sample astro data for the member
    print("\nCreating sample astro data for the member...")
    sample_chart_data = {
        'D1': {
            'chart_info': {
                'name': 'Rasi Chart',
                'description': 'Basic birth chart showing planetary positions at birth',
                'divisional_factor': 1
            },
            'houses': [
                {
                    'house_number': 1,
                    'house_name': 'MESHAM',
                    'planets': ['sun', 'mercury'],
                    'planet_degrees': {'sun': '16°30\'', 'mercury': '12°15\''},
                    'planet_nakshatras': {'sun': 'BARANI', 'mercury': 'ASHWINI'}
                },
                {
                    'house_number': 2,
                    'house_name': 'RISHABAM',
                    'planets': ['venus'],
                    'planet_degrees': {'venus': '25°45\''},
                    'planet_nakshatras': {'venus': 'ROHINI'}
                },
                {
                    'house_number': 3,
                    'house_name': 'MIDUNAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 4,
                    'house_name': 'KADAGAM',
                    'planets': ['moon'],
                    'planet_degrees': {'moon': '25°45\''},
                    'planet_nakshatras': {'moon': 'ASHLESHA'}
                },
                {
                    'house_number': 5,
                    'house_name': 'SIMMAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 6,
                    'house_name': 'KANNI',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 7,
                    'house_name': 'THULAM',
                    'planets': ['saturn'],
                    'planet_degrees': {'saturn': '10°20\''},
                    'planet_nakshatras': {'saturn': 'CHITHIRAI'}
                },
                {
                    'house_number': 8,
                    'house_name': 'VRICHIGAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 9,
                    'house_name': 'DHANUSU',
                    'planets': ['jupiter'],
                    'planet_degrees': {'jupiter': '5°15\''},
                    'planet_nakshatras': {'jupiter': 'MOOLAM'}
                },
                {
                    'house_number': 10,
                    'house_name': 'MAGARAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 11,
                    'house_name': 'KUMBAM',
                    'planets': ['mars'],
                    'planet_degrees': {'mars': '18°30\''},
                    'planet_nakshatras': {'mars': 'SADAYAM'}
                },
                {
                    'house_number': 12,
                    'house_name': 'MEENAM',
                    'planets': ['rahu'],
                    'planet_degrees': {'rahu': '2°10\''},
                    'planet_nakshatras': {'rahu': 'POORATADHI'}
                }
            ],
            'lagna': {
                'sign': 'MESHAM',
                'degree': 15.5,
                'house_number': 1
            },
            'dashas': {
                'maha_dhasa_period': [
                    {
                        'period_name': 'VENUS-JUPITER',
                        'start_date': '2023-01-01 00:00:00 AM',
                        'end_date': '2025-12-31 11:59:59 PM'
                    }
                ],
                'antara_dhasa_period': [
                    {
                        'period_name': 'VENUS-JUPITER',
                        'start_date': '2023-01-01 00:00:00 AM',
                        'end_date': '2025-12-31 11:59:59 PM'
                    }
                ]
            }
        }
    }
    
    new_astro_data = {
        "member_profile_id": member_id,
        "user_profile_id": member_profile.get("user_profile_id"),
        "name": member_profile.get("name", "Unknown"),
        "gender": member_profile.get("gender", "Unknown"),
        "birth_date": member_profile.get("birth_date", "1990-01-01"),
        "birth_time": member_profile.get("birth_time", "12:00:00"),
        "birth_place": member_profile.get("birth_place", "Chennai, India"),
        "chart_data": sample_chart_data
    }
    
    result = db["user_member_astro_profile_data"].insert_one(new_astro_data)
    print(f"Created sample astro data with ID: {result.inserted_id}")
    
    # Get the newly created astro data
    astro_data = db["user_member_astro_profile_data"].find_one({"_id": result.inserted_id})
else:
    print(f"\nFound astro data with ID: {astro_data['_id']}")
    
    # Check if D1 chart and dasha data exist
    d1_chart = astro_data.get('chart_data', {}).get('D1', {})
    dasha_data = astro_data.get('chart_data', {}).get('dashas', {})
    
    if d1_chart:
        print("D1 chart data found")
        
        # Print houses information
        houses = d1_chart.get('houses', [])
        print("\nHouses information:")
        for house in houses:
            house_number = house.get('house_number')
            house_name = house.get('house_name')
            planets = house.get('planets', [])
            print(f"House {house_number} ({house_name}): Planets = {', '.join(planets)}")
    else:
        print("D1 chart data not found")
        
    if dasha_data:
        print("\nDasha data found:")
        print(json.dumps(dasha_data, default=str, indent=2))
    else:
        print("\nDasha data not found")

print("\nNow you can run the marriage date prediction API with this data.")
