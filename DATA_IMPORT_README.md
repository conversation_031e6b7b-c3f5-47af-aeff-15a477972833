# Fortune Lens Data Import

This directory contains scripts to import data from Excel to MongoDB for the Fortune Lens application.

## Project Structure

```
fortune_lens/
├── data/                      # Data files
│   └── user_data_1.xlsx       # Excel file with user data
├── scripts/                   # Scripts package
│   ├── data_import/           # Data import scripts
│   │   ├── __init__.py
│   │   └── import_excel_to_mongodb.py  # Script to import data from Excel to MongoDB
│   └── utils/                 # Utility scripts
│       ├── __init__.py
│       ├── check_database.py  # Script to check the database content
│       └── clear_database.py  # Script to clear the database
├── import_data.py             # Main script to import data
└── DATA_IMPORT_README.md      # This file
```

## Usage

### Main Script

The main script `import_data.py` provides a command-line interface to:

1. Clear the database
2. Import data from Excel to MongoDB
3. Check the database content

```bash
# Show help
python import_data.py

# Clear the database
python import_data.py --clear

# Import data from Excel to MongoDB
python import_data.py --import

# Check the database content
python import_data.py --check

# Perform all operations
python import_data.py --clear --import --check
```

### Individual Scripts

You can also run the individual scripts directly:

```bash
# Clear the database
python -m scripts.utils.clear_database

# Import data from Excel to MongoDB
python -m scripts.data_import.import_excel_to_mongodb

# Check the database content
python -m scripts.utils.check_database
```

## Data Structure

The import process creates the following collections in MongoDB:

1. **user_profile**: Contains user profiles with the following fields:
   - user_profile_id: Unique identifier for the user
   - email: User's email address
   - password: Hashed password
   - name: User's name
   - mobile: User's mobile number
   - unique_key: Unique key for the user
   - created_at: Timestamp when the user was created
   - updated_at: Timestamp when the user was last updated

2. **member_profile**: Contains member profiles with the following fields:
   - member_profile_id: Unique identifier for the member
   - user_profile_id: Reference to the user profile
   - unique_key: Unique key for the member
   - name: Member's name
   - relation: Relationship to the user (self, member_2, member_3, etc.)
   - birth_date: Member's birth date
   - birth_time: Member's birth time
   - birth_place: Member's birth place
   - state: Member's state
   - country: Member's country
   - latitude: Latitude of the birth place
   - longitude: Longitude of the birth place
   - gender: Member's gender
   - created_at: Timestamp when the member was created
   - updated_at: Timestamp when the member was last updated

3. **user_member_astro_profile_data**: Contains astrological data for each member with the following fields:
   - member_profile_id: Reference to the member profile
   - user_profile_id: Reference to the user profile
   - unique_key: Unique key for the astro profile
   - name: Member's name
   - relation: Relationship to the user
   - gender: Member's gender
   - birth_date: Member's birth date
   - birth_time: Member's birth time
   - birth_place: Member's birth place
   - state: Member's state
   - country: Member's country
   - latitude: Latitude of the birth place
   - longitude: Longitude of the birth place
   - created_at: Timestamp when the astro profile was created
   - updated_at: Timestamp when the astro profile was last updated
   - astro_data: Additional astrological data
   - chart_data: Astrological chart data

## Excel File Structure

The Excel file `user_data_1.xlsx` contains the following sheets:

1. **User Astro Basic**: Contains basic user and member data with the following columns:
   - user_id: Unique identifier for the user
   - MEMBER_ID: Member identifier (not used)
   - user_name: User's name
   - user_birthdate: User's birth date
   - user_birthtime: User's birth time
   - user_birthplace: User's birth place
   - user_gender: User's gender
   - user_profession: User's profession
   - User_Data_Label_1 to User_Data_Label_5: Additional user data

2. **Astro Results**: Contains astrological data for each user with various columns for houses, planets, stars, etc.

## Notes

- The import process creates user profiles with sequential IDs (1, 2, 3, etc.)
- For each user, it creates member profiles based on the actual rows in the Excel file
- The first member for each user has the relation "self"
- Additional members have relations "member_2", "member_3", etc.
- The import process generates astrological chart data for each member using the `generate_chart()` function
