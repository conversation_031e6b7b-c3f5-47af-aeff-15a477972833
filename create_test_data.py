import pymongo
from bson import ObjectId
from datetime import datetime

# Connect to MongoDB
client = pymongo.MongoClient('mongodb://localhost:27017/')
db = client['fortune_lens']

# Get existing member profiles
female_member = db.member_profile.find_one({'gender': 'female'})
male_member = db.member_profile.find_one({'gender': 'male'})

if not female_member or not male_member:
    print("Could not find both male and female members")
    exit(1)

# Create sample chart data
def create_chart_data():
    return {
        'D1': {
            'chart_info': {
                'name': 'Rasi Chart',
                'description': 'Basic birth chart showing planetary positions at birth',
                'divisional_factor': 1
            },
            'houses': [
                {
                    'house_number': 1,
                    'house_name': 'MESHAM',
                    'planets': ['sun', 'mercury'],
                    'planet_degrees': {'sun': '16°30\'', 'mercury': '12°15\''},
                    'planet_nakshatras': {'sun': 'BARANI', 'mercury': 'ASHWINI'}
                },
                {
                    'house_number': 2,
                    'house_name': 'RISHABAM',
                    'planets': ['venus'],
                    'planet_degrees': {'venus': '25°45\''},
                    'planet_nakshatras': {'venus': 'ROHINI'}
                },
                {
                    'house_number': 3,
                    'house_name': 'MIDUNAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 4,
                    'house_name': 'KADAGAM',
                    'planets': ['moon'],
                    'planet_degrees': {'moon': '25°45\''},
                    'planet_nakshatras': {'moon': 'ASHLESHA'}
                },
                {
                    'house_number': 5,
                    'house_name': 'SIMMAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 6,
                    'house_name': 'KANNI',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 7,
                    'house_name': 'THULAM',
                    'planets': ['saturn'],
                    'planet_degrees': {'saturn': '10°20\''},
                    'planet_nakshatras': {'saturn': 'CHITHIRAI'}
                },
                {
                    'house_number': 8,
                    'house_name': 'VRICHIGAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 9,
                    'house_name': 'DHANUSU',
                    'planets': ['jupiter'],
                    'planet_degrees': {'jupiter': '5°15\''},
                    'planet_nakshatras': {'jupiter': 'MOOLAM'}
                },
                {
                    'house_number': 10,
                    'house_name': 'MAGARAM',
                    'planets': [],
                    'planet_degrees': {},
                    'planet_nakshatras': {}
                },
                {
                    'house_number': 11,
                    'house_name': 'KUMBAM',
                    'planets': ['mars'],
                    'planet_degrees': {'mars': '18°30\''},
                    'planet_nakshatras': {'mars': 'SADAYAM'}
                },
                {
                    'house_number': 12,
                    'house_name': 'MEENAM',
                    'planets': ['rahu'],
                    'planet_degrees': {'rahu': '2°10\''},
                    'planet_nakshatras': {'rahu': 'POORATADHI'}
                }
            ],
            'lagna': {
                'sign': 'MESHAM',
                'degree': 15.5,
                'house_number': 1
            },
            'dashas': {
                'maha_dhasa_period': [
                    {
                        'period_name': 'VENUS-JUPITER',
                        'start_date': '2023-01-01 00:00:00 AM',
                        'end_date': '2025-12-31 11:59:59 PM'
                    }
                ],
                'antara_dhasa_period': [
                    {
                        'period_name': 'VENUS-JUPITER',
                        'start_date': '2023-01-01 00:00:00 AM',
                        'end_date': '2025-12-31 11:59:59 PM'
                    }
                ]
            }
        }
    }

# Create female astro data
female_astro_data = {
    '_id': ObjectId(),
    'member_profile_id': female_member['_id'],
    'user_profile_id': female_member.get('user_profile_id', ObjectId()),
    'name': female_member.get('name', 'Female Member'),
    'gender': 'female',
    'birth_date': female_member.get('birth_date', '1990-05-15'),
    'birth_time': female_member.get('birth_time', '14:30'),
    'birth_place': female_member.get('birth_place', 'Chennai, India'),
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow(),
    'chart_data': create_chart_data()
}

# Create male astro data with slightly different chart
male_chart_data = create_chart_data()
# Modify some positions to make it different
male_chart_data['D1']['houses'][0]['planets'] = ['lagnam', 'sun']
male_chart_data['D1']['houses'][3]['planets'] = ['moon', 'mercury']
male_chart_data['D1']['houses'][8]['planets'] = ['jupiter', 'venus']

male_astro_data = {
    '_id': ObjectId(),
    'member_profile_id': male_member['_id'],
    'user_profile_id': male_member.get('user_profile_id', ObjectId()),
    'name': male_member.get('name', 'Male Member'),
    'gender': 'male',
    'birth_date': male_member.get('birth_date', '1988-10-20'),
    'birth_time': male_member.get('birth_time', '10:15'),
    'birth_place': male_member.get('birth_place', 'Chennai, India'),
    'created_at': datetime.utcnow(),
    'updated_at': datetime.utcnow(),
    'chart_data': male_chart_data
}

# Insert data into database
db.user_member_astro_profile_data.insert_one(female_astro_data)
db.user_member_astro_profile_data.insert_one(male_astro_data)

print(f"Created female astro data with ID: {female_astro_data['_id']}")
print(f"Created male astro data with ID: {male_astro_data['_id']}")
print(f"Female member ID: {female_member['_id']}")
print(f"Male member ID: {male_member['_id']}")
