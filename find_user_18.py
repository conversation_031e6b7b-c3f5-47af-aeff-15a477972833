#!/usr/bin/env python3
"""
Script to find and update member profile for user_profile_id 18
"""

from pymongo import MongoClient
from bson import ObjectId

# Connect to MongoDB
client = MongoClient('mongodb://localhost:27017/')
db = client.fortune_lens

# Find user with user_profile_id 18
user = db.user_profile.find_one({'user_profile_id': 18})
if user:
    print(f"Found user with user_profile_id 18:")
    print(f"  _id: {user['_id']}")
    print(f"  email: {user.get('email')}")
    
    # Find member profiles for this user
    members = list(db.member_profile.find({'user_profile_id': user['_id']}))
    print(f"\nFound {len(members)} member profiles with user_profile_id = _id")
    
    # Find member profiles for this user using user_profile_id
    members_by_profile_id = list(db.member_profile.find({'user_profile_id': 18}))
    print(f"Found {len(members_by_profile_id)} member profiles with user_profile_id = 18")
    
    # Find member profiles for this user using user_profile_id as string
    members_by_profile_id_str = list(db.member_profile.find({'user_profile_id': '18'}))
    print(f"Found {len(members_by_profile_id_str)} member profiles with user_profile_id = '18'")
    
    # Find all member profiles
    all_members = list(db.member_profile.find())
    print(f"\nTotal member profiles: {len(all_members)}")
    
    # Print all member profiles
    print("\nAll member profiles:")
    for i, member in enumerate(all_members):
        print(f"Member {i+1}:")
        print(f"  _id: {member['_id']}")
        print(f"  name: {member.get('name')}")
        print(f"  user_profile_id: {member.get('user_profile_id')} (type: {type(member.get('user_profile_id')).__name__})")
        print(f"  member_profile_id: {member.get('member_profile_id')}")
else:
    print("User with user_profile_id 18 not found")
