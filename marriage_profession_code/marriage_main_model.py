from data_loading import *
# from medical_profession import *
from marriage_main_functions import *

dataframes = read_excel_sheets(fileName)
import pandas as pd


def filter_by_profession(dataframe, profession):
    """
    Filters the dataframe to include only rows where the user_profession matches the specified profession.

    Parameters:
        dataframe (pd.DataFrame): The dataframe containing user data.
        profession (str): The profession to filter by.

    Returns:

        pd.DataFrame: A filtered dataframe with only the specified profession.
    """

    # print(dataframe['user_profession'])
    filtered_df = dataframe[dataframe['user_marital_status'] == profession]
    filtered_user_birth_chart = dataframes['user_birth_chart'][
        dataframes['user_birth_chart']['user_id'].isin(filtered_df['user_id'])]
    # print(filtered_user_birth_chart)
    # Update dataframes['user_birth_chart'] with the filtered data
    dataframes['user_birth_chart'] = filtered_user_birth_chart

    # Print the updated dataframe to verify
    # print(dataframes['user_birth_chart'])
    # print(filtered_df['user_id'])
    return dataframes['user_birth_chart']


user_input = input("Enter the user_id or 'ALL': ")

if user_input.upper() == "ALL":
    user_profession = input("Enter the user Marital Status: ")

    if user_profession.upper() != "ALL":
        # Retrieve all planet placements
        all_placements = filter_by_profession(dataframes['user_astro_Basic'], user_profession)
        # print(all_placements.columns)
        # print(all_placements)

    else:
        all_placements = dataframes['user_birth_chart']

    user_data = []
    over_all_data = []
    rule_all_details = []

    print("Lagna style")
    for index, row in all_placements.iterrows():
        user_id = row['user_id']
        rule_check = {}
        # ru_ch = []

        placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
        dob = get_dob(dataframes['user_astro_Basic'], user_id, "user_birthdate")
        marriage_date = get_dob(dataframes['user_astro_Basic'], user_id, "User_Data_Label_10 (Marriage Date)")
        # print(marriage_date)
        # print(dob)
        # print("Lagna style")
        ascendant = placements[0]['house_name']['house_name_1']
        house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
        house_planet_mapping = placements[0]['planet_house']
        file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
        df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
        re, mar_das_jup_asp_27, mar_das_jup_asp_ras, mar_das_ven_in_7, mar_das_ven_in_ras, result = m_rule_one(placements,
                                                                                                       dataframes, df,
                                                                                                       dob,
                                                                                                       marriage_date,
                                                                                                       user_id)
        user_data.append(re)
        # print(matching_periods)
    # print(user_data)

    df_ordered = pd.DataFrame(user_data)
    rule_points = {'Rule 1.1': 10, 'Rule 1.2': 5, 'Rule 1.3': 5}

    # Calculate total points for each user
    df_ordered['total_points'] = (
            df_ordered['Rule 1.1'] * rule_points['Rule 1.1'] +
            df_ordered['Rule 1.2'] * rule_points['Rule 1.2'] +
            df_ordered['Rule 1.3'] * rule_points['Rule 1.3']
    )
    # Specify the output Excel file path
    file_path = "Marriage_rule_prediction.xlsx"

    # Write only ordered_data to Excel
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df_ordered.to_excel(writer, sheet_name='Result_Data', index=False)


else:
    rule_check = {}
    user_id = int(user_input)
    placements = find_planet_placements(dataframes['user_birth_chart'], user_id)
    # print(placements[0]['planet_house'], placements[0]['house_name'])
    dob = get_dob(dataframes['user_astro_Basic'], user_id, "user_birthdate")
    marriage_date = get_dob(dataframes['user_astro_Basic'], user_id, "User_Data_Label_10 (Marriage Date)")
    print(marriage_date)
    # print(dob)
    print("Lagna style")
    ascendant = placements[0]['house_name']['house_name_1']
    house_names_in_order, ascendant_index = get_house_names_with_numbers(dataframes["house_name"], ascendant)
    house_planet_mapping = placements[0]['planet_house']
    file_path = "chart/" + str(user_id) + "_" + generate_random_string(5) + ".png"
    df = plot_south_indian_chart(file_path, house_names_in_order, ascendant_index, house_planet_mapping)
    re, mar_das_jup_asp_27, mar_das_jup_asp_ras, mar_das_ven_in_7, mar_das_ven_in_ras, result = m_rule_one(placements,
                                                                                                           dataframes,
                                                                                                           df, dob,
                                                                                                           marriage_date,
                                                                                                           user_id)
    print(re)
    # print(matching_periods)
    print("Matching Dasha Periods:")
    print("\nrule 1.1\n")
    print("Jupiter 5th Aspect Periods:")
    for period in mar_das_jup_asp_27[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in mar_das_jup_asp_27[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in mar_das_jup_asp_27[9]:
        print(period)

    print("\nJupiter Staying with 2th,7th house Periods:")
    for period in mar_das_jup_asp_27['stay']:
        print(period)

    print("\nrule 1.2\n")
    print("Jupiter 5th Aspect Periods:")
    for period in mar_das_jup_asp_ras[5]:
        print(period)

    print("\nJupiter 7th Aspect Periods:")
    for period in mar_das_jup_asp_ras[7]:
        print(period)

    print("\nJupiter 9th Aspect Periods:")
    for period in mar_das_jup_asp_ras[9]:
        print(period)

    print("\nJupiter Staying with rasi Periods:")
    for period in mar_das_jup_asp_ras['stay']:
        print(period)

    print("\nrule 1.3\n")

    print("\nVenus 7th Aspect Periods:")
    for period in mar_das_ven_in_7[7]:
        print(period)
    print("\nVenus Staying with 7th house Periods:")
    for period in mar_das_ven_in_7['stay']:
        print(period)

    print("\nrule 1.4\n")

    print("\nVenus 7th Aspect Periods:")
    for period in mar_das_ven_in_ras[7]:
        print(period)

    print("\nVenus Staying with rasi Periods:")
    for period in mar_das_ven_in_ras['stay']:
        print(period)
    # for date in matching_periods:
    #     print(date)
    print("\nCustomer Output")
    for r in result:
        print(r)
