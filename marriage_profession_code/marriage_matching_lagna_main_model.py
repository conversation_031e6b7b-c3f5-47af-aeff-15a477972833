from data_loading import *
# from medical_profession import *
from marriage_main_functions import *
from bulit_in_function import *

dataframes = read_excel_sheets(fileName)
import pandas as pd


def filter_by_user_gender(dataframe, user_id_1, user_id_2):
    """
    Retrieves and compares the genders of two users specified by their user IDs.

    Parameters:
        dataframe (pd.DataFrame): The dataframe containing user data. It should include columns 'user_id' and 'user_gender'.
        user_id_1 (int or str): The ID of the first user.
        user_id_2 (int or str): The ID of the second user.

    Returns:
        list or None: A list containing user IDs if genders match the specified criteria, otherwise None.
    """
    user_data_1 = dataframe[dataframe['user_id'] == user_id_1]
    user_data_2 = dataframe[dataframe['user_id'] == user_id_2]

    if user_data_1.empty or user_data_2.empty:
        print("One or both users are not found in the dataframe.")
        return None

    gender_1 = user_data_1['user_gender'].iloc[0]
    gender_2 = user_data_2['user_gender'].iloc[0]

    if gender_1 == "Female" and gender_2 == "Male":
        return [user_id_1, user_id_2]
    elif gender_1 == "Male" and gender_2 == "Female":
        return [user_id_2, user_id_1]
    else:
        print(f"Genders do not match the specified criteria.")
        return None


def get_moon_houses_cycle(planet_positions, house_names, planet, house_range):
    # Find the planet's position
    planet_position = None
    for key, value in planet_positions.items():
        if isinstance(value, str) and planet in value.split(', '):
            planet_position = int(key)
            break

    if planet_position is None:
        raise ValueError("Planet not found in provided positions.")

    # Calculate selected houses
    if house_range:
        selected_houses = [house_names[f'house_name_{(planet_position + hr - 1) % 12 or 12}'] for hr in house_range]
    else:
        selected_houses = [house_names[f'house_name_{planet_position}']]

    return selected_houses


def is_friend(house1, house2, planetary_relationships_df):
    """
    Check if the ruling planet of one house is a friend to the ruling planet of another house.

    :param house1: dict, e.g., {'house_name': 'MAGARAM', 'ruling_planet_name': 'SATURN'}
    :param house2: dict, e.g., {'house_name': 'RISHABAM', 'ruling_planet_name': 'VENUS'}
    :param planetary_relationships_df: pandas DataFrame with columns 'Planet', 'Friends', 'Enemies', 'Neutral'
    :return: bool, True if ruling planet of house1 is a friend to ruling planet of house2, otherwise False
    """
    planet1 = house1[0]['ruling_planet_name']
    planet2 = house2[0]['ruling_planet_name']

    # Get friends of planet1 and clean the data
    friends = planetary_relationships_df.loc[planetary_relationships_df['Planet'] == planet1, 'Friends'].values[0]
    friends_list = [planet.strip() for planet in friends.split(',')] if isinstance(friends, str) else []

    return planet2 in friends_list


def check_multiple_planets(planet_names, da_1, da_2, dataframe):
    for planet_name in planet_names:
        # Get the planet's Ucham and Neecham from the DataFrame
        planet_row = dataframe[dataframe["Planet"] == planet_name]
        if not planet_row.empty:
            ucham = planet_row["Exalt (Max. Power)"].values[0]
            neecham = planet_row["Debilitate (Weakest)"].values[0]

            # Check direct and reverse order
            order_match = ucham in da_1 and neecham in da_2
            reverse_order_match = neecham in da_1 and ucham in da_2

            # print(da_1_ucham_match, da_2_neecham_match)
            # If any planet fails the condition, return False
            if order_match or reverse_order_match:
                return False
            else:
                # If a planet is not found in the data, return False
                return True
        else:
            # If all planets pass the condition, return True
            return False


def has_combination(jathagam, combination):
    """Check if the given combination is present in the Jathagam."""
    for house, planets in jathagam.items():
        # Ensure the value is a string before processing
        if isinstance(planets, str):
            planet_list = [p.strip() for p in planets.split(',')]
            if all(planet in planet_list for planet in combination):
                return True
    return False


def validate_jathagam(bride_jathagam, groom_jathagam, combinations):
    bride = []
    groom = []
    for combination in combinations:
        bride_has = has_combination(bride_jathagam, combination)
        groom_has = has_combination(groom_jathagam, combination)
        # print(bride_has,groom_has)
        bride.append(bride_has)
        groom.append(groom_has)
    bride_count = bride.count(True)
    groom_count = groom.count(True)
    # print(bride, groom)
    if 0 < bride_count == groom_count > 0:
        return "Fail"
    else:
        return "Pass"


# Convert to DataFrame


# Function to check aspects between two planets
def check_jupiter_aspects_moon(df1, planet1, planet2, aspects):
    df1 = pd.DataFrame(list(df1.items()), columns=['house_number', 'planet_name'])

    # Helper function to get house positions for a specific planet
    def get_house_positions(df2, planet):
        return df2[df2['planet_name'].str.contains(planet, na=False)]['house_number'].values

    # Get positions of planet1 and planet2
    planet1_positions = get_house_positions(df1, planet1)
    planet2_positions = get_house_positions(df1, planet2)

    # Check if planet2 is in any of the specified aspects (5th or 9th) from planet1
    for pos1 in planet1_positions:
        for aspect in aspects:
            aspect_position = (pos1 + aspect - 1) % 12 or 12
            if any(pos2 == aspect_position for pos2 in planet2_positions):
                return True
    return False


def extract_venus_period(astrological_periods):
    today = datetime.now()
    venus_period = []

    for period, start, end in astrological_periods:
        start_date = datetime.strptime(start, '%Y-%m-%d %H:%M:%S %p')
        end_date = datetime.strptime(end, '%Y-%m-%d %H:%M:%S %p')

        if start_date <= today <= end_date:
            venus_period = period.split('-')[0]

    return venus_period


def planet_relationships(df_1, data, related_planet):
    planet_name = data['ruling_planet_name'].lower()
    related_planet = related_planet.lower()

    planet_info = df_1[df_1['Planet'].str.lower() == planet_name]

    if not planet_info.empty:
        friends = planet_info['Friends'].values[0]
        friends_list = [planet.strip().lower() for planet in friends.split(',')] if isinstance(friends, str) else []

        # friends = planet_info['Friends'].iloc[0].lower().split(', ')
        # print(friends_list)

        if related_planet in friends_list:
            return True
        else:
            return False
    else:
        return False


# Call the function
# result = check_jupiter_aspects_moon(df, "jupiter", "moon", [5, 9])

# Example inputs
# planet_positions = [None, 'lagnam', 'venus', 'sun, mercury', None, None, 'rahu', None, 'moon', 'mars, saturn', 'jupiter', None, 'ketu']
# house_names = ['MAGARAM', 'KUMBAM', 'MEENAM', 'MESHAM', 'RISHABAM', 'MIDUNAM', 'KADAGAM', 'SIMMAM', 'KANNI', 'THULAM', 'VIRICHIGAM', 'DHANUSU']
#
# moon_houses = get_moon_houses_in_range(planet_positions, house_names, "moon", [6, 8])
# print(moon_houses)

# Example usage:
# filtered_users = filter_by_user_gender(df, 1, 2)
# print(filtered_users)


user_input_1 = int(input("Enter the User ID for a female: "))

# Prompt the user to enter a user ID for a male
user_input_2 = int(input("Enter the User ID for a male: "))
rule_check = {}
# Retrieve filtered user IDs based on gender
all_placements = filter_by_user_gender(dataframes['user_astro_Basic'], user_input_1, user_input_2)

# If placements are found, proceed to find planet placements
if len(all_placements) == 2:
    placements_1 = find_planet_placements(dataframes['user_birth_chart'], all_placements[0])
    placements_2 = find_planet_placements(dataframes['user_birth_chart'], all_placements[1])

    # Print the placements
    print(placements_1[0]['planet_house'])
    print(placements_1[0]['house_name'])
    print(placements_2[0]['planet_house'])
    print(placements_2[0]['house_name'])
    moon_houses_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "venus",
                                          [6, 8])

    moon_houses_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "venus", [])
    print(moon_houses_1, moon_houses_2)
    is_any_equal = set(moon_houses_1) & set(moon_houses_2)
    rule_check['Rule 1.1'] = "Pass" if not is_any_equal else "Fail"
    # rule_check['Rule 1.1'] = bool(is_any_equal)
    print('Rule 1.1 : ', bool(is_any_equal))
    moon_houses_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "mars",
                                          [6, 8])

    moon_houses_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "mars", [])
    print(moon_houses_1, moon_houses_2)
    is_any_equal = set(moon_houses_1) & set(moon_houses_2)
    rule_check['Rule 1.2'] = "Pass" if not is_any_equal else "Fail"
    print('Rule 1.2 : ', bool(is_any_equal))
    moon_houses_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "jupiter",
                                          [6, 8])

    moon_houses_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "jupiter", [])
    print(moon_houses_1, moon_houses_2)
    is_any_equal = set(moon_houses_1) & set(moon_houses_2)
    rule_check['Rule 1.3'] = "Pass" if not is_any_equal else "Fail"
    print('Rule 1.3 : ', bool(is_any_equal))
    moon_houses_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "lagnam",
                                          [6, 8])

    moon_houses_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "lagnam", [])
    print(moon_houses_1, moon_houses_2)
    is_any_equal = set(moon_houses_1) & set(moon_houses_2)
    rule_check['Rule 1.4'] = "Pass" if not is_any_equal else "Fail"
    print('Rule 1.4 : ', bool(is_any_equal))

    us_1 = finding_ruling_planet_names([placements_1[0]['house_name']['house_name_1']],
                                       dataframes["house_name"])
    us_2 = finding_ruling_planet_names([placements_2[0]['house_name']['house_name_1']],
                                       dataframes["house_name"])
    # print(us_1, us_2)
    # print(dataframes['planets_friends_neutral_enemies'])
    rule_check['Rule 1.5'] = "Pass" if is_friend(us_1, us_2, dataframes['planets_friends_neutral_enemies']) else "Fail"
    print('Rule 1.5 : ', rule_check['Rule 1.5'])

    rasi_1 = find_rasi(placements_1[0]['planet_house'], placements_1[0]['house_name'])
    rasi_2 = find_rasi(placements_2[0]['planet_house'], placements_2[0]['house_name'])

    us_1 = finding_ruling_planet_names(rasi_1, dataframes["house_name"])
    us_2 = finding_ruling_planet_names(rasi_2, dataframes["house_name"])
    print(us_1, us_2)
    # print(dataframes['planets_friends_neutral_enemies'])
    rule_check['Rule 1.6'] = "Pass" if is_friend(us_1, us_2, dataframes['planets_friends_neutral_enemies']) else "Fail"
    print('Rule 1.6 : ', rule_check['Rule 1.6'])
    print("The Rasi where the Moon is located:")
    print(rasi_1, rasi_2)
    # finding_ruling_planet_names()
    rule_17 = {}
    moon_houses_jup_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "jupiter",
                                              [])
    moon_houses_jup_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "jupiter",
                                              [])
    rule_17['Rule 1.7.1'] = check_multiple_planets(["JUPITER"], moon_houses_jup_1,
                                                   moon_houses_jup_2,
                                                   dataframes['Planets_exalt_debilitate'])
    # print(moon_houses_jup_1, moon_houses_jup_2)
    moon_houses_ven_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "venus",
                                              [])
    moon_houses_ven_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "venus",
                                              [])
    rule_17['Rule 1.7.2'] = check_multiple_planets(["VENUS"], moon_houses_ven_1,
                                                   moon_houses_ven_1,
                                                   dataframes['Planets_exalt_debilitate'])
    moon_houses_mer_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "mercury",
                                              [])
    moon_houses_mer_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "mercury",
                                              [])
    rule_17['Rule 1.7.3'] = check_multiple_planets(["MERCURY"], moon_houses_mer_1,
                                                   moon_houses_mer_2,
                                                   dataframes['Planets_exalt_debilitate'])
    moon_houses_moon_1 = get_moon_houses_cycle(placements_1[0]['planet_house'], placements_1[0]['house_name'], "moon",
                                               [])
    moon_houses_moon_2 = get_moon_houses_cycle(placements_2[0]['planet_house'], placements_2[0]['house_name'], "moon",
                                               [])
    rule_17['Rule 1.7.4'] = check_multiple_planets(["MOON"], moon_houses_moon_1,
                                                   moon_houses_moon_2,
                                                   dataframes['Planets_exalt_debilitate'])
    print('Rule 1.7: ', rule_17)
    rule_check['Rule 1.7'] = "Fail" if not all(rule_17.values()) else "Pass"

    combinations = [('mars', 'rahu'), ('venus', 'rahu'), ('mars', 'ketu'), ('venus', 'ketu')]
    # print(placements_1[0]['planet_house'], placements_1[0]['house_name'])
    rule_check['Rule 1.8'] = validate_jathagam(placements_1[0]['planet_house'], placements_2[0]['planet_house'],
                                               combinations)
    print('Rule 1.8: ', rule_check['Rule 1.8'])
    # combinations_19 = [('jupiter', 'moon'), ('moon', 'jupiter')]
    # print(placements_1[0]['planet_house'], placements_1[0]['house_name'])
    # rule_19 = validate_jathagam(placements_1[0]['planet_house'], placements_2[0]['planet_house'],
    #                             combinations_19)
    rule_19_1 = check_jupiter_aspects_moon(placements_1[0]['planet_house'], "jupiter", "moon", [1, 5, 9])
    rule_19_2 = check_jupiter_aspects_moon(placements_2[0]['planet_house'], "jupiter", "moon", [1, 5, 9])
    print('Rule 1.9: ', rule_19_1, rule_19_2)
    rule_check['Rule 1.9'] = "Pass" if rule_19_1 or rule_19_2 else "Fail"
    dhasa_1 = placements_1[0]['dhasa']
    dh_1 = parse_to_tuples(dhasa_1['maha_dhasa_period'])
    # print(dh)
    rule_10_1 = extract_venus_period(dh_1)
    dhasa_2 = placements_2[0]['dhasa']
    dh_2 = parse_to_tuples(dhasa_2['maha_dhasa_period'])
    # print(dh)
    rule_10_2 = extract_venus_period(dh_2)
    print('Rule 1.10: ')
    print(rule_10_1, rule_10_2, us_1, us_2)
    # data = {'house_name': 'SIMMAM', 'ruling_planet_name': 'SUN'}
    rule_10_1 = planet_relationships(dataframes['planets_friends_neutral_enemies'], us_1[0], rule_10_1)
    rule_10_2 = planet_relationships(dataframes['planets_friends_neutral_enemies'], us_2[0], rule_10_2)
    print(rule_10_1, rule_10_2)
    rule_check['Rule 1.10'] = "Pass" if rule_10_1 or rule_10_2 else "Fail"
    # planet_info = dataframes['Planets_exalt_debilitate'][dataframes['Planets_exalt_debilitate']['Planet'] == rule_10_1]
    # print(planet_info)
    # print(placements_1[0]['house_name']['house_name_1'])
    # rule_check['Rule 1.10'] = "Pass" if rule_19_1 or rule_19_2 else "Fail"
    data_2 = {
        'Rule': ['1.1', '1.2', '1.3', '1.4', '1.5', '1.6', '1.7', '1.8', '1.9', '1.10'],
        'Points': [5, 5, 5, 10, 30, 30, 5, 5, 50, 5]
    }
    print(rule_check)
    # Creating DataFrame from data
    df = pd.DataFrame(data_2)
    df['Status'] = df['Rule'].apply(lambda x: rule_check.get(f'Rule {x}', ''))

    # Setting 'Points' to 0 for non-Pass cases
    df['Points'] = df.apply(lambda row: row['Points'] if row['Status'] == 'Pass' else 0, axis=1)

    # Creating a new workbook
    file_path = "Marriage_matching_rule_prediction.xlsx"
    with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='Result_Data', index=False)

    print(f"Excel file created successfully at {file_path}.")

else:
    print("Insufficient user placements found.")
