# Fortune Lens API Postman Collection

This repository contains a comprehensive Postman collection for testing all API endpoints in the Fortune Lens application.

## Contents

- `fortune_lens_postman_collection.json` - The Postman collection with all API requests
- `fortune_lens_postman_environment.json` - Environment variables for the collection
- `POSTMAN_USAGE_GUIDE.md` - Detailed guide on how to use the collection

## API Endpoints Covered

The collection includes requests for all API endpoints in the Fortune Lens application:

### Authentication
- Register a new user
- Verify OTP
- Login
- Refresh token
- Get current user information

### User Management
- Get all users
- Get user by ID
- Update user
- Delete user

### Member Profiles
- Create member profile
- Create member profile with charts
- Get all member profiles
- Get member profile by ID
- Get self profile
- Get member profile with charts
- Update member profile
- Delete member profile

### Charts
- Get available chart types
- Generate all 23 divisional charts
- Generate specific divisional chart

### Marriage Matching
- Analyze marriage compatibility
- Get marriage compatibility
- Analyze Rasi marriage compatibility
- Get Rasi marriage compatibility

### Marriage Date Prediction
- Predict marriage date
- Get marriage date prediction
- Get marriage date prediction by user

### Daily Panchanga
- Get daily panchanga
- Get monthly panchanga
- Get festival dates
- Get Tamil date

### Career Prediction
- Predict medical profession

## Features

- **Environment Variables**: Uses environment variables for base URL, tokens, and IDs
- **Automatic Token Handling**: Automatically extracts and saves tokens from login responses
- **Request Examples**: Includes example request bodies for all endpoints
- **Documentation**: Each request includes detailed documentation

## Getting Started

1. Import the collection and environment into Postman
2. Select the "Fortune Lens Environment" from the environment dropdown
3. Follow the testing workflow in the `POSTMAN_USAGE_GUIDE.md` file

## Testing Workflow

For a complete testing workflow, see the `POSTMAN_USAGE_GUIDE.md` file. The basic workflow is:

1. Register a user and login
2. Create member profiles
3. Generate charts
4. Test marriage matching and date prediction
5. Test panchanga functionality
6. Test career prediction

## Requirements

- Postman (latest version recommended)
- Fortune Lens API running (default: http://localhost:5000/api)

## Notes

- The collection is designed to work with the default configuration of the Fortune Lens API
- You may need to adjust the base URL in the environment variables if your API is running on a different host or port
- Some requests require specific IDs to be set in the environment variables
