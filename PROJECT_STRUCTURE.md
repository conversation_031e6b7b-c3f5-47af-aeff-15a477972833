# Fortune Lens Project Structure

This document outlines the structure of the Fortune Lens project, explaining the purpose of each directory and key files.

## Root Directory

```
fortune_lens/
├── .env.example                # Example environment variables file
├── .gitignore                  # Git ignore file
├── API_ENDPOINTS.md            # API documentation
├── Fortune_Lens_API.postman_collection.json  # Postman collection for API testing
├── Fortune_Lens_Environment.postman_environment.json  # Postman environment variables
├── POSTMAN_GUIDE.md            # Guide for using Postman with the API
├── PROJECT_STRUCTURE.md        # This file
├── README.md                   # Project overview and setup instructions
├── requirements.txt            # Python dependencies
├── run_app.py                  # Main entry point for running the application
├── fl_user_profile/            # User profile module (separate application)
└── fortune_lens/               # Main application module
```

## Main Application Module (fortune_lens/)

```
fortune_lens/
├── app/
│   ├── api/                    # API endpoints
│   │   ├── __init__.py         # API blueprint initialization
│   │   ├── charts.py           # Chart generation endpoints
│   │   ├── member_profiles.py  # Member profile endpoints
│   │   └── user_profile.py     # User profile endpoints
│   ├── errors/                 # Error handling
│   │   ├── __init__.py         # Error handling initialization
│   │   ├── exceptions.py       # Custom exception classes
│   │   └── handlers.py         # Error handlers
│   ├── schemas/                # Data validation schemas
│   │   ├── __init__.py         # Schema initialization
│   │   ├── auth_schemas.py     # Authentication schemas
│   │   ├── member_profile_schemas.py  # Member profile schemas
│   │   └── user_schemas.py     # User schemas
│   ├── services/               # Business logic
│   │   ├── __init__.py         # Service initialization
│   │   ├── astro_generator.py  # Astrological data generation
│   │   ├── auth_service.py     # Authentication service
│   │   ├── chart_constants.py  # Chart constants
│   │   ├── chart_service.py    # Chart generation service
│   │   ├── const.py            # Application constants
│   │   ├── horoscope/          # Astrological calculations
│   │   │   ├── chart/          # Chart calculations
│   │   │   │   ├── charts.py   # Divisional chart calculations
│   │   │   │   └── house.py    # House calculations
│   │   │   ├── dhasa/          # Dasha (planetary period) calculations
│   │   │   │   ├── graha.py    # Planetary calculations
│   │   │   │   └── vimsottari.py  # Vimsottari dasha calculations
│   │   │   └── yoga/           # Yoga calculations
│   │   ├── location.py         # Location service
│   │   ├── member_profile_service.py  # Member profile service
│   │   ├── otp_service.py      # OTP service
│   │   ├── panchanga/          # Panchanga (Hindu calendar) calculations
│   │   │   ├── drik.py         # Drik panchanga calculations
│   │   │   └── sunrise.py      # Sunrise calculations
│   │   ├── sequence_service.py # Sequence generation service
│   │   ├── user_service.py     # User service
│   │   └── utils.py            # Utility functions
│   ├── __init__.py             # Application factory
│   ├── config.py               # Configuration
│   ├── constants.py            # Application constants
│   └── extensions.py           # Flask extensions
└── run.py                      # Application entry point
```

## Key Files

### Configuration Files

- **app/config.py**: Contains application configuration settings, including database connection details, JWT settings, and other environment-specific configurations.
- **.env.example**: Example environment variables file that should be copied to `.env` and filled with actual values.

### Entry Points

- **run_app.py**: Main entry point for running the application.
- **fortune_lens/run.py**: Entry point for the main application module.

### API Endpoints

- **app/api/charts.py**: Endpoints for generating astrological charts.
- **app/api/member_profiles.py**: Endpoints for managing member profiles.
- **app/api/user_profile.py**: Endpoints for managing user profiles and authentication.

### Services

- **app/services/chart_service.py**: Service for generating astrological charts.
- **app/services/member_profile_service.py**: Service for managing member profiles.
- **app/services/auth_service.py**: Service for authentication.
- **app/services/otp_service.py**: Service for OTP generation and verification.
- **app/services/user_service.py**: Service for user management.

### Astrological Calculations

- **app/services/horoscope/chart/charts.py**: Divisional chart calculations.
- **app/services/horoscope/dhasa/graha.py**: Planetary calculations.
- **app/services/horoscope/dhasa/vimsottari.py**: Vimsottari dasha calculations.
- **app/services/panchanga/drik.py**: Drik panchanga calculations.

### Documentation

- **API_ENDPOINTS.md**: Comprehensive documentation of all API endpoints.
- **README.md**: Project overview and setup instructions.
- **PROJECT_STRUCTURE.md**: This file, explaining the project structure.
- **POSTMAN_GUIDE.md**: Documentation for using Postman with the API.

## Database Collections

The application uses the following MongoDB collections:

- **user_profile**: User account information
  - _id: ObjectId (Primary key)
  - email: String (Unique)
  - password: String (Hashed)
  - name: String
  - mobile: String (Optional)
  - created_at: DateTime
  - updated_at: DateTime

- **member_profile**: Member profiles with birth details
  - _id: ObjectId (Primary key)
  - user_id: ObjectId (Foreign key to user_profile)
  - member_name: String
  - member_gender: String
  - member_relation: String
  - user_birthdate: String
  - user_birthtime: String
  - user_birthplace: String
  - user_state: String
  - user_country: String
  - created_at: DateTime
  - updated_at: DateTime

- **user_member_astro_profile_data**: Astrological chart data for members
  - _id: ObjectId (Primary key)
  - user_id: ObjectId (Foreign key to user_profile)
  - member_id: ObjectId (Foreign key to member_profile)
  - chart_data: Object (Contains all 23 divisional charts)
  - created_at: DateTime
  - updated_at: DateTime

- **otps**: OTP verification codes
  - _id: ObjectId (Primary key)
  - email: String
  - otp: String
  - otp_type: String
  - expires_at: DateTime
  - created_at: DateTime

## Workflow

1. **User Registration and Authentication**:
   - User registers with email, password, name, and optional mobile number
   - OTP is sent to the user's email for verification
   - User verifies OTP and completes registration
   - User logs in with email and password to get access token

2. **Member Profile Management**:
   - User creates member profiles for themselves and others
   - Each profile includes birth details (date, time, place)
   - When a profile is created with charts, all 23 divisional charts are generated automatically

3. **Chart Generation**:
   - Charts can be generated directly using the chart endpoints
   - Charts can also be generated automatically when creating a member profile
   - All 23 divisional charts are stored in the database for each member profile

4. **Data Retrieval**:
   - User can retrieve their own profile information
   - User can retrieve all their member profiles
   - User can retrieve astrological chart data for any of their member profiles
